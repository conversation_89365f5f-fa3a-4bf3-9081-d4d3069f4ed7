//
//  SimpleAppleIntelligence.swift
//  VibeFinance - Simple Apple Intelligence Implementation
//
//  Created by MAGESH DHANASEKARAN on 12/26/24.
//  Minimal Apple Intelligence implementation that builds successfully
//

import Foundation
import SwiftUI

// MARK: - Simple Apple Intelligence Manager

@MainActor
class SimpleAppleIntelligenceManager: ObservableObject {
    static let shared = SimpleAppleIntelligenceManager()
    
    @Published var isProcessing = false
    @Published var lastResponse = ""
    
    private init() {}
    
    // MARK: - Basic AI Functions
    
    func analyzePortfolio(_ portfolio: Portfolio) async -> String {
        isProcessing = true
        
        // Simulate AI processing
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        let analysis = """
        Portfolio Analysis:
        • Total Value: $\(String(format: "%.2f", portfolio.totalValue))
        • Number of Positions: \(portfolio.positions.count)
        • Cash Available: $\(String(format: "%.2f", portfolio.cash))
        
        AI Insights:
        • Your portfolio shows good diversification
        • Consider rebalancing if any position exceeds 20%
        • Current allocation appears suitable for your risk profile
        """
        
        lastResponse = analysis
        isProcessing = false
        
        return analysis
    }
    
    func generateFinancialAdvice(for question: String) async -> String {
        isProcessing = true
        
        // Simulate AI processing
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        let advice: String
        
        if question.lowercased().contains("invest") {
            advice = "For investing, consider starting with diversified ETFs like VTI or VOO. These provide broad market exposure with low fees."
        } else if question.lowercased().contains("save") {
            advice = "For saving, aim to save at least 20% of your income. Start with an emergency fund covering 3-6 months of expenses."
        } else if question.lowercased().contains("budget") {
            advice = "For budgeting, try the 50/30/20 rule: 50% needs, 30% wants, 20% savings and debt repayment."
        } else if question.lowercased().contains("retirement") {
            advice = "For retirement planning, contribute to your 401(k) up to the company match, then consider a Roth IRA for additional savings."
        } else {
            advice = "That's a great financial question! Consider consulting with a financial advisor for personalized advice based on your specific situation."
        }
        
        lastResponse = advice
        isProcessing = false
        
        return advice
    }
    
    func summarizeNews(_ content: String) async -> String {
        isProcessing = true
        
        // Simulate AI processing
        try? await Task.sleep(nanoseconds: 300_000_000) // 0.3 seconds
        
        let summary = "Market Summary: " + String(content.prefix(200)) + "..."
        
        lastResponse = summary
        isProcessing = false
        
        return summary
    }
    
    func generateQuest(for userLevel: Int) async -> Quest {
        isProcessing = true
        
        // Simulate AI processing
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
        
        let questTitles = [
            "Learn About Compound Interest",
            "Understand Stock Market Basics",
            "Explore ETF Investing",
            "Create a Budget Plan",
            "Research Dividend Stocks"
        ]
        
        let questDescriptions = [
            "Discover how compound interest can grow your wealth over time",
            "Learn the fundamentals of how the stock market works",
            "Understand Exchange-Traded Funds and their benefits",
            "Create a personal budget to track your expenses",
            "Research companies that pay regular dividends"
        ]
        
        let randomIndex = Int.random(in: 0..<questTitles.count)
        
        let quest = Quest(
            title: questTitles[randomIndex],
            description: questDescriptions[randomIndex],
            category: .stocks,
            difficulty: userLevel < 3 ? .beginner : (userLevel < 6 ? .intermediate : .advanced),
            xpReward: 50 + (userLevel * 10),
            estimatedTime: 15 + (userLevel * 5),
            tasks: [
                QuestTask(
                    title: "Read Article",
                    description: "Read the provided educational content",
                    isCompleted: false
                ),
                QuestTask(
                    title: "Take Quiz",
                    description: "Complete the knowledge check quiz",
                    isCompleted: false
                )
            ]
        )
        
        isProcessing = false
        
        return quest
    }
}

// MARK: - Simple AI Chat View

struct SimpleAIChatView: View {
    @StateObject private var aiManager = SimpleAppleIntelligenceManager.shared
    @State private var inputText = ""
    @State private var messages: [ChatMessage] = []
    
    var body: some View {
        NavigationView {
            VStack {
                // Messages List
                ScrollView {
                    LazyVStack(alignment: .leading, spacing: 12) {
                        ForEach(messages) { message in
                            ChatBubbleView(message: message)
                        }
                        
                        if aiManager.isProcessing {
                            HStack {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("AI is thinking...")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding()
                        }
                    }
                    .padding()
                }
                
                // Input Area
                HStack {
                    TextField("Ask me about finance...", text: $inputText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    Button("Send") {
                        sendMessage()
                    }
                    .disabled(inputText.isEmpty || aiManager.isProcessing)
                }
                .padding()
            }
            .navigationTitle("AI Assistant")
        }
        .onAppear {
            addWelcomeMessage()
        }
    }
    
    private func addWelcomeMessage() {
        let welcomeMessage = ChatMessage(
            content: "Hello! I'm your AI financial assistant. I can help you with investing, budgeting, and financial planning. What would you like to know?",
            isFromUser: false,
            timestamp: Date()
        )
        messages.append(welcomeMessage)
    }
    
    private func sendMessage() {
        let userMessage = ChatMessage(
            content: inputText,
            isFromUser: true,
            timestamp: Date()
        )
        messages.append(userMessage)
        
        let question = inputText
        inputText = ""
        
        Task {
            let response = await aiManager.generateFinancialAdvice(for: question)
            
            let aiMessage = ChatMessage(
                content: response,
                isFromUser: false,
                timestamp: Date()
            )
            
            await MainActor.run {
                messages.append(aiMessage)
            }
        }
    }
}

// MARK: - Supporting Types

struct ChatMessage: Identifiable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp: Date
}

struct ChatBubbleView: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer()
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(message.content)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(message.isFromUser ? Color.blue : Color(.systemGray5))
                    )
                    .foregroundColor(message.isFromUser ? .white : .primary)
                
                Text(message.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 4)
            }
            
            if !message.isFromUser {
                Spacer()
            }
        }
    }
}

// MARK: - Simple Portfolio Analysis View

struct SimplePortfolioAnalysisView: View {
    @StateObject private var aiManager = SimpleAppleIntelligenceManager.shared
    @State private var analysis = ""
    @State private var portfolio = Portfolio.mockPortfolio()
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Portfolio Overview
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Portfolio Overview")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        HStack {
                            VStack(alignment: .leading) {
                                Text("Total Value")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text("$\(String(format: "%.2f", portfolio.totalValue))")
                                    .font(.title2)
                                    .fontWeight(.bold)
                            }
                            
                            Spacer()
                            
                            VStack(alignment: .trailing) {
                                Text("Positions")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                Text("\(portfolio.positions.count)")
                                    .font(.title2)
                                    .fontWeight(.bold)
                            }
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
                    
                    // AI Analysis
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("AI Analysis")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            Spacer()
                            
                            Button("Analyze") {
                                analyzePortfolio()
                            }
                            .disabled(aiManager.isProcessing)
                        }
                        
                        if aiManager.isProcessing {
                            HStack {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("Analyzing portfolio...")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        } else if !analysis.isEmpty {
                            Text(analysis)
                                .font(.body)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color(.systemBackground))
                                )
                        } else {
                            Text("Tap 'Analyze' to get AI insights about your portfolio")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
                }
                .padding()
            }
            .navigationTitle("Portfolio")
        }
    }
    
    private func analyzePortfolio() {
        Task {
            let result = await aiManager.analyzePortfolio(portfolio)
            await MainActor.run {
                analysis = result
            }
        }
    }
}

// MARK: - Mock Portfolio Extension

extension Portfolio {
    static func mockPortfolio() -> Portfolio {
        return Portfolio(
            positions: [
                Position(
                    symbol: "AAPL",
                    quantity: 10,
                    marketValue: 1500,
                    costBasis: 1200,
                    unrealizedPL: 300,
                    unrealizedPLPercent: 25.0
                ),
                Position(
                    symbol: "GOOGL",
                    quantity: 5,
                    marketValue: 1000,
                    costBasis: 900,
                    unrealizedPL: 100,
                    unrealizedPLPercent: 11.1
                )
            ],
            totalValue: 2500,
            cash: 500
        )
    }
}
