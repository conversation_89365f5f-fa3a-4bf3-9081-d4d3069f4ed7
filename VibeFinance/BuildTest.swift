//
//  BuildTest.swift
//  VibeFinance - Build Test
//
//  Created by MAGESH DHANASEKARAN on 12/26/24.
//  Simple test to verify build success
//

import Foundation
import SwiftUI

/// Simple build test
struct BuildTest {
    
    /// Test that core managers can be instantiated
    static func testManagerInstantiation() -> Bool {
        do {
            let _ = UserManager()
            let _ = AuthManager()
            let _ = FeedManager()
            let _ = QuestManager()
            let _ = SquadManager()
            let _ = SimulatorManager()
            let _ = CacheManager.shared
            let _ = ImageCacheManager.shared
            let _ = NetworkOptimizer.shared
            
            print("✅ All managers instantiated successfully")
            return true
        } catch {
            print("❌ Manager instantiation failed: \(error)")
            return false
        }
    }
    
    /// Test that data models can be created
    static func testDataModels() -> Bool {
        do {
            let _ = User(email: "<EMAIL>", username: "test", preferences: UserPreferences(), subscriptionStatus: .free)
            let _ = Portfolio(positions: [], totalValue: 0, cash: 0)
            let _ = Quest(title: "Test", description: "Test", category: .stocks, difficulty: .beginner, xpReward: 10, estimatedTime: 5, tasks: [])
            let _ = Squad(name: "Test", description: "Test", memberLimit: 10, isPrivate: false, createdBy: UUID())
            
            print("✅ All data models created successfully")
            return true
        } catch {
            print("❌ Data model creation failed: \(error)")
            return false
        }
    }
    
    /// Test that views can be instantiated
    static func testViewInstantiation() -> Bool {
        do {
            let _ = MainTabView()
            let _ = FeedView()
            let _ = QuestsView()
            let _ = SquadsView()
            let _ = ChatView()
            
            print("✅ All views instantiated successfully")
            return true
        } catch {
            print("❌ View instantiation failed: \(error)")
            return false
        }
    }
    
    /// Run all tests
    static func runAllTests() -> Bool {
        print("🔨 Running VibeFinance Build Tests...")
        
        let managersTest = testManagerInstantiation()
        let modelsTest = testDataModels()
        let viewsTest = testViewInstantiation()
        
        let allPassed = managersTest && modelsTest && viewsTest
        
        if allPassed {
            print("🎉 All build tests passed! Build is successful.")
        } else {
            print("❌ Some build tests failed.")
        }
        
        return allPassed
    }
}

/// Simple view to test build
struct BuildTestView: View {
    @State private var testResult: String = "Ready to test"
    @State private var isRunning = false
    
    var body: some View {
        VStack(spacing: 20) {
            Text("VibeFinance Build Test")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            Text(testResult)
                .font(.headline)
                .foregroundColor(testResult.contains("✅") ? .green : (testResult.contains("❌") ? .red : .blue))
            
            Button("Run Build Test") {
                runTest()
            }
            .disabled(isRunning)
            .font(.headline)
            .foregroundColor(.white)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isRunning ? Color.gray : Color.blue)
            )
        }
        .padding()
    }
    
    private func runTest() {
        isRunning = true
        testResult = "Running tests..."
        
        DispatchQueue.global().async {
            let success = BuildTest.runAllTests()
            
            DispatchQueue.main.async {
                testResult = success ? "✅ Build Successful!" : "❌ Build Failed"
                isRunning = false
            }
        }
    }
}

#Preview {
    BuildTestView()
}
