//
//  BuildTest.swift
//  VibeFinance - Build Verification
//
//  Created by MAGESH DHANASEKARAN on 12/26/24.
//  Simple build test to verify compilation
//

import Foundation
import SwiftUI

/// Simple build test to verify all components compile correctly
struct BuildTest {
    
    /// Test Apple Intelligence Manager initialization
    static func testAppleIntelligenceManager() {
        let manager = AppleIntelligenceManager.shared
        print("Apple Intelligence Manager initialized: \(manager.isProcessing)")
    }
    
    /// Test Portfolio Manager initialization
    static func testPortfolioManager() {
        let manager = PortfolioManager.shared
        print("Portfolio Manager initialized with \(manager.holdings.count) holdings")
    }
    
    /// Test basic data models
    static func testDataModels() {
        let portfolio = Portfolio.mockPortfolio()
        let context = UserContext.mockContext()
        print("Portfolio value: \(portfolio.totalValue)")
        print("User level: \(context.level)")
    }
    
    /// Test UI components compilation
    static func testUIComponents() {
        // This just tests that the views can be instantiated
        let _ = AppleIntelligenceChatView()
        let _ = AppleIntelligencePortfolioView()
        print("UI components compile successfully")
    }
    
    /// Run all build tests
    static func runAllTests() {
        print("🔨 Running VibeFinance Build Tests...")
        
        testAppleIntelligenceManager()
        testPortfolioManager()
        testDataModels()
        testUIComponents()
        
        print("✅ All build tests passed!")
    }
}

/// Build verification view for testing
struct BuildVerificationView: View {
    var body: some View {
        VStack(spacing: 20) {
            Text("VibeFinance 2.0")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            Text("Apple Intelligence Powered")
                .font(.title2)
                .foregroundColor(.purple)
            
            Button("Run Build Test") {
                BuildTest.runAllTests()
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding()
            .background(Color.blue)
            .cornerRadius(12)
        }
        .padding()
    }
}

#Preview {
    BuildVerificationView()
}
