//
//  RealTradingModels.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Real Portfolio
struct RealPortfolio: Codable, Identifiable {
    let id: UUID
    let accountId: String
    let totalValue: Double
    let buyingPower: Double
    let cash: Double
    let dayChange: Double
    let dayChangePercent: Double
    let lastUpdated: Date
    
    init(accountId: String, totalValue: Double, buyingPower: Double, cash: Double, dayChange: Double, dayChangePercent: Double) {
        self.id = UUID()
        self.accountId = accountId
        self.totalValue = totalValue
        self.buyingPower = buyingPower
        self.cash = cash
        self.dayChange = dayChange
        self.dayChangePercent = dayChangePercent
        self.lastUpdated = Date()
    }
}

// MARK: - Real Holding
struct RealHolding: Codable, Identifiable {
    let id: UUID
    let symbol: String
    let quantity: Double
    let marketValue: Double
    let costBasis: Double
    let unrealizedPL: Double
    let unrealizedPLPercent: Double
    let currentPrice: Double
    let lastUpdated: Date
    
    init(symbol: String, quantity: Double, marketValue: Double, costBasis: Double, unrealizedPL: Double, unrealizedPLPercent: Double, currentPrice: Double, lastUpdated: Date) {
        self.id = UUID()
        self.symbol = symbol
        self.quantity = quantity
        self.marketValue = marketValue
        self.costBasis = costBasis
        self.unrealizedPL = unrealizedPL
        self.unrealizedPLPercent = unrealizedPLPercent
        self.currentPrice = currentPrice
        self.lastUpdated = lastUpdated
    }
    
    var averagePrice: Double {
        guard quantity > 0 else { return 0 }
        return costBasis / quantity
    }
    
    var gainLossColor: String {
        return unrealizedPL >= 0 ? "green" : "red"
    }
}

// MARK: - Real Transaction
struct RealTransaction: Codable, Identifiable {
    let id: UUID
    let orderId: String
    let symbol: String
    let side: OrderSide
    let quantity: Double
    let price: Double
    let timestamp: Date
    let fees: Double

    init(orderId: String, symbol: String, side: OrderSide, quantity: Double, price: Double, timestamp: Date, fees: Double) {
        self.id = UUID()
        self.orderId = orderId
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        self.price = price
        self.timestamp = timestamp
        self.fees = fees
    }

    var totalValue: Double {
        return quantity * price
    }
    
    var netValue: Double {
        return totalValue + fees
    }
}

// MARK: - Real Order
struct RealOrder: Codable, Identifiable {
    let id: String
    let symbol: String
    let qty: Double
    let side: OrderSide
    let type: OrderType
    let timeInForce: TimeInForce
    let status: String
    let filledQty: Double
    let filledAvgPrice: Double?
    let limitPrice: Double?
    let createdAt: Date
    let updatedAt: Date
    let filledAt: Date?
    
    var isActive: Bool {
        return status == "new" || status == "partially_filled" || status == "pending_new"
    }
    
    var isFilled: Bool {
        return status == "filled"
    }
    
    var remainingQty: Double {
        return qty - filledQty
    }
}

// MARK: - Order Enums
enum OrderSide: String, Codable, CaseIterable {
    case buy = "buy"
    case sell = "sell"
    
    var displayName: String {
        switch self {
        case .buy: return "Buy"
        case .sell: return "Sell"
        }
    }
    
    var color: String {
        switch self {
        case .buy: return "green"
        case .sell: return "red"
        }
    }
}

enum OrderType: String, Codable, CaseIterable {
    case market = "market"
    case limit = "limit"
    case stop = "stop"
    case stopLimit = "stop_limit"
    
    var displayName: String {
        switch self {
        case .market: return "Market"
        case .limit: return "Limit"
        case .stop: return "Stop"
        case .stopLimit: return "Stop Limit"
        }
    }
}

enum TimeInForce: String, Codable, CaseIterable {
    case gtc = "gtc" // Good Till Cancelled
    case day = "day" // Day Order
    case ioc = "ioc" // Immediate or Cancel
    case fok = "fok" // Fill or Kill
    
    var displayName: String {
        switch self {
        case .gtc: return "Good Till Cancelled"
        case .day: return "Day Order"
        case .ioc: return "Immediate or Cancel"
        case .fok: return "Fill or Kill"
        }
    }
}

enum OrderStatus: String, Codable, CaseIterable {
    case all = "all"
    case open = "open"
    case closed = "closed"
    case filled = "filled"
    case cancelled = "cancelled"
}

// MARK: - Order Validation
enum OrderValidationResult {
    case valid
    case warning(String)
    case invalid(String)
    
    var isValid: Bool {
        switch self {
        case .valid, .warning: return true
        case .invalid: return false
        }
    }
    
    var message: String? {
        switch self {
        case .valid: return nil
        case .warning(let message), .invalid(let message): return message
        }
    }
}

// MARK: - Trading Restrictions
enum TradingRestriction {
    case restricted(String)
    case afterHours(String)
    case pdtRestricted(String)
    
    var message: String {
        switch self {
        case .restricted(let msg), .afterHours(let msg), .pdtRestricted(let msg):
            return msg
        }
    }
}

// MARK: - Portfolio Performance
struct RealPortfolioPerformance {
    let totalValue: Double
    let totalGainLoss: Double
    let totalGainLossPercent: Double
    let dayChange: Double
    let dayChangePercent: Double
    let bestPerformer: RealHolding?
    let worstPerformer: RealHolding?
    let cashBalance: Double
    let buyingPower: Double
    
    var isPositive: Bool {
        return totalGainLoss >= 0
    }
    
    var isDayPositive: Bool {
        return dayChange >= 0
    }
}

// MARK: - Alpaca API Models
struct AlpacaAccountInfo {
    let id: String
    let accountNumber: String
    let status: String
    let currency: String
    let buyingPower: Double
    let cash: Double
    let portfolioValue: Double
    let equity: Double
    let lastEquity: Double
    let dayChange: Double
    let dayChangePercent: Double
    let patternDayTrader: Bool
    let tradingBlocked: Bool
    let transfersBlocked: Bool
}

struct AlpacaPosition {
    let symbol: String
    let qty: Double
    let marketValue: Double
    let costBasis: Double
    let unrealizedPL: Double
    let unrealizedPLPercent: Double
    let currentPrice: Double
    let side: String
}

struct AlpacaQuote {
    let symbol: String
    let bidPrice: Double
    let bidSize: Int
    let askPrice: Double
    let askSize: Int
    let timestamp: Date
}

// MARK: - Alpaca API Response Models
struct AlpacaAccountResponse: Codable {
    let id: String
    let accountNumber: String
    let status: String
    let currency: String
    let buyingPower: String
    let cash: String
    let portfolioValue: String
    let equity: String
    let lastEquity: String
    let patternDayTrader: Bool
    let tradingBlocked: Bool
    let transfersBlocked: Bool
    
    private enum CodingKeys: String, CodingKey {
        case id, status, currency, cash, equity
        case accountNumber = "account_number"
        case buyingPower = "buying_power"
        case portfolioValue = "portfolio_value"
        case lastEquity = "last_equity"
        case patternDayTrader = "pattern_day_trader"
        case tradingBlocked = "trading_blocked"
        case transfersBlocked = "transfers_blocked"
    }
}

struct AlpacaPositionResponse: Codable {
    let symbol: String
    let qty: String
    let marketValue: String
    let costBasis: String
    let unrealizedPL: String
    let unrealizedPLPercent: String
    let currentPrice: String
    let side: String
    
    private enum CodingKeys: String, CodingKey {
        case symbol, qty, side
        case marketValue = "market_value"
        case costBasis = "cost_basis"
        case unrealizedPL = "unrealized_pl"
        case unrealizedPLPercent = "unrealized_plpc"
        case currentPrice = "current_price"
    }
}

struct AlpacaOrderRequest: Codable {
    let symbol: String
    let qty: Double
    let side: String
    let type: String
    let timeInForce: String
    
    private enum CodingKeys: String, CodingKey {
        case symbol, qty, side, type
        case timeInForce = "time_in_force"
    }
}

struct AlpacaLimitOrderRequest: Codable {
    let symbol: String
    let qty: Double
    let side: String
    let type: String
    let timeInForce: String
    let limitPrice: Double
    
    private enum CodingKeys: String, CodingKey {
        case symbol, qty, side, type
        case timeInForce = "time_in_force"
        case limitPrice = "limit_price"
    }
}

struct AlpacaOrderResponse: Codable {
    let id: String
    let symbol: String
    let qty: String
    let side: String
    let type: String
    let timeInForce: String
    let status: String
    let filledQty: String?
    let filledAvgPrice: String?
    let limitPrice: String?
    let createdAt: String
    let updatedAt: String
    let filledAt: String?
    
    private enum CodingKeys: String, CodingKey {
        case id, symbol, qty, side, type, status
        case timeInForce = "time_in_force"
        case filledQty = "filled_qty"
        case filledAvgPrice = "filled_avg_price"
        case limitPrice = "limit_price"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case filledAt = "filled_at"
    }
}

struct AlpacaQuoteResponse: Codable {
    let quote: AlpacaQuoteData
}

struct AlpacaQuoteData: Codable {
    let bidPrice: Double
    let bidSize: Int
    let askPrice: Double
    let askSize: Int
    let timestamp: String
    
    private enum CodingKeys: String, CodingKey {
        case timestamp
        case bidPrice = "bp"
        case bidSize = "bs"
        case askPrice = "ap"
        case askSize = "as"
    }
}
