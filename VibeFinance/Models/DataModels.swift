//
//  DataModels.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Stock Data Models
struct StockPrice: Codable {
    let symbol: String
    let price: Double
    let change: Double
    let changePercent: Double
    let timestamp: Date
}

struct StockQuote: Codable {
    let symbol: String
    let open: Double
    let high: Double
    let low: Double
    let close: Double
    let volume: Int
    let timestamp: Date
}

struct MarketIndex: Codable {
    let name: String
    let symbol: String
    let value: Double
    let change: Double
    let changePercent: Double
}

struct MarketData: Codable {
    let indices: [MarketIndex]
    let topGainers: [StockPrice]
    let topLosers: [StockPrice]
    let mostActive: [StockPrice]
}

// MARK: - Portfolio Models
struct Portfolio: Codable {
    let positions: [Position]
    let totalValue: Double
    let cash: Double
}

struct Position: Codable {
    let symbol: String
    let quantity: Double
    let marketValue: Double
    let costBasis: Double
    let unrealizedPL: Double
    let unrealizedPLPercent: Double
}

struct PortfolioAnalysis: Codable {
    let totalValue: Double
    let totalReturn: Double
    let riskScore: Double
    let diversificationScore: Double
    let recommendations: [String]
}

// MARK: - Investment Models
struct InvestmentRecommendation: Codable {
    let symbol: String
    let name: String
    let recommendationType: String
    let confidence: Double
    let expectedReturn: Double
    let riskLevel: String
    let reasoning: String
}

struct RiskProfile: Codable {
    let riskTolerance: String // "conservative", "moderate", "aggressive"
    let investmentHorizon: String // "short", "medium", "long"
    let age: Int
    let income: Double
    let investmentGoals: [String]
}

// MARK: - Trading Models
struct TradingAccount: Codable {
    let accountId: String
    let buyingPower: Double
    let cash: Double
    let portfolioValue: Double
    let dayTradeCount: Int
}

struct TradeOrder: Codable {
    let symbol: String
    let quantity: Double
    let side: String // "buy" or "sell"
    let type: String // "market", "limit", etc.
    let timeInForce: String
    let limitPrice: Double?
}

struct OrderResponse: Codable {
    let orderId: String
    let status: String
    let filledQuantity: Double
    let averagePrice: Double?
}

struct Order: Codable {
    let orderId: String
    let symbol: String
    let quantity: Double
    let side: String
    let status: String
    let orderType: String
    let createdAt: Date
}

// MARK: - AI Models
struct AIResponse: Codable {
    let text: String
    let confidence: Double
    let suggestions: [String]?
}

// MARK: - Service Protocols
protocol StockDataService {
    func getStockPrice(symbol: String) async throws -> StockPrice
    func getStockQuote(symbol: String) async throws -> StockQuote
    func getMarketData() async throws -> MarketData
}

protocol AIService {
    func generateResponse(prompt: String) async throws -> AIResponse
    func analyzePortfolio(portfolio: Portfolio) async throws -> PortfolioAnalysis
    func getInvestmentRecommendations(riskProfile: RiskProfile) async throws -> [InvestmentRecommendation]
}

protocol TradingService {
    func getAccount() async throws -> TradingAccount
    func placeOrder(order: TradeOrder) async throws -> OrderResponse
    func getPositions() async throws -> [Position]
    func getOrders() async throws -> [Order]
}
