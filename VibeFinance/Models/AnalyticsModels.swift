//
//  AnalyticsModels.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Portfolio Analytics
struct PortfolioAnalytics: Codable {
    let totalValue: Double
    let totalInvested: Double
    let totalGainLoss: Double
    let totalReturn: Double
    let volatility: Double
    let sharpeRatio: Double
    let diversificationScore: Double
    let winRate: Double
    let averageHoldingPeriod: Double
    let numberOfPositions: Int
    let largestPosition: RealHolding?
    let cashPercentage: Double
    
    var riskLevel: RiskLevel {
        if volatility < 10 { return .low }
        else if volatility < 20 { return .medium }
        else { return .high }
    }
    
    var performanceGrade: String {
        if totalReturn >= 15 { return "A+" }
        else if totalReturn >= 10 { return "A" }
        else if totalReturn >= 5 { return "B" }
        else if totalReturn >= 0 { return "C" }
        else { return "D" }
    }
}

enum RiskLevel: String, Codable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    
    var color: String {
        switch self {
        case .low: return "green"
        case .medium: return "orange"
        case .high: return "red"
        }
    }
    
    var description: String {
        switch self {
        case .low: return "Conservative portfolio with stable returns"
        case .medium: return "Balanced risk with moderate growth potential"
        case .high: return "Aggressive portfolio with high growth potential"
        }
    }
}

// MARK: - Performance History
struct PerformanceDataPoint: Codable, Identifiable {
    let id = UUID()
    let date: Date
    let portfolioValue: Double
    let dayChange: Double
    let dayChangePercent: Double
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Risk Metrics
struct RiskMetrics: Codable {
    let valueAtRisk95: Double // 95% confidence VaR
    let valueAtRisk99: Double // 99% confidence VaR
    let beta: Double // Portfolio beta vs market
    let maxDrawdown: Double // Maximum historical loss
    let concentrationRisk: Double // Largest position percentage
    let riskScore: Double // Overall risk score 0-100
    
    var riskLevel: RiskLevel {
        if riskScore < 30 { return .low }
        else if riskScore < 60 { return .medium }
        else { return .high }
    }
    
    var riskDescription: String {
        switch riskLevel {
        case .low:
            return "Your portfolio has low risk with stable performance"
        case .medium:
            return "Your portfolio has moderate risk with balanced growth"
        case .high:
            return "Your portfolio has high risk with aggressive growth potential"
        }
    }
}

// MARK: - Market Sentiment
struct MarketSentiment: Codable {
    let overallSentiment: Double // 0-1 scale (0=bearish, 1=bullish)
    let sentimentBySymbol: [String: Double]
    let sentimentTrend: String // "Improving", "Declining", "Stable"
    let newsItems: [SentimentNewsItem]
    
    var sentimentLabel: String {
        if overallSentiment >= 0.7 { return "Bullish" }
        else if overallSentiment >= 0.3 { return "Neutral" }
        else { return "Bearish" }
    }
    
    var sentimentColor: String {
        if overallSentiment >= 0.7 { return "green" }
        else if overallSentiment >= 0.3 { return "orange" }
        else { return "red" }
    }
    
    var sentimentDescription: String {
        switch sentimentLabel {
        case "Bullish":
            return "Market sentiment is positive for your holdings"
        case "Neutral":
            return "Market sentiment is mixed for your holdings"
        case "Bearish":
            return "Market sentiment is negative for your holdings"
        default:
            return "Unable to determine market sentiment"
        }
    }
}

struct SentimentNewsItem: Codable, Identifiable {
    let id = UUID()
    let symbol: String
    let headline: String
    let sentiment: Double
    let timestamp: Date
    let source: String
    
    var sentimentLabel: String {
        if sentiment >= 0.7 { return "Positive" }
        else if sentiment >= 0.3 { return "Neutral" }
        else { return "Negative" }
    }
    
    var sentimentColor: String {
        if sentiment >= 0.7 { return "green" }
        else if sentiment >= 0.3 { return "orange" }
        else { return "red" }
    }
}

// MARK: - Benchmark Comparison
struct BenchmarkComparison: Codable {
    let portfolioReturn: Double
    let sp500Return: Double
    let nasdaqReturn: Double
    let totalMarketReturn: Double
    let outperformance: Double // vs S&P 500
    let alpha: Double // Risk-adjusted outperformance
    let trackingError: Double // Volatility of outperformance
    
    var isOutperforming: Bool {
        return outperformance > 0
    }
    
    var outperformanceDescription: String {
        if outperformance > 5 {
            return "Significantly outperforming the market"
        } else if outperformance > 0 {
            return "Outperforming the market"
        } else if outperformance > -5 {
            return "Slightly underperforming the market"
        } else {
            return "Significantly underperforming the market"
        }
    }
    
    var bestBenchmark: String {
        let benchmarks = [
            ("S&P 500", sp500Return),
            ("NASDAQ", nasdaqReturn),
            ("Total Market", totalMarketReturn)
        ]
        
        let closest = benchmarks.min { abs($0.1 - portfolioReturn) < abs($1.1 - portfolioReturn) }
        return closest?.0 ?? "S&P 500"
    }
}

// MARK: - Sector Analysis
struct SectorAnalysis: Codable {
    let allocations: [SectorAllocation]
    let mostAllocatedSector: String
    let leastAllocatedSector: String
    let diversificationRecommendation: String
    
    var isDiversified: Bool {
        let maxAllocation = allocations.max { $0.percentage < $1.percentage }?.percentage ?? 0
        return maxAllocation < 40 && allocations.count >= 3
    }
    
    var diversificationScore: Double {
        let maxAllocation = allocations.max { $0.percentage < $1.percentage }?.percentage ?? 0
        let sectorCount = allocations.count
        
        // Score based on max allocation and number of sectors
        let allocationScore = max(0, 100 - maxAllocation * 2) // Penalty for concentration
        let diversityScore = min(100, Double(sectorCount) * 20) // Bonus for more sectors
        
        return (allocationScore + diversityScore) / 2
    }
}

struct SectorAllocation: Codable, Identifiable {
    let id = UUID()
    let sector: String
    let value: Double
    let percentage: Double
    let performance: Double // Sector performance %
    
    var performanceColor: String {
        return performance >= 0 ? "green" : "red"
    }
    
    var sectorIcon: String {
        switch sector {
        case "Technology": return "laptopcomputer"
        case "Healthcare": return "cross.case"
        case "Financial Services": return "banknote"
        case "Consumer Discretionary": return "cart"
        case "Consumer Staples": return "basket"
        case "Energy": return "bolt"
        case "Utilities": return "lightbulb"
        case "Real Estate": return "house"
        case "Materials": return "hammer"
        case "Industrials": return "gear"
        case "Communication": return "antenna.radiowaves.left.and.right"
        default: return "questionmark.circle"
        }
    }
}

// MARK: - Chart Data Models
struct ChartDataPoint: Identifiable {
    let id = UUID()
    let x: Double
    let y: Double
    let date: Date?
    let label: String?
    
    init(x: Double, y: Double, date: Date? = nil, label: String? = nil) {
        self.x = x
        self.y = y
        self.date = date
        self.label = label
    }
}

struct ChartData {
    let points: [ChartDataPoint]
    let title: String
    let xAxisLabel: String
    let yAxisLabel: String
    let color: String
    let chartType: ChartType
    
    var minY: Double {
        points.map { $0.y }.min() ?? 0
    }
    
    var maxY: Double {
        points.map { $0.y }.max() ?? 100
    }
    
    var range: Double {
        maxY - minY
    }
}

enum ChartType {
    case line
    case bar
    case pie
    case area
}

// MARK: - Time Period
enum TimePeriod: String, CaseIterable {
    case oneDay = "1D"
    case oneWeek = "1W"
    case oneMonth = "1M"
    case threeMonths = "3M"
    case sixMonths = "6M"
    case oneYear = "1Y"
    case all = "All"
    
    var displayName: String {
        switch self {
        case .oneDay: return "1 Day"
        case .oneWeek: return "1 Week"
        case .oneMonth: return "1 Month"
        case .threeMonths: return "3 Months"
        case .sixMonths: return "6 Months"
        case .oneYear: return "1 Year"
        case .all: return "All Time"
        }
    }
    
    var days: Int {
        switch self {
        case .oneDay: return 1
        case .oneWeek: return 7
        case .oneMonth: return 30
        case .threeMonths: return 90
        case .sixMonths: return 180
        case .oneYear: return 365
        case .all: return 3650 // 10 years
        }
    }
}

// MARK: - Supporting Models
struct CompletedTrade: Codable {
    let symbol: String
    let buyDate: Date
    let sellDate: Date
    let buyPrice: Double
    let sellPrice: Double
    let quantity: Double
    let profit: Double
    let profitPercent: Double
    
    var holdingPeriodDays: Double {
        return sellDate.timeIntervalSince(buyDate) / (24 * 60 * 60)
    }
    
    var isWinning: Bool {
        return profit > 0
    }
}

struct NewsItem: Codable {
    let title: String
    let source: String
    let publishedAt: Date
    let url: String
    let summary: String?
}

// MARK: - Analytics Insights
struct AnalyticsInsight: Identifiable {
    let id = UUID()
    let type: InsightType
    let title: String
    let description: String
    let actionable: Bool
    let priority: InsightPriority
    let icon: String
    
    var priorityColor: String {
        switch priority {
        case .high: return "red"
        case .medium: return "orange"
        case .low: return "blue"
        }
    }
}

enum InsightType {
    case risk
    case performance
    case diversification
    case opportunity
    case warning
}

enum InsightPriority {
    case high
    case medium
    case low
}
