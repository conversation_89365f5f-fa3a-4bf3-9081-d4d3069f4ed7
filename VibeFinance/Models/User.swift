//
//  User.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - User Model
struct User: Codable, Identifiable {
    let id: UUID
    var email: String
    var username: String
    var preferences: UserPreferences
    var subscriptionStatus: SubscriptionTier
    var xp: Int
    var level: Int
    var createdAt: Date
    var updatedAt: Date

    init(id: UUID = UUID(), email: String, username: String, preferences: UserPreferences = UserPreferences()) {
        self.id = id
        self.email = email
        self.username = username
        self.preferences = preferences
        self.subscriptionStatus = .free
        self.xp = 0
        self.level = 1
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    init(id: UUID, email: String, username: String, preferences: UserPreferences, subscriptionStatus: SubscriptionTier, xp: Int, level: Int, createdAt: Date, updatedAt: Date) {
        self.id = id
        self.email = email
        self.username = username
        self.preferences = preferences
        self.subscriptionStatus = subscriptionStatus
        self.xp = xp
        self.level = level
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

// MARK: - User Preferences
struct UserPreferences: Codable {
    var interests: [String]
    var goals: [String]
    var riskTolerance: RiskTolerance
    var preferredLanguage: String
    var notificationsEnabled: Bool
    var dailyQuestTime: String // "06:00"

    init() {
        self.interests = []
        self.goals = []
        self.riskTolerance = .moderate
        self.preferredLanguage = "en"
        self.notificationsEnabled = true
        self.dailyQuestTime = "06:00"
    }
}

// MARK: - Risk Tolerance
enum RiskTolerance: String, CaseIterable, Codable {
    case conservative = "conservative"
    case moderate = "moderate"
    case aggressive = "aggressive"

    var displayName: String {
        switch self {
        case .conservative: return "Conservative 🛡️"
        case .moderate: return "Moderate ⚖️"
        case .aggressive: return "Aggressive 🚀"
        }
    }

    var description: String {
        switch self {
        case .conservative: return "Play it safe with low-risk investments"
        case .moderate: return "Balance risk and reward"
        case .aggressive: return "Go big or go home with high-risk, high-reward"
        }
    }
}

// MARK: - Subscription Tier
enum SubscriptionTier: String, CaseIterable, Codable {
    case free = "free"
    case basic = "basic"
    case pro = "pro"

    var displayName: String {
        switch self {
        case .free: return "Free"
        case .basic: return "Basic"
        case .pro: return "Pro"
        }
    }

    var price: String {
        switch self {
        case .free: return "$0"
        case .basic: return "$9.99/month"
        case .pro: return "$19.99/month"
        }
    }

    var features: [String] {
        switch self {
        case .free:
            return [
                "3 feed posts per day",
                "Basic quests",
                "Limited chat access"
            ]
        case .basic:
            return [
                "Unlimited feed access",
                "All quests",
                "Full chat access",
                "Basic analytics"
            ]
        case .pro:
            return [
                "Everything in Basic",
                "Community squads",
                "Investment simulator",
                "Priority chat support",
                "Advanced analytics",
                "Real investment integration"
            ]
        }
    }
}

// MARK: - User Extensions
extension User {
    var levelProgress: Double {
        let xpForCurrentLevel = level * 100
        let xpForNextLevel = (level + 1) * 100
        let progressXP = xp - xpForCurrentLevel
        let totalXPNeeded = xpForNextLevel - xpForCurrentLevel
        return Double(progressXP) / Double(totalXPNeeded)
    }

    var nextLevelXP: Int {
        return (level + 1) * 100
    }

    var currentLevelXP: Int {
        return level * 100
    }

    mutating func addXP(_ amount: Int) {
        xp += amount
        updateLevel()
        updatedAt = Date()
    }

    private mutating func updateLevel() {
        let newLevel = max(1, xp / 100 + 1)
        if newLevel > level {
            level = newLevel
            // Could trigger level up celebration here
        }
    }

    var canAccessFeature: (FeatureAccess) -> Bool {
        return { feature in
            switch feature {
            case .unlimitedFeed:
                return subscriptionStatus != .free
            case .allQuests:
                return subscriptionStatus != .free
            case .fullChat:
                return subscriptionStatus != .free
            case .squads:
                return subscriptionStatus == .pro
            case .simulator:
                return subscriptionStatus == .pro
            case .priorityChat:
                return subscriptionStatus == .pro
            case .realInvestments:
                return subscriptionStatus == .pro
            }
        }
    }
}

// MARK: - Feature Access
enum FeatureAccess {
    case unlimitedFeed
    case allQuests
    case fullChat
    case squads
    case simulator
    case priorityChat
    case realInvestments
}
