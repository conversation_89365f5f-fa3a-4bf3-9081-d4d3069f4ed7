//
//  User.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import SwiftUI

// MARK: - User Model
struct User: Codable, Identifiable {
    let id: UUID
    var email: String
    var username: String
    var preferences: UserPreferences
    var subscriptionStatus: SubscriptionTier
    var xp: Int
    var level: Int
    var createdAt: Date
    var updatedAt: Date

    init(id: UUID = UUID(), email: String, username: String, preferences: UserPreferences = UserPreferences()) {
        self.id = id
        self.email = email
        self.username = username
        self.preferences = preferences
        self.subscriptionStatus = .free
        self.xp = 0
        self.level = 1
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    init(id: UUID, email: String, username: String, preferences: UserPreferences, subscriptionStatus: SubscriptionTier, xp: Int, level: Int, createdAt: Date, updatedAt: Date) {
        self.id = id
        self.email = email
        self.username = username
        self.preferences = preferences
        self.subscriptionStatus = subscriptionStatus
        self.xp = xp
        self.level = level
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }
}

// MARK: - User Preferences
struct UserPreferences: Codable {
    var interests: [String]
    var goals: [String]
    var riskTolerance: RiskTolerance
    var preferredLanguage: String
    var notificationsEnabled: Bool
    var dailyQuestTime: String // "06:00"
    var experienceLevel: ExperienceLevel

    init() {
        self.interests = []
        self.goals = []
        self.riskTolerance = .moderate
        self.preferredLanguage = "en"
        self.notificationsEnabled = true
        self.dailyQuestTime = "06:00"
        self.experienceLevel = .beginner
    }
}

// MARK: - Risk Tolerance
enum RiskTolerance: String, CaseIterable, Codable {
    case conservative = "conservative"
    case moderate = "moderate"
    case aggressive = "aggressive"

    var displayName: String {
        switch self {
        case .conservative: return "Conservative 🛡️"
        case .moderate: return "Moderate ⚖️"
        case .aggressive: return "Aggressive 🚀"
        }
    }

    var description: String {
        switch self {
        case .conservative: return "Play it safe with low-risk investments"
        case .moderate: return "Balance risk and reward"
        case .aggressive: return "Go big or go home with high-risk, high-reward"
        }
    }
}

// MARK: - Experience Level
enum ExperienceLevel: String, CaseIterable, Codable {
    case beginner = "beginner"
    case intermediate = "intermediate"
    case advanced = "advanced"

    var displayName: String {
        switch self {
        case .beginner: return "Beginner 🌱"
        case .intermediate: return "Intermediate 📈"
        case .advanced: return "Advanced 🎯"
        }
    }

    var description: String {
        switch self {
        case .beginner: return "New to investing and finance"
        case .intermediate: return "Some experience with basic investments"
        case .advanced: return "Experienced with complex financial strategies"
        }
    }

    var icon: String {
        switch self {
        case .beginner: return "leaf.fill"
        case .intermediate: return "chart.line.uptrend.xyaxis"
        case .advanced: return "crown.fill"
        }
    }
}

// MARK: - Subscription Tier
enum SubscriptionTier: String, CaseIterable, Codable {
    case free = "free"
    case basic = "basic"
    case pro = "pro"

    var displayName: String {
        switch self {
        case .free: return "Free"
        case .basic: return "Basic"
        case .pro: return "Pro"
        }
    }

    var price: String {
        switch self {
        case .free: return "$0"
        case .basic: return "$9.99/month"
        case .pro: return "$19.99/month"
        }
    }

    var features: [String] {
        switch self {
        case .free:
            return [
                "3 feed posts per day",
                "Basic quests",
                "Limited chat access"
            ]
        case .basic:
            return [
                "Unlimited feed access",
                "All quests",
                "Full chat access",
                "Basic analytics"
            ]
        case .pro:
            return [
                "Everything in Basic",
                "Community squads",
                "Investment simulator",
                "Priority chat support",
                "Advanced analytics",
                "Real investment integration"
            ]
        }
    }
}

// MARK: - User Stats
struct UserStats {
    let level: Int
    let xp: Int
    let questsCompleted: Int
    let daysActive: Int
    let achievementsUnlocked: Int

    var xpToNextLevel: Int {
        let nextLevelXP = calculateXPForLevel(level + 1)
        return nextLevelXP - xp
    }

    var progressToNextLevel: Double {
        let currentLevelXP = calculateXPForLevel(level)
        let nextLevelXP = calculateXPForLevel(level + 1)
        let progressXP = xp - currentLevelXP
        let totalXPNeeded = nextLevelXP - currentLevelXP

        return Double(progressXP) / Double(totalXPNeeded)
    }

    private func calculateXPForLevel(_ level: Int) -> Int {
        let xpThresholds = [0, 100, 250, 500, 1000, 2000, 4000, 8000, 16000, 32000, 64000]
        return level < xpThresholds.count ? xpThresholds[level] : xpThresholds.last! * (level - xpThresholds.count + 2)
    }
}

// MARK: - Achievement System
struct Achievement: Codable, Identifiable {
    let id: String
    let title: String
    let description: String
    let icon: String
    let category: AchievementCategory
    let xpReward: Int
    let rarity: AchievementRarity
    let requirements: AchievementRequirements

    init(id: String, title: String, description: String, icon: String, category: AchievementCategory, xpReward: Int, rarity: AchievementRarity, requirements: AchievementRequirements) {
        self.id = id
        self.title = title
        self.description = description
        self.icon = icon
        self.category = category
        self.xpReward = xpReward
        self.rarity = rarity
        self.requirements = requirements
    }
}

enum AchievementCategory: String, Codable, CaseIterable {
    case learning = "learning"
    case investing = "investing"
    case social = "social"
    case milestones = "milestones"

    var displayName: String {
        switch self {
        case .learning: return "Learning"
        case .investing: return "Investing"
        case .social: return "Social"
        case .milestones: return "Milestones"
        }
    }

    var color: Color {
        switch self {
        case .learning: return .blue
        case .investing: return .green
        case .social: return .purple
        case .milestones: return .gold
        }
    }
}

enum AchievementRarity: String, Codable {
    case common = "common"
    case rare = "rare"
    case epic = "epic"
    case legendary = "legendary"

    var displayName: String {
        switch self {
        case .common: return "Common"
        case .rare: return "Rare"
        case .epic: return "Epic"
        case .legendary: return "Legendary"
        }
    }

    var color: Color {
        switch self {
        case .common: return .gray
        case .rare: return .blue
        case .epic: return .purple
        case .legendary: return .gold
        }
    }
}

struct AchievementRequirements: Codable {
    let type: RequirementType
    let target: Int
    let description: String
}

enum RequirementType: String, Codable {
    case level = "level"
    case questsCompleted = "quests_completed"
    case xpEarned = "xp_earned"
    case daysActive = "days_active"
    case squadsJoined = "squads_joined"
    case investmentsMade = "investments_made"
}

struct UserAchievement: Codable {
    let userID: UUID
    let achievementID: String
    let unlockedAt: Date
}

// MARK: - Quest Progress Tracking
struct QuestCompletion: Codable, Identifiable {
    let id = UUID()
    let questID: UUID
    let userID: UUID
    let completedAt: Date
    let xpEarned: Int
    let rewardsClaimed: Bool

    init(questID: UUID, userID: UUID, completedAt: Date, xpEarned: Int, rewardsClaimed: Bool = false) {
        self.questID = questID
        self.userID = userID
        self.completedAt = completedAt
        self.xpEarned = xpEarned
        self.rewardsClaimed = rewardsClaimed
    }
}

struct QuestTaskProgress: Codable {
    let questID: UUID
    let taskID: UUID
    let userAnswer: String
    let completedAt: Date
}

// MARK: - User Extensions
extension User {
    var levelProgress: Double {
        let xpForCurrentLevel = level * 100
        let xpForNextLevel = (level + 1) * 100
        let progressXP = xp - xpForCurrentLevel
        let totalXPNeeded = xpForNextLevel - xpForCurrentLevel
        return Double(progressXP) / Double(totalXPNeeded)
    }

    var nextLevelXP: Int {
        return (level + 1) * 100
    }

    var currentLevelXP: Int {
        return level * 100
    }

    mutating func addXP(_ amount: Int) {
        xp += amount
        updateLevel()
        updatedAt = Date()
    }

    private mutating func updateLevel() {
        let newLevel = max(1, xp / 100 + 1)
        if newLevel > level {
            level = newLevel
            // Could trigger level up celebration here
        }
    }

    var canAccessFeature: (FeatureAccess) -> Bool {
        return { feature in
            switch feature {
            case .unlimitedFeed:
                return subscriptionStatus != .free
            case .allQuests:
                return subscriptionStatus != .free
            case .fullChat:
                return subscriptionStatus != .free
            case .squads:
                return subscriptionStatus == .pro
            case .simulator:
                return subscriptionStatus == .pro
            case .priorityChat:
                return subscriptionStatus == .pro
            case .realInvestments:
                return subscriptionStatus == .pro
            case .advancedAnalytics:
                return subscriptionStatus == .pro
            }
        }
    }
}

// MARK: - Feature Access
enum FeatureAccess {
    case unlimitedFeed
    case allQuests
    case fullChat
    case squads
    case simulator
    case priorityChat
    case realInvestments
    case advancedAnalytics
}

// MARK: - Color Extensions
extension Color {
    static let gold = Color(red: 1.0, green: 0.84, blue: 0.0)
}
