//
//  SimulatorManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

@MainActor
class SimulatorManager: ObservableObject {
    @Published var portfolio: VirtualPortfolio?
    @Published var watchlists: [Watchlist] = []
    @Published var marketData: [String: StockPrice] = [:]
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private let marketService = MarketService.shared
    private var cancellables = Set<AnyCancellable>()
    private var priceUpdateTimer: Timer?
    
    // MARK: - Portfolio Management
    
    func loadPortfolio(for userID: UUID) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let portfolios = try await supabaseService.fetch(
                VirtualPortfolio.self,
                from: "virtual_portfolios",
                where: "userID=eq.\(userID.uuidString)"
            )
            
            if let existingPortfolio = portfolios.first {
                await MainActor.run {
                    self.portfolio = existingPortfolio
                    self.isLoading = false
                }
                
                // Update prices for existing positions
                await updatePortfolioPrices()
            } else {
                // Create new portfolio with $10K starting balance
                let newPortfolio = VirtualPortfolio(userID: userID, startingBalance: 10000.0)
                try await supabaseService.create(newPortfolio, table: "virtual_portfolios")
                
                await MainActor.run {
                    self.portfolio = newPortfolio
                    self.isLoading = false
                }
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    func loadWatchlists(for userID: UUID) async {
        do {
            let lists = try await supabaseService.fetch(
                Watchlist.self,
                from: "watchlists",
                where: "userID=eq.\(userID.uuidString)"
            )
            
            await MainActor.run {
                self.watchlists = lists
            }
            
        } catch {
            print("Failed to load watchlists: \(error)")
        }
    }
    
    // MARK: - Trading Operations
    
    func buyStock(symbol: String, shares: Double) async {
        guard var currentPortfolio = portfolio else { return }
        
        do {
            let quote = try await marketService.getQuote(symbol: symbol)
            let totalCost = shares * quote.price
            
            guard currentPortfolio.balance >= totalCost else {
                errorMessage = "Insufficient funds"
                return
            }
            
            let position = VirtualPosition(
                symbol: symbol,
                name: "\(quote.symbol) Inc.",
                shares: shares,
                averageCost: quote.price,
                currentPrice: quote.price
            )
            
            currentPortfolio.addPosition(position)
            
            let transaction = VirtualTransaction(
                portfolioID: currentPortfolio.id,
                type: .buy,
                symbol: symbol,
                name: "\(quote.symbol) Inc.",
                shares: shares,
                price: quote.price
            )
            
            currentPortfolio.transactions.append(transaction)
            
            try await supabaseService.update(
                currentPortfolio,
                in: "virtual_portfolios",
                where: "id=eq.\(currentPortfolio.id.uuidString)"
            )
            
            await MainActor.run {
                self.portfolio = currentPortfolio
                self.marketData[symbol] = quote
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    func sellStock(symbol: String, shares: Double) async {
        guard var currentPortfolio = portfolio else { return }
        guard let position = currentPortfolio.positions.first(where: { $0.symbol == symbol }),
              position.shares >= shares else {
            errorMessage = "Insufficient shares"
            return
        }
        
        do {
            let quote = try await marketService.getQuote(symbol: symbol)
            
            currentPortfolio.removePosition(symbol, shares: shares)
            
            let transaction = VirtualTransaction(
                portfolioID: currentPortfolio.id,
                type: .sell,
                symbol: symbol,
                name: "\(quote.symbol) Inc.",
                shares: shares,
                price: quote.price
            )
            
            currentPortfolio.transactions.append(transaction)
            
            try await supabaseService.update(
                currentPortfolio,
                in: "virtual_portfolios",
                where: "id=eq.\(currentPortfolio.id.uuidString)"
            )
            
            await MainActor.run {
                self.portfolio = currentPortfolio
                self.marketData[symbol] = quote
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    // MARK: - Market Data
    
    func searchStocks(query: String) async -> [StockPrice] {
        do {
            return try await marketService.searchStocks(query: query)
        } catch {
            print("Failed to search stocks: \(error)")
            return []
        }
    }
    
    func getQuote(symbol: String) async -> StockPrice? {
        do {
            let quote = try await marketService.getQuote(symbol: symbol)
            await MainActor.run {
                self.marketData[symbol] = quote
            }
            return quote
        } catch {
            print("Failed to get quote for \(symbol): \(error)")
            return nil
        }
    }
    
    func updatePortfolioPrices() async {
        guard var currentPortfolio = portfolio else { return }
        
        var updatedMarketData: [StockPrice] = []
        
        for position in currentPortfolio.positions {
            if let quote = await getQuote(symbol: position.symbol) {
                updatedMarketData.append(quote)
            }
        }
        
        currentPortfolio.updatePrices(updatedMarketData)
        
        do {
            try await supabaseService.update(
                currentPortfolio,
                in: "virtual_portfolios",
                where: "id=eq.\(currentPortfolio.id.uuidString)"
            )
            
            await MainActor.run {
                self.portfolio = currentPortfolio
            }
            
        } catch {
            print("Failed to update portfolio prices: \(error)")
        }
    }
    
    func startPriceUpdates() {
        priceUpdateTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { _ in
            Task {
                await self.updatePortfolioPrices()
            }
        }
    }
    
    func stopPriceUpdates() {
        priceUpdateTimer?.invalidate()
        priceUpdateTimer = nil
    }
    
    // MARK: - Watchlist Management
    
    func createWatchlist(name: String, userID: UUID) async {
        let watchlist = Watchlist(userID: userID, name: name)
        
        do {
            try await supabaseService.create(watchlist, table: "watchlists")
            
            await MainActor.run {
                self.watchlists.append(watchlist)
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    func addToWatchlist(_ watchlistID: UUID, symbol: String) async {
        guard let index = watchlists.firstIndex(where: { $0.id == watchlistID }) else { return }
        
        var watchlist = watchlists[index]
        if !watchlist.symbols.contains(symbol) {
            watchlist.symbols.append(symbol)
            watchlist.updatedAt = Date()
            
            do {
                try await supabaseService.update(
                    watchlist,
                    in: "watchlists",
                    where: "id=eq.\(watchlistID.uuidString)"
                )
                
                await MainActor.run {
                    self.watchlists[index] = watchlist
                }
                
            } catch {
                await MainActor.run {
                    self.errorMessage = error.localizedDescription
                }
            }
        }
    }
    
    func removeFromWatchlist(_ watchlistID: UUID, symbol: String) async {
        guard let index = watchlists.firstIndex(where: { $0.id == watchlistID }) else { return }
        
        var watchlist = watchlists[index]
        watchlist.symbols.removeAll { $0 == symbol }
        watchlist.updatedAt = Date()
        
        do {
            try await supabaseService.update(
                watchlist,
                in: "watchlists",
                where: "id=eq.\(watchlistID.uuidString)"
            )
            
            await MainActor.run {
                self.watchlists[index] = watchlist
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    // MARK: - Portfolio Analytics
    
    func getPortfolioPerformance() -> PortfolioPerformance? {
        guard let portfolio = portfolio else { return nil }
        
        let totalReturn = portfolio.totalReturn
        let totalReturnPercent = portfolio.totalReturnPercent
        
        // Calculate day change (simplified)
        let dayChange = portfolio.positions.reduce(0) { $0 + $1.dayChange * $1.shares }
        let dayChangePercent = portfolio.totalInvested > 0 ? (dayChange / portfolio.totalInvested) * 100 : 0
        
        let bestPerformer = portfolio.positions.max { $0.totalReturnPercent < $1.totalReturnPercent }?.symbol
        let worstPerformer = portfolio.positions.min { $0.totalReturnPercent < $1.totalReturnPercent }?.symbol
        
        return PortfolioPerformance(
            totalReturn: totalReturn,
            totalReturnPercent: totalReturnPercent,
            dayChange: dayChange,
            dayChangePercent: dayChangePercent,
            bestPerformer: bestPerformer,
            worstPerformer: worstPerformer,
            diversificationScore: portfolio.diversificationScore
        )
    }
    
    // MARK: - Helper Methods
    
    func clearError() {
        errorMessage = nil
    }
    
    func resetPortfolio() async {
        guard let currentPortfolio = portfolio else { return }
        
        var resetPortfolio = currentPortfolio
        resetPortfolio.balance = 10000.0
        resetPortfolio.totalValue = 10000.0
        resetPortfolio.totalInvested = 0.0
        resetPortfolio.positions.removeAll()
        resetPortfolio.transactions.removeAll()
        resetPortfolio.updatedAt = Date()
        
        do {
            try await supabaseService.update(
                resetPortfolio,
                in: "virtual_portfolios",
                where: "id=eq.\(currentPortfolio.id.uuidString)"
            )
            
            await MainActor.run {
                self.portfolio = resetPortfolio
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
}
