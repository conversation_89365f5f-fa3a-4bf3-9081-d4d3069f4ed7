//
//  QuestManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

@MainActor
class QuestManager: ObservableObject {
    @Published var availableQuests: [Quest] = []
    @Published var userProgress: [QuestProgress] = []
    @Published var dailyQuest: Quest?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private let aiService = GeminiAIService()
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Quest Loading

    func loadAvailableQuests() async {
        isLoading = true
        errorMessage = nil

        do {
            // Load quests from database
            let quests: [Quest] = try await supabaseService.fetch(
                Quest.self,
                from: "quests",
                where: "is_active=eq.true"
            )

            await MainActor.run {
                self.availableQuests = quests
                self.isLoading = false
            }

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }

    func generateDailyQuest(for user: User) async {
        do {
            let quest = try await aiService.generateDailyQuest(
                userLevel: user.level,
                preferences: user.preferences.interests
            )

            // Save to database
            try await supabaseService.create(quest, table: "quests")

            await MainActor.run {
                self.dailyQuest = quest
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to generate daily quest: \(error.localizedDescription)"
            }
        }
    }

    // MARK: - Quest Progress

    func saveTaskProgress(questID: UUID, taskID: UUID, answer: String) async {
        do {
            let progress = QuestTaskProgress(
                questID: questID,
                taskID: taskID,
                userAnswer: answer,
                completedAt: Date()
            )

            try await supabaseService.create(progress, table: "quest_task_progress")

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to save progress: \(error.localizedDescription)"
            }
        }
    }

    func completeQuest(_ questID: UUID) async {
        do {
            let completion = QuestCompletion(
                questID: questID,
                userID: getCurrentUserID(),
                completedAt: Date(),
                xpEarned: getQuestXP(questID)
            )

            try await supabaseService.create(completion, table: "quest_completions")

            // Update local state
            if let index = availableQuests.firstIndex(where: { $0.id == questID }) {
                await MainActor.run {
                    self.availableQuests[index].isCompleted = true
                }
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to complete quest: \(error.localizedDescription)"
            }
        }
    }

    func claimQuestRewards(_ questID: UUID) async {
        do {
            // Mark rewards as claimed
            try await supabaseService.update(
                ["rewards_claimed": true],
                table: "quest_completions",
                where: "quest_id=eq.\(questID.uuidString)"
            )

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to claim rewards: \(error.localizedDescription)"
            }
        }
    }

    // MARK: - Helper Methods

    private func getCurrentUserID() -> UUID {
        // Get from auth manager or user manager
        return UUID() // Placeholder
    }

    private func getQuestXP(_ questID: UUID) -> Int {
        return availableQuests.first { $0.id == questID }?.xpReward ?? 0
    }
    
    func loadQuests(for user: User) async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Load available quests
            let quests = try await supabaseService.fetch(
                Quest.self,
                from: "quests",
                where: "unlockLevel<=\(user.level)&order=createdAt.desc"
            )
            
            // Load user progress
            let progress = try await supabaseService.fetch(
                QuestProgress.self,
                from: "quest_progress",
                where: "userID=eq.\(user.id.uuidString)"
            )
            
            await MainActor.run {
                self.availableQuests = quests
                self.userProgress = progress
                self.isLoading = false
            }
            
            // Generate daily quest if needed
            await generateDailyQuestIfNeeded(for: user)
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    func generateDailyQuestIfNeeded(for user: User) async {
        // Check if user already has a daily quest for today
        let today = Calendar.current.startOfDay(for: Date())
        let existingDailyQuest = availableQuests.first { quest in
            quest.isDaily && Calendar.current.isDate(quest.createdAt, inSameDayAs: today)
        }
        
        if existingDailyQuest == nil {
            await generateDailyQuest(for: user)
        } else {
            dailyQuest = existingDailyQuest
        }
    }
    
    // MARK: - Quest Progress
    
    func startQuest(_ quest: Quest, for userID: UUID) async {
        let progress = QuestProgress(userID: userID, questID: quest.id)
        
        do {
            try await supabaseService.create(progress, table: "quest_progress")
            
            await MainActor.run {
                self.userProgress.append(progress)
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    func completeTask(_ taskID: UUID, in questID: UUID, for userID: UUID, xpReward: Int) async {
        guard let progressIndex = userProgress.firstIndex(where: { 
            $0.questID == questID && $0.userID == userID 
        }) else { return }
        
        var progress = userProgress[progressIndex]
        progress.completeTask(taskID, xpReward: xpReward)
        
        // Check if quest is complete
        if let quest = availableQuests.first(where: { $0.id == questID }) {
            if progress.completedTasks.count >= quest.tasks.count {
                progress.complete(totalXP: quest.xpReward)
            }
        }
        
        do {
            try await supabaseService.update(
                progress,
                in: "quest_progress",
                where: "id=eq.\(progress.id.uuidString)"
            )
            
            await MainActor.run {
                self.userProgress[progressIndex] = progress
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    func completeQuest(_ questID: UUID, for userID: UUID) async -> Int {
        guard let quest = availableQuests.first(where: { $0.id == questID }),
              let progressIndex = userProgress.firstIndex(where: { 
                  $0.questID == questID && $0.userID == userID 
              }) else { return 0 }
        
        var progress = userProgress[progressIndex]
        progress.complete(totalXP: quest.xpReward)
        
        do {
            try await supabaseService.update(
                progress,
                in: "quest_progress",
                where: "id=eq.\(progress.id.uuidString)"
            )
            
            await MainActor.run {
                self.userProgress[progressIndex] = progress
            }
            
            return quest.xpReward
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
            return 0
        }
    }
    
    // MARK: - Quest Queries
    
    func getQuestProgress(for questID: UUID, userID: UUID) -> QuestProgress? {
        return userProgress.first { $0.questID == questID && $0.userID == userID }
    }
    
    func getAvailableQuests(for user: User) -> [Quest] {
        return availableQuests.filter { quest in
            quest.unlockLevel <= user.level && !isQuestCompleted(quest.id, for: user.id)
        }
    }
    
    func getInProgressQuests(for userID: UUID) -> [Quest] {
        let inProgressIDs = userProgress
            .filter { $0.userID == userID && $0.status == .inProgress }
            .map { $0.questID }
        
        return availableQuests.filter { inProgressIDs.contains($0.id) }
    }
    
    func getCompletedQuests(for userID: UUID) -> [Quest] {
        let completedIDs = userProgress
            .filter { $0.userID == userID && $0.status == .completed }
            .map { $0.questID }
        
        return availableQuests.filter { completedIDs.contains($0.id) }
    }
    
    func getQuestsByCategory(_ category: QuestCategory) -> [Quest] {
        return availableQuests.filter { $0.category == category }
    }
    
    func getQuestsByDifficulty(_ difficulty: DifficultyLevel) -> [Quest] {
        return availableQuests.filter { $0.difficulty == difficulty }
    }
    
    private func isQuestCompleted(_ questID: UUID, for userID: UUID) -> Bool {
        return userProgress.contains { 
            $0.questID == questID && $0.userID == userID && $0.status == .completed 
        }
    }
    
    private func getCompletedQuestIDs() -> [UUID] {
        return userProgress
            .filter { $0.status == .completed }
            .map { $0.questID }
    }
    
    // MARK: - Statistics
    
    func getUserQuestStats(for userID: UUID) -> QuestStats {
        let completed = getCompletedQuests(for: userID)
        let inProgress = getInProgressQuests(for: userID)
        let totalXP = userProgress
            .filter { $0.userID == userID && $0.status == .completed }
            .reduce(0) { $0 + $1.xpEarned }
        
        let categoryCounts = Dictionary(grouping: completed) { $0.category }
            .mapValues { $0.count }
        
        return QuestStats(
            totalCompleted: completed.count,
            totalInProgress: inProgress.count,
            totalXPEarned: totalXP,
            categoryCounts: categoryCounts,
            currentStreak: calculateCurrentStreak(for: userID),
            longestStreak: calculateLongestStreak(for: userID)
        )
    }
    
    private func calculateCurrentStreak(for userID: UUID) -> Int {
        let completedQuests = getCompletedQuests(for: userID)
            .sorted { $0.createdAt > $1.createdAt }
        
        var streak = 0
        var currentDate = Calendar.current.startOfDay(for: Date())
        
        for quest in completedQuests {
            let questDate = Calendar.current.startOfDay(for: quest.createdAt)
            if Calendar.current.isDate(questDate, inSameDayAs: currentDate) {
                streak += 1
                currentDate = Calendar.current.date(byAdding: .day, value: -1, to: currentDate) ?? currentDate
            } else {
                break
            }
        }
        
        return streak
    }
    
    private func calculateLongestStreak(for userID: UUID) -> Int {
        // Implementation for longest streak calculation
        return 0 // Placeholder
    }
    
    // MARK: - Helper Methods
    
    func clearError() {
        errorMessage = nil
    }
    
    func refreshQuests(for user: User) async {
        availableQuests.removeAll()
        userProgress.removeAll()
        await loadQuests(for: user)
    }
}

// MARK: - Quest Stats
struct QuestStats {
    let totalCompleted: Int
    let totalInProgress: Int
    let totalXPEarned: Int
    let categoryCounts: [QuestCategory: Int]
    let currentStreak: Int
    let longestStreak: Int
    
    var completionRate: Double {
        let total = totalCompleted + totalInProgress
        guard total > 0 else { return 0.0 }
        return Double(totalCompleted) / Double(total)
    }
    
    var favoriteCategory: QuestCategory? {
        return categoryCounts.max { $0.value < $1.value }?.key
    }
}
