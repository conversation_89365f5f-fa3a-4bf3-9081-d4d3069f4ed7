//
//  UserManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

@MainActor
class UserManager: ObservableObject {
    @Published var user: User?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - User Profile Methods
    
    func loadUser(id: UUID) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let loadedUser = try await supabaseService.getUser(id: id)
            
            await MainActor.run {
                self.user = loadedUser
                self.isLoading = false
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    func updateUser(_ updatedUser: User) async {
        isLoading = true
        errorMessage = nil
        
        do {
            try await supabaseService.updateUser(updatedUser)
            
            await MainActor.run {
                self.user = updatedUser
                self.isLoading = false
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    func updatePreferences(_ preferences: UserPreferences) async {
        guard var currentUser = user else { return }
        
        currentUser.preferences = preferences
        currentUser.updatedAt = Date()
        
        await updateUser(currentUser)
    }
    
    func updateSubscription(_ tier: SubscriptionTier) async {
        guard var currentUser = user else { return }
        
        currentUser.subscriptionStatus = tier
        currentUser.updatedAt = Date()
        
        await updateUser(currentUser)
    }
    
    func addXP(_ amount: Int) async {
        guard var currentUser = user else { return }
        
        let oldLevel = currentUser.level
        currentUser.addXP(amount)
        
        await updateUser(currentUser)
        
        // Check for level up
        if currentUser.level > oldLevel {
            // Could trigger level up celebration or notification here
            print("🎉 Level up! Now level \(currentUser.level)")
        }
    }
    
    // MARK: - Preferences Management
    
    func addInterest(_ interest: String) async {
        guard var currentUser = user else { return }
        
        if !currentUser.preferences.interests.contains(interest) {
            currentUser.preferences.interests.append(interest)
            await updatePreferences(currentUser.preferences)
        }
    }
    
    func removeInterest(_ interest: String) async {
        guard var currentUser = user else { return }
        
        currentUser.preferences.interests.removeAll { $0 == interest }
        await updatePreferences(currentUser.preferences)
    }
    
    func addGoal(_ goal: String) async {
        guard var currentUser = user else { return }
        
        if !currentUser.preferences.goals.contains(goal) {
            currentUser.preferences.goals.append(goal)
            await updatePreferences(currentUser.preferences)
        }
    }
    
    func removeGoal(_ goal: String) async {
        guard var currentUser = user else { return }
        
        currentUser.preferences.goals.removeAll { $0 == goal }
        await updatePreferences(currentUser.preferences)
    }
    
    func updateRiskTolerance(_ riskTolerance: RiskTolerance) async {
        guard var currentUser = user else { return }
        
        currentUser.preferences.riskTolerance = riskTolerance
        await updatePreferences(currentUser.preferences)
    }
    
    func updateNotificationSettings(_ enabled: Bool) async {
        guard var currentUser = user else { return }
        
        currentUser.preferences.notificationsEnabled = enabled
        await updatePreferences(currentUser.preferences)
    }
    
    func updateDailyQuestTime(_ time: String) async {
        guard var currentUser = user else { return }
        
        currentUser.preferences.dailyQuestTime = time
        await updatePreferences(currentUser.preferences)
    }
    
    // MARK: - Feature Access
    
    func canAccessFeature(_ feature: FeatureAccess) -> Bool {
        guard let user = user else { return false }
        return user.canAccessFeature(feature)
    }
    
    func getFeatureLimit(for feature: FeatureAccess) -> Int? {
        guard let user = user else { return nil }
        
        switch feature {
        case .unlimitedFeed:
            return user.subscriptionStatus == .free ? 3 : nil
        default:
            return nil
        }
    }
    
    // MARK: - Statistics
    
    func getUserStats() -> UserStats? {
        guard let user = user else { return nil }
        
        return UserStats(
            level: user.level,
            xp: user.xp,
            nextLevelXP: user.nextLevelXP,
            levelProgress: user.levelProgress,
            subscriptionTier: user.subscriptionStatus,
            joinDate: user.createdAt,
            interestsCount: user.preferences.interests.count,
            goalsCount: user.preferences.goals.count
        )
    }
    
    // MARK: - Helper Methods
    
    func clearError() {
        errorMessage = nil
    }
    
    func setUser(_ user: User) {
        self.user = user
    }
    
    // MARK: - Validation
    
    func validateInterest(_ interest: String) -> Bool {
        return !interest.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               interest.count <= 50
    }
    
    func validateGoal(_ goal: String) -> Bool {
        return !goal.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               goal.count <= 100
    }
}

// MARK: - User Stats
struct UserStats {
    let level: Int
    let xp: Int
    let nextLevelXP: Int
    let levelProgress: Double
    let subscriptionTier: SubscriptionTier
    let joinDate: Date
    let interestsCount: Int
    let goalsCount: Int
    
    var formattedJoinDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: joinDate)
    }
    
    var daysActive: Int {
        return Calendar.current.dateComponents([.day], from: joinDate, to: Date()).day ?? 0
    }
    
    var xpToNextLevel: Int {
        return nextLevelXP - xp
    }
    
    var levelProgressPercentage: Int {
        return Int(levelProgress * 100)
    }
}

// MARK: - Available Interests
extension UserManager {
    static let availableInterests = [
        "Gaming", "Technology", "Sustainability", "Healthcare", "Real Estate",
        "Cryptocurrency", "Artificial Intelligence", "Electric Vehicles", "Social Media",
        "E-commerce", "Renewable Energy", "Biotechnology", "Space Exploration",
        "Fintech", "Entertainment", "Food & Beverage", "Fashion", "Travel",
        "Education", "Sports", "Music", "Art", "Photography", "Fitness"
    ]
    
    static let availableGoals = [
        "Build an emergency fund",
        "Save for retirement",
        "Buy a house",
        "Pay off debt",
        "Start investing",
        "Learn about stocks",
        "Understand cryptocurrency",
        "Build passive income",
        "Save for vacation",
        "Fund education",
        "Start a business",
        "Achieve financial independence",
        "Learn about ETFs",
        "Understand options trading",
        "Build a diversified portfolio"
    ]
}
