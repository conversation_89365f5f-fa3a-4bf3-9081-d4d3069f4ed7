//
//  UserManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

@MainActor
class UserManager: ObservableObject {
    @Published var user: User?
    @Published var userStats: UserStats?
    @Published var achievements: [Achievement] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()

    // MARK: - User Profile Management

    func loadUserProfile(_ userID: UUID) async {
        isLoading = true
        errorMessage = nil

        do {
            let loadedUser = try await supabaseService.getUser(id: userID)
            let stats = try await loadUserStats(userID)
            let userAchievements = try await loadUserAchievements(userID)

            await MainActor.run {
                self.user = loadedUser
                self.userStats = stats
                self.achievements = userAchievements
                self.isLoading = false
            }

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }

    func updateUserPreferences(_ preferences: UserPreferences) async {
        guard let user = user else { return }

        do {
            let updatedUser = User(
                id: user.id,
                email: user.email,
                username: user.username,
                preferences: preferences,
                subscriptionStatus: user.subscriptionStatus,
                xp: user.xp,
                level: user.level,
                createdAt: user.createdAt,
                updatedAt: Date()
            )

            try await supabaseService.updateUser(updatedUser)

            await MainActor.run {
                self.user = updatedUser
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to update preferences: \(error.localizedDescription)"
            }
        }
    }

    // MARK: - XP and Leveling System

    func addXP(_ amount: Int) async {
        guard let user = user else { return }

        let newXP = user.xp + amount
        let newLevel = calculateLevel(from: newXP)
        let leveledUp = newLevel > user.level

        do {
            try await supabaseService.update(
                ["xp": newXP, "level": newLevel],
                table: "users",
                where: "id=eq.\(user.id.uuidString)"
            )

            await MainActor.run {
                self.user?.xp = newXP
                self.user?.level = newLevel

                if leveledUp {
                    self.handleLevelUp(newLevel)
                }
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to update XP: \(error.localizedDescription)"
            }
        }
    }

    private func calculateLevel(from xp: Int) -> Int {
        // XP required for each level: 100, 250, 500, 1000, 2000, etc.
        let xpThresholds = [0, 100, 250, 500, 1000, 2000, 4000, 8000, 16000, 32000, 64000]

        for (level, threshold) in xpThresholds.enumerated().reversed() {
            if xp >= threshold {
                return level
            }
        }
        return 0
    }

    private func handleLevelUp(_ newLevel: Int) {
        // Check for level-based achievements
        checkLevelAchievements(newLevel)

        // Show level up celebration (could trigger UI notification)
        print("🎉 Level up! You're now level \(newLevel)")
    }

    // MARK: - Achievement System

    func checkLevelAchievements(_ level: Int) {
        let levelAchievements: [Int: String] = [
            5: "rising_star",
            10: "finance_enthusiast",
            25: "investment_expert",
            50: "market_master",
            100: "legendary_investor"
        ]

        if let achievementID = levelAchievements[level] {
            Task {
                await unlockAchievement(achievementID)
            }
        }
    }

    func unlockAchievement(_ achievementID: String) async {
        guard let user = user else { return }

        // Check if already unlocked
        if achievements.contains(where: { $0.id == achievementID }) {
            return
        }

        do {
            let userAchievement = UserAchievement(
                userID: user.id,
                achievementID: achievementID,
                unlockedAt: Date()
            )

            try await supabaseService.create(userAchievement, table: "user_achievements")

            // Load the achievement details
            if let achievement = try await loadAchievement(achievementID) {
                await MainActor.run {
                    self.achievements.append(achievement)
                }
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to unlock achievement: \(error.localizedDescription)"
            }
        }
    }

    private func loadUserStats(_ userID: UUID) async throws -> UserStats {
        // Calculate user statistics
        let questsCompleted = try await getQuestsCompleted(userID)
        let totalXP = user?.xp ?? 0
        let level = user?.level ?? 0
        let daysActive = try await getDaysActive(userID)

        return UserStats(
            level: level,
            xp: totalXP,
            questsCompleted: questsCompleted,
            daysActive: daysActive,
            achievementsUnlocked: achievements.count
        )
    }

    private func loadUserAchievements(_ userID: UUID) async throws -> [Achievement] {
        let userAchievements: [UserAchievement] = try await supabaseService.fetch(
            UserAchievement.self,
            from: "user_achievements",
            where: "user_id=eq.\(userID.uuidString)"
        )

        var achievements: [Achievement] = []
        for userAchievement in userAchievements {
            if let achievement = try await loadAchievement(userAchievement.achievementID) {
                achievements.append(achievement)
            }
        }

        return achievements
    }

    private func loadAchievement(_ achievementID: String) async throws -> Achievement? {
        let achievements: [Achievement] = try await supabaseService.fetch(
            Achievement.self,
            from: "achievements",
            where: "id=eq.\(achievementID)"
        )
        return achievements.first
    }

    private func getQuestsCompleted(_ userID: UUID) async throws -> Int {
        let completions: [QuestCompletion] = try await supabaseService.fetch(
            QuestCompletion.self,
            from: "quest_completions",
            where: "user_id=eq.\(userID.uuidString)"
        )
        return completions.count
    }

    private func getDaysActive(_ userID: UUID) async throws -> Int {
        // Calculate days since account creation
        guard let user = user else { return 0 }
        let calendar = Calendar.current
        let now = Date()
        let components = calendar.dateComponents([.day], from: user.createdAt, to: now)
        return components.day ?? 0
    }
    
    // MARK: - User Profile Methods
    
    func loadUser(id: UUID) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let loadedUser = try await supabaseService.getUser(id: id)
            
            await MainActor.run {
                self.user = loadedUser
                self.isLoading = false
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    func updateUser(_ updatedUser: User) async {
        isLoading = true
        errorMessage = nil
        
        do {
            try await supabaseService.updateUser(updatedUser)
            
            await MainActor.run {
                self.user = updatedUser
                self.isLoading = false
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    func updatePreferences(_ preferences: UserPreferences) async {
        guard var currentUser = user else { return }
        
        currentUser.preferences = preferences
        currentUser.updatedAt = Date()
        
        await updateUser(currentUser)
    }
    
    func updateSubscription(_ tier: SubscriptionTier) async {
        guard var currentUser = user else { return }
        
        currentUser.subscriptionStatus = tier
        currentUser.updatedAt = Date()
        
        await updateUser(currentUser)
    }
    
    // MARK: - Preferences Management
    
    func addInterest(_ interest: String) async {
        guard var currentUser = user else { return }
        
        if !currentUser.preferences.interests.contains(interest) {
            currentUser.preferences.interests.append(interest)
            await updatePreferences(currentUser.preferences)
        }
    }
    
    func removeInterest(_ interest: String) async {
        guard var currentUser = user else { return }
        
        currentUser.preferences.interests.removeAll { $0 == interest }
        await updatePreferences(currentUser.preferences)
    }
    
    func addGoal(_ goal: String) async {
        guard var currentUser = user else { return }
        
        if !currentUser.preferences.goals.contains(goal) {
            currentUser.preferences.goals.append(goal)
            await updatePreferences(currentUser.preferences)
        }
    }
    
    func removeGoal(_ goal: String) async {
        guard var currentUser = user else { return }
        
        currentUser.preferences.goals.removeAll { $0 == goal }
        await updatePreferences(currentUser.preferences)
    }
    
    func updateRiskTolerance(_ riskTolerance: RiskTolerance) async {
        guard var currentUser = user else { return }
        
        currentUser.preferences.riskTolerance = riskTolerance
        await updatePreferences(currentUser.preferences)
    }
    
    func updateNotificationSettings(_ enabled: Bool) async {
        guard var currentUser = user else { return }
        
        currentUser.preferences.notificationsEnabled = enabled
        await updatePreferences(currentUser.preferences)
    }
    
    func updateDailyQuestTime(_ time: String) async {
        guard var currentUser = user else { return }
        
        currentUser.preferences.dailyQuestTime = time
        await updatePreferences(currentUser.preferences)
    }
    
    // MARK: - Feature Access
    
    func canAccessFeature(_ feature: FeatureAccess) -> Bool {
        guard let user = user else { return false }
        return user.canAccessFeature(feature)
    }
    
    func getFeatureLimit(for feature: FeatureAccess) -> Int? {
        guard let user = user else { return nil }
        
        switch feature {
        case .unlimitedFeed:
            return user.subscriptionStatus == .free ? 3 : nil
        default:
            return nil
        }
    }
    
    // MARK: - Statistics
    
    func getUserStats() -> UserStats? {
        guard let user = user else { return nil }
        
        return UserStats(
            level: user.level,
            xp: user.xp,
            nextLevelXP: user.nextLevelXP,
            levelProgress: user.levelProgress,
            subscriptionTier: user.subscriptionStatus,
            joinDate: user.createdAt,
            interestsCount: user.preferences.interests.count,
            goalsCount: user.preferences.goals.count
        )
    }
    
    // MARK: - Helper Methods
    
    func clearError() {
        errorMessage = nil
    }
    
    func setUser(_ user: User) {
        self.user = user
    }
    
    // MARK: - Validation
    
    func validateInterest(_ interest: String) -> Bool {
        return !interest.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               interest.count <= 50
    }
    
    func validateGoal(_ goal: String) -> Bool {
        return !goal.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               goal.count <= 100
    }
}

// MARK: - User Stats
struct UserStats {
    let level: Int
    let xp: Int
    let nextLevelXP: Int
    let levelProgress: Double
    let subscriptionTier: SubscriptionTier
    let joinDate: Date
    let interestsCount: Int
    let goalsCount: Int
    
    var formattedJoinDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: joinDate)
    }
    
    var daysActive: Int {
        return Calendar.current.dateComponents([.day], from: joinDate, to: Date()).day ?? 0
    }
    
    var xpToNextLevel: Int {
        return nextLevelXP - xp
    }
    
    var levelProgressPercentage: Int {
        return Int(levelProgress * 100)
    }
}

// MARK: - Available Interests
extension UserManager {
    static let availableInterests = [
        "Gaming", "Technology", "Sustainability", "Healthcare", "Real Estate",
        "Cryptocurrency", "Artificial Intelligence", "Electric Vehicles", "Social Media",
        "E-commerce", "Renewable Energy", "Biotechnology", "Space Exploration",
        "Fintech", "Entertainment", "Food & Beverage", "Fashion", "Travel",
        "Education", "Sports", "Music", "Art", "Photography", "Fitness"
    ]
    
    static let availableGoals = [
        "Build an emergency fund",
        "Save for retirement",
        "Buy a house",
        "Pay off debt",
        "Start investing",
        "Learn about stocks",
        "Understand cryptocurrency",
        "Build passive income",
        "Save for vacation",
        "Fund education",
        "Start a business",
        "Achieve financial independence",
        "Learn about ETFs",
        "Understand options trading",
        "Build a diversified portfolio"
    ]
}
