//
//  PaymentManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import StoreKit
import Combine

@MainActor
class PaymentManager: ObservableObject {
    @Published var paymentMethods: [PaymentMethod] = []
    @Published var billingHistory: [BillingRecord] = []
    @Published var subscriptionDetails: SubscriptionDetails?
    @Published var promoCode: String = ""
    @Published var appliedDiscount: Discount?
    @Published var isProcessingPayment = false
    @Published var paymentError: PaymentError?
    @Published var showingPaymentSheet = false
    
    private let subscriptionManager: SubscriptionManager
    private let supabaseService = SupabaseService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // Available product IDs with different billing cycles
    private let productIDs = [
        "com.wealthvibe.basic.monthly",
        "com.wealthvibe.basic.yearly",
        "com.wealthvibe.pro.monthly",
        "com.wealthvibe.pro.yearly",
        "com.wealthvibe.pro.lifetime"
    ]
    
    init(subscriptionManager: SubscriptionManager) {
        self.subscriptionManager = subscriptionManager
        setupSubscriptionObserver()
    }
    
    // MARK: - Payment Processing
    
    func processPayment(for product: Product, paymentMethod: PaymentMethod? = nil) async -> PaymentResult {
        isProcessingPayment = true
        paymentError = nil
        
        do {
            // Validate payment method if provided
            if let paymentMethod = paymentMethod {
                try await validatePaymentMethod(paymentMethod)
            }
            
            // Apply any active discounts
            let finalProduct = try await applyDiscountIfAvailable(to: product)
            
            // Process the purchase
            let result = try await finalProduct.purchase()
            
            switch result {
            case .success(let verification):
                let transaction = try checkVerified(verification)
                
                // Record the transaction
                await recordTransaction(transaction, originalProduct: product)
                
                // Update subscription status
                await subscriptionManager.updateSubscriptionStatus()
                
                // Send confirmation
                await sendPaymentConfirmation(transaction)
                
                await MainActor.run {
                    self.isProcessingPayment = false
                }
                
                await transaction.finish()
                return .success(transaction)
                
            case .userCancelled:
                await MainActor.run {
                    self.isProcessingPayment = false
                }
                return .cancelled
                
            case .pending:
                await MainActor.run {
                    self.paymentError = .paymentPending
                    self.isProcessingPayment = false
                }
                return .pending
                
            @unknown default:
                await MainActor.run {
                    self.paymentError = .unknownError
                    self.isProcessingPayment = false
                }
                return .failed(.unknownError)
            }
            
        } catch {
            await MainActor.run {
                self.paymentError = PaymentError.from(error)
                self.isProcessingPayment = false
            }
            return .failed(PaymentError.from(error))
        }
    }
    
    // MARK: - Subscription Management
    
    func loadSubscriptionDetails() async {
        do {
            let details = try await fetchSubscriptionDetails()
            
            await MainActor.run {
                self.subscriptionDetails = details
            }
            
        } catch {
            await MainActor.run {
                self.paymentError = .subscriptionLoadFailed
            }
        }
    }
    
    func changeSubscriptionPlan(to newProduct: Product) async -> Bool {
        isProcessingPayment = true
        paymentError = nil
        
        do {
            // Cancel current subscription if exists
            if let currentSubscription = subscriptionDetails?.currentSubscription {
                try await cancelSubscription(currentSubscription.id)
            }
            
            // Purchase new subscription
            let result = await processPayment(for: newProduct)
            
            switch result {
            case .success:
                await loadSubscriptionDetails()
                return true
            default:
                return false
            }
            
        } catch {
            await MainActor.run {
                self.paymentError = .subscriptionChangeFailed
                self.isProcessingPayment = false
            }
            return false
        }
    }
    
    func pauseSubscription() async -> Bool {
        guard let subscription = subscriptionDetails?.currentSubscription else { return false }
        
        do {
            try await pauseSubscription(subscription.id)
            await loadSubscriptionDetails()
            return true
        } catch {
            await MainActor.run {
                self.paymentError = .subscriptionPauseFailed
            }
            return false
        }
    }
    
    func resumeSubscription() async -> Bool {
        guard let subscription = subscriptionDetails?.currentSubscription else { return false }
        
        do {
            try await resumeSubscription(subscription.id)
            await loadSubscriptionDetails()
            return true
        } catch {
            await MainActor.run {
                self.paymentError = .subscriptionResumeFailed
            }
            return false
        }
    }
    
    // MARK: - Billing History
    
    func loadBillingHistory() async {
        do {
            let history = try await fetchBillingHistory()
            
            await MainActor.run {
                self.billingHistory = history.sorted { $0.date > $1.date }
            }
            
        } catch {
            await MainActor.run {
                self.paymentError = .billingHistoryLoadFailed
            }
        }
    }
    
    func downloadInvoice(_ record: BillingRecord) async -> URL? {
        do {
            return try await generateInvoicePDF(for: record)
        } catch {
            await MainActor.run {
                self.paymentError = .invoiceGenerationFailed
            }
            return nil
        }
    }
    
    // MARK: - Promo Codes & Discounts
    
    func validatePromoCode(_ code: String) async -> Bool {
        do {
            let discount = try await validatePromoCode(code)
            
            await MainActor.run {
                self.appliedDiscount = discount
                self.promoCode = code
            }
            
            return true
            
        } catch {
            await MainActor.run {
                self.paymentError = .invalidPromoCode
                self.appliedDiscount = nil
                self.promoCode = ""
            }
            return false
        }
    }
    
    func removeDiscount() {
        appliedDiscount = nil
        promoCode = ""
    }
    
    // MARK: - Payment Methods
    
    func addPaymentMethod(_ method: PaymentMethod) async -> Bool {
        do {
            try await savePaymentMethod(method)
            await loadPaymentMethods()
            return true
        } catch {
            await MainActor.run {
                self.paymentError = .paymentMethodAddFailed
            }
            return false
        }
    }
    
    func removePaymentMethod(_ methodId: String) async -> Bool {
        do {
            try await deletePaymentMethod(methodId)
            await loadPaymentMethods()
            return true
        } catch {
            await MainActor.run {
                self.paymentError = .paymentMethodRemoveFailed
            }
            return false
        }
    }
    
    func loadPaymentMethods() async {
        do {
            let methods = try await fetchPaymentMethods()
            
            await MainActor.run {
                self.paymentMethods = methods
            }
            
        } catch {
            await MainActor.run {
                self.paymentError = .paymentMethodsLoadFailed
            }
        }
    }
    
    // MARK: - Revenue Analytics
    
    func getRevenueMetrics() async -> RevenueMetrics? {
        do {
            return try await fetchRevenueMetrics()
        } catch {
            return nil
        }
    }
    
    func trackConversion(from tier: SubscriptionTier, to newTier: SubscriptionTier) async {
        let event = ConversionEvent(
            fromTier: tier,
            toTier: newTier,
            timestamp: Date(),
            revenue: newTier.monthlyPrice - tier.monthlyPrice
        )
        
        try? await recordConversionEvent(event)
    }
    
    // MARK: - Helper Methods
    
    private func setupSubscriptionObserver() {
        subscriptionManager.$subscriptionStatus
            .sink { [weak self] status in
                Task {
                    await self?.loadSubscriptionDetails()
                }
            }
            .store(in: &cancellables)
    }
    
    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            throw PaymentError.verificationFailed
        case .verified(let safe):
            return safe
        }
    }
    
    private func validatePaymentMethod(_ method: PaymentMethod) async throws {
        // Validate payment method details
        switch method.type {
        case .creditCard:
            guard method.isValid else {
                throw PaymentError.invalidPaymentMethod
            }
        case .applePay:
            // Apple Pay validation handled by system
            break
        case .bankTransfer:
            guard method.bankDetails?.isValid == true else {
                throw PaymentError.invalidBankDetails
            }
        }
    }
    
    private func applyDiscountIfAvailable(to product: Product) async throws -> Product {
        guard let discount = appliedDiscount else { return product }
        
        // For StoreKit, discounts are typically handled server-side
        // This would integrate with your backend discount system
        return product
    }
    
    private func recordTransaction(_ transaction: Transaction, originalProduct: Product) async {
        let record = BillingRecord(
            id: transaction.id.description,
            productId: transaction.productID,
            amount: originalProduct.price,
            currency: "USD", // Would get from product
            date: transaction.purchaseDate,
            status: .completed,
            description: originalProduct.displayName,
            invoiceURL: nil
        )
        
        try? await saveBillingRecord(record)
    }
    
    private func sendPaymentConfirmation(_ transaction: Transaction) async {
        // Send confirmation email/notification
        // This would integrate with your notification system
    }
    
    // MARK: - API Calls (Mock implementations)
    
    private func fetchSubscriptionDetails() async throws -> SubscriptionDetails {
        // Mock implementation - would call your backend
        return SubscriptionDetails(
            currentSubscription: ActiveSubscription(
                id: "sub_123",
                productId: "com.wealthvibe.pro.monthly",
                status: .active,
                currentPeriodStart: Date(),
                currentPeriodEnd: Calendar.current.date(byAdding: .month, value: 1, to: Date()) ?? Date(),
                cancelAtPeriodEnd: false
            ),
            nextBillingDate: Calendar.current.date(byAdding: .month, value: 1, to: Date()),
            billingCycle: .monthly
        )
    }
    
    private func fetchBillingHistory() async throws -> [BillingRecord] {
        // Mock implementation - would call your backend
        return []
    }
    
    private func fetchPaymentMethods() async throws -> [PaymentMethod] {
        // Mock implementation - would call your backend
        return []
    }
    
    private func validatePromoCode(_ code: String) async throws -> Discount {
        // Mock implementation - would validate with backend
        throw PaymentError.invalidPromoCode
    }
    
    private func fetchRevenueMetrics() async throws -> RevenueMetrics {
        // Mock implementation - would call analytics backend
        return RevenueMetrics(
            monthlyRecurringRevenue: 1_500_000,
            annualRecurringRevenue: 18_000_000,
            churnRate: 5.2,
            averageRevenuePerUser: 12.50,
            conversionRate: 8.5,
            lifetimeValue: 180.0
        )
    }
    
    // Additional mock methods for API calls
    private func cancelSubscription(_ subscriptionId: String) async throws { }
    private func pauseSubscription(_ subscriptionId: String) async throws { }
    private func resumeSubscription(_ subscriptionId: String) async throws { }
    private func savePaymentMethod(_ method: PaymentMethod) async throws { }
    private func deletePaymentMethod(_ methodId: String) async throws { }
    private func generateInvoicePDF(for record: BillingRecord) async throws -> URL { URL(string: "https://example.com")! }
    private func saveBillingRecord(_ record: BillingRecord) async throws { }
    private func recordConversionEvent(_ event: ConversionEvent) async throws { }
}
