//
//  CacheManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

class CacheManager: ObservableObject {
    static let shared = CacheManager()
    
    private var memoryCache: NSCache<NSString, CacheItem> = NSCache()
    private var diskCache: DiskCache
    private var cacheStatistics = CacheStatistics()
    private var isDebugLoggingEnabled = false
    private var isLowMemoryMode = false
    
    // Cache configuration
    private let maxMemoryCacheSize: Int = 50 * 1024 * 1024 // 50MB
    private let maxDiskCacheSize: Int = 200 * 1024 * 1024 // 200MB
    private let defaultCacheDuration: TimeInterval = 300 // 5 minutes
    private let lowPriorityCacheDuration: TimeInterval = 60 // 1 minute
    
    private init() {
        diskCache = DiskCache()
        setupMemoryCache()
        setupCacheCleanup()
    }
    
    // MARK: - Cache Setup
    
    private func setupMemoryCache() {
        memoryCache.totalCostLimit = maxMemoryCacheSize
        memoryCache.countLimit = 1000
        memoryCache.delegate = self
    }
    
    private func setupCacheCleanup() {
        // Clean up expired cache every 5 minutes
        Timer.scheduledTimer(withTimeInterval: 300, repeats: true) { [weak self] _ in
            self?.cleanupExpiredCache()
        }
    }
    
    // MARK: - Cache Operations
    
    func set<T: Codable>(_ value: T, forKey key: String, priority: CachePriority = .normal, duration: TimeInterval? = nil) {
        let cacheDuration = duration ?? (priority == .low ? lowPriorityCacheDuration : defaultCacheDuration)
        let expirationDate = Date().addingTimeInterval(cacheDuration)
        
        let cacheItem = CacheItem(
            value: value,
            expirationDate: expirationDate,
            priority: priority,
            accessCount: 0,
            lastAccessed: Date()
        )
        
        // Store in memory cache
        let cost = estimateSize(of: value)
        memoryCache.setObject(cacheItem, forKey: NSString(string: key), cost: cost)
        
        // Store in disk cache for persistence
        if priority != .low {
            diskCache.set(cacheItem, forKey: key)
        }
        
        // Update statistics
        cacheStatistics.totalSets += 1
        
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Cached item for key: \(key), priority: \(priority), duration: \(cacheDuration)s", category: "CACHE")
        }
    }
    
    func get<T: Codable>(_ type: T.Type, forKey key: String) -> T? {
        // Check memory cache first
        if let cacheItem = memoryCache.object(forKey: NSString(string: key)) {
            if cacheItem.isValid {
                cacheItem.accessCount += 1
                cacheItem.lastAccessed = Date()
                cacheStatistics.memoryHits += 1
                
                if isDebugLoggingEnabled {
                    DevelopmentConfig.log("Memory cache hit for key: \(key)", category: "CACHE")
                }
                
                return cacheItem.value as? T
            } else {
                // Remove expired item
                memoryCache.removeObject(forKey: NSString(string: key))
            }
        }
        
        // Check disk cache
        if let cacheItem = diskCache.get(forKey: key) {
            if cacheItem.isValid {
                // Restore to memory cache
                let cost = estimateSize(of: cacheItem.value)
                memoryCache.setObject(cacheItem, forKey: NSString(string: key), cost: cost)
                
                cacheItem.accessCount += 1
                cacheItem.lastAccessed = Date()
                cacheStatistics.diskHits += 1
                
                if isDebugLoggingEnabled {
                    DevelopmentConfig.log("Disk cache hit for key: \(key)", category: "CACHE")
                }
                
                return cacheItem.value as? T
            } else {
                // Remove expired item
                diskCache.remove(forKey: key)
            }
        }
        
        // Cache miss
        cacheStatistics.misses += 1
        
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Cache miss for key: \(key)", category: "CACHE")
        }
        
        return nil
    }
    
    func remove(forKey key: String) {
        memoryCache.removeObject(forKey: NSString(string: key))
        diskCache.remove(forKey: key)
        
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Removed cache item for key: \(key)", category: "CACHE")
        }
    }
    
    func removeAll() {
        memoryCache.removeAllObjects()
        diskCache.removeAll()
        cacheStatistics.reset()
        
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Cleared all cache", category: "CACHE")
        }
    }
    
    // MARK: - Cache Management
    
    func clearExpiredCache() {
        let keys = getAllKeys()
        var removedCount = 0
        
        for key in keys {
            if let cacheItem = memoryCache.object(forKey: NSString(string: key)) {
                if !cacheItem.isValid {
                    memoryCache.removeObject(forKey: NSString(string: key))
                    removedCount += 1
                }
            }
        }
        
        diskCache.clearExpired()
        
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Cleared \(removedCount) expired cache items", category: "CACHE")
        }
    }
    
    func clearLowPriorityCache() {
        let keys = getAllKeys()
        var removedCount = 0
        
        for key in keys {
            if let cacheItem = memoryCache.object(forKey: NSString(string: key)) {
                if cacheItem.priority == .low {
                    memoryCache.removeObject(forKey: NSString(string: key))
                    diskCache.remove(forKey: key)
                    removedCount += 1
                }
            }
        }
        
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Cleared \(removedCount) low priority cache items", category: "CACHE")
        }
    }
    
    func preloadFrequentData() {
        // Preload frequently accessed data based on access patterns
        let frequentKeys = getFrequentlyAccessedKeys()
        
        for key in frequentKeys {
            // This would typically trigger a background fetch for the data
            // For now, we'll just log the intent
            if isDebugLoggingEnabled {
                DevelopmentConfig.log("Preloading frequent data for key: \(key)", category: "CACHE")
            }
        }
    }
    
    func optimizeCacheSizes() {
        if isLowMemoryMode {
            memoryCache.totalCostLimit = maxMemoryCacheSize / 2
            memoryCache.countLimit = 500
        } else {
            memoryCache.totalCostLimit = maxMemoryCacheSize
            memoryCache.countLimit = 1000
        }
        
        diskCache.optimizeSize()
        
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Optimized cache sizes - Low memory mode: \(isLowMemoryMode)", category: "CACHE")
        }
    }
    
    // MARK: - Performance Modes
    
    func enableLowMemoryMode() {
        isLowMemoryMode = true
        clearLowPriorityCache()
        optimizeCacheSizes()
        
        DevelopmentConfig.log("Low memory mode enabled", category: "CACHE")
    }
    
    func disableLowMemoryMode() {
        isLowMemoryMode = false
        optimizeCacheSizes()
        
        DevelopmentConfig.log("Low memory mode disabled", category: "CACHE")
    }
    
    func enableAggressiveCaching() {
        // Increase cache durations for network optimization
        // This would be implemented with dynamic cache duration adjustment
        
        DevelopmentConfig.log("Aggressive caching enabled", category: "CACHE")
    }
    
    func increaseCacheDuration() {
        // Increase cache durations for battery saving
        // This would be implemented with dynamic cache duration adjustment
        
        DevelopmentConfig.log("Cache duration increased for battery saving", category: "CACHE")
    }
    
    // MARK: - Statistics and Monitoring
    
    func getHitRate() -> Double {
        let totalRequests = cacheStatistics.memoryHits + cacheStatistics.diskHits + cacheStatistics.misses
        guard totalRequests > 0 else { return 0.0 }
        
        let totalHits = cacheStatistics.memoryHits + cacheStatistics.diskHits
        return Double(totalHits) / Double(totalRequests)
    }
    
    func getStatistics() -> CacheStatistics {
        cacheStatistics.memoryCacheSize = memoryCache.totalCostLimit
        cacheStatistics.diskCacheSize = diskCache.getCurrentSize()
        cacheStatistics.itemCount = getAllKeys().count
        return cacheStatistics
    }
    
    func getMemoryUsage() -> Int {
        return memoryCache.totalCostLimit
    }
    
    func getDiskUsage() -> Int {
        return diskCache.getCurrentSize()
    }
    
    // MARK: - Debug and Development
    
    func enableDebugLogging() {
        isDebugLoggingEnabled = true
    }
    
    func disableDebugLogging() {
        isDebugLoggingEnabled = false
    }
    
    func printCacheStatistics() {
        guard DevelopmentConfig.isDevelopmentMode else { return }
        
        let stats = getStatistics()
        let hitRate = getHitRate()
        
        print("""
        📊 Cache Statistics:
        Hit Rate: \(String(format: "%.1f", hitRate * 100))%
        Memory Hits: \(stats.memoryHits)
        Disk Hits: \(stats.diskHits)
        Misses: \(stats.misses)
        Total Sets: \(stats.totalSets)
        Items: \(stats.itemCount)
        Memory Usage: \(stats.memoryCacheSize / 1024 / 1024)MB
        Disk Usage: \(stats.diskCacheSize / 1024 / 1024)MB
        """)
    }
    
    // MARK: - Helper Methods
    
    private func getAllKeys() -> [String] {
        // This is a simplified implementation
        // In a real scenario, you'd maintain a separate collection of keys
        return []
    }
    
    private func getFrequentlyAccessedKeys() -> [String] {
        // Return keys that are accessed frequently based on access patterns
        // This would be implemented with actual access tracking
        return []
    }
    
    private func estimateSize<T>(of value: T) -> Int {
        // Simplified size estimation
        if let data = try? JSONEncoder().encode(value as! Codable) {
            return data.count
        }
        return 1024 // Default estimate
    }
    
    private func cleanupExpiredCache() {
        clearExpiredCache()
    }
}

// MARK: - NSCacheDelegate
extension CacheManager: NSCacheDelegate {
    func cache(_ cache: NSCache<AnyObject, AnyObject>, willEvictObject obj: AnyObject) {
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Memory cache evicting object", category: "CACHE")
        }
    }
}

// MARK: - Cache Item
class CacheItem: NSObject {
    let value: Any
    let expirationDate: Date
    let priority: CachePriority
    var accessCount: Int
    var lastAccessed: Date
    
    init(value: Any, expirationDate: Date, priority: CachePriority, accessCount: Int, lastAccessed: Date) {
        self.value = value
        self.expirationDate = expirationDate
        self.priority = priority
        self.accessCount = accessCount
        self.lastAccessed = lastAccessed
        super.init()
    }
    
    var isValid: Bool {
        return Date() < expirationDate
    }
    
    var isFrequentlyAccessed: Bool {
        return accessCount > 5 && Date().timeIntervalSince(lastAccessed) < 3600 // 1 hour
    }
}

// MARK: - Cache Priority
enum CachePriority: String, Codable {
    case low = "low"
    case normal = "normal"
    case high = "high"
    case critical = "critical"
}

// MARK: - Cache Statistics
struct CacheStatistics: Codable {
    var memoryHits: Int = 0
    var diskHits: Int = 0
    var misses: Int = 0
    var totalSets: Int = 0
    var memoryCacheSize: Int = 0
    var diskCacheSize: Int = 0
    var itemCount: Int = 0
    
    mutating func reset() {
        memoryHits = 0
        diskHits = 0
        misses = 0
        totalSets = 0
        memoryCacheSize = 0
        diskCacheSize = 0
        itemCount = 0
    }
}

// MARK: - Disk Cache
class DiskCache {
    private let cacheDirectory: URL
    private let fileManager = FileManager.default
    
    init() {
        let cachesDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
        cacheDirectory = cachesDirectory.appendingPathComponent("VibeFinanceCache")
        
        try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
    }
    
    func set(_ item: CacheItem, forKey key: String) {
        let url = cacheDirectory.appendingPathComponent(key.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? key)
        
        do {
            let data = try JSONEncoder().encode(CacheItemWrapper(item: item))
            try data.write(to: url)
        } catch {
            DevelopmentConfig.log("Failed to write to disk cache: \(error)", category: "CACHE")
        }
    }
    
    func get(forKey key: String) -> CacheItem? {
        let url = cacheDirectory.appendingPathComponent(key.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? key)
        
        do {
            let data = try Data(contentsOf: url)
            let wrapper = try JSONDecoder().decode(CacheItemWrapper.self, from: data)
            return wrapper.toCacheItem()
        } catch {
            return nil
        }
    }
    
    func remove(forKey key: String) {
        let url = cacheDirectory.appendingPathComponent(key.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? key)
        try? fileManager.removeItem(at: url)
    }
    
    func removeAll() {
        try? fileManager.removeItem(at: cacheDirectory)
        try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
    }
    
    func clearExpired() {
        guard let urls = try? fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil) else { return }
        
        for url in urls {
            if let item = get(forKey: url.lastPathComponent), !item.isValid {
                try? fileManager.removeItem(at: url)
            }
        }
    }
    
    func getCurrentSize() -> Int {
        guard let urls = try? fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey]) else { return 0 }
        
        return urls.compactMap { url in
            try? url.resourceValues(forKeys: [.fileSizeKey]).fileSize
        }.reduce(0, +)
    }
    
    func optimizeSize() {
        // Remove least recently used items if cache is too large
        let maxSize = 200 * 1024 * 1024 // 200MB
        let currentSize = getCurrentSize()
        
        if currentSize > maxSize {
            // Implementation would sort by last accessed and remove oldest items
            DevelopmentConfig.log("Disk cache optimization needed - current size: \(currentSize / 1024 / 1024)MB", category: "CACHE")
        }
    }
}

// MARK: - Cache Item Wrapper for Disk Storage
private struct CacheItemWrapper: Codable {
    let valueData: Data
    let expirationDate: Date
    let priority: CachePriority
    let accessCount: Int
    let lastAccessed: Date
    
    init(item: CacheItem) {
        self.valueData = (try? JSONEncoder().encode(item.value as! Codable)) ?? Data()
        self.expirationDate = item.expirationDate
        self.priority = item.priority
        self.accessCount = item.accessCount
        self.lastAccessed = item.lastAccessed
    }
    
    func toCacheItem() -> CacheItem {
        // This is simplified - in reality you'd need type information to decode properly
        return CacheItem(
            value: valueData,
            expirationDate: expirationDate,
            priority: priority,
            accessCount: accessCount,
            lastAccessed: lastAccessed
        )
    }
}
