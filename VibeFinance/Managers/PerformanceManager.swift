//
//  PerformanceManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine
import UIKit

@MainActor
class PerformanceManager: ObservableObject {
    static let shared = PerformanceManager()
    
    @Published var performanceMetrics: PerformanceMetrics = PerformanceMetrics()
    @Published var isMonitoring = false
    @Published var memoryWarningActive = false
    
    private let cacheManager = CacheManager.shared
    private let imageCache = ImageCacheManager.shared
    private let networkOptimizer = NetworkOptimizer.shared
    private var cancellables = Set<AnyCancellable>()
    private var performanceTimer: Timer?
    private var startTime: CFAbsoluteTime = 0
    
    private init() {
        setupMemoryWarningObserver()
        setupPerformanceMonitoring()
    }

    private func setupPerformanceMonitoring() {
        // Initialize performance monitoring
        startTime = CFAbsoluteTimeGetCurrent()
    }
    
    // MARK: - Performance Monitoring
    
    func startPerformanceMonitoring() {
        isMonitoring = true
        startTime = CFAbsoluteTimeGetCurrent()
        
        performanceTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.updatePerformanceMetrics()
            }
        }
        
        DevelopmentConfig.log("Performance monitoring started", category: "PERF")
    }
    
    nonisolated func stopPerformanceMonitoring() {
        Task { @MainActor in
            isMonitoring = false
            performanceTimer?.invalidate()
            performanceTimer = nil

            DevelopmentConfig.log("Performance monitoring stopped", category: "PERF")
        }
    }
    
    private func updatePerformanceMetrics() {
        let currentTime = CFAbsoluteTimeGetCurrent()
        let memoryUsage = getMemoryUsage()
        let cpuUsage = getCPUUsage()
        
        performanceMetrics.updateMetrics(
            memoryUsage: memoryUsage,
            cpuUsage: cpuUsage,
            uptime: currentTime - startTime,
            cacheHitRate: cacheManager.getHitRate(),
            networkLatency: networkOptimizer.getAverageLatency()
        )
        
        // Check for performance issues
        checkPerformanceThresholds()
    }
    
    private func checkPerformanceThresholds() {
        // Memory warning threshold (80% of available memory)
        if performanceMetrics.memoryUsage > 0.8 {
            triggerMemoryOptimization()
        }
        
        // CPU usage threshold (90%)
        if performanceMetrics.cpuUsage > 0.9 {
            triggerCPUOptimization()
        }
        
        // Cache hit rate threshold (below 70%)
        if performanceMetrics.cacheHitRate < 0.7 {
            optimizeCaching()
        }
    }
    
    // MARK: - Memory Management
    
    private func setupMemoryWarningObserver() {
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleMemoryWarning()
                }
            }
            .store(in: &cancellables)
    }
    
    private func handleMemoryWarning() {
        memoryWarningActive = true
        DevelopmentConfig.log("Memory warning received - starting cleanup", category: "MEMORY")
        
        // Clear caches
        cacheManager.clearExpiredCache()
        imageCache.clearMemoryCache()
        
        // Notify managers to reduce memory usage
        NotificationCenter.default.post(name: .performanceOptimizationNeeded, object: nil)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.memoryWarningActive = false
        }
    }
    
    private func triggerMemoryOptimization() {
        DevelopmentConfig.log("Memory usage high - optimizing", category: "MEMORY")
        
        // Clear non-essential caches
        cacheManager.clearLowPriorityCache()
        imageCache.reduceMemoryFootprint()
        
        // Reduce background processing
        networkOptimizer.reduceConcurrentRequests()
    }
    
    private func triggerCPUOptimization() {
        DevelopmentConfig.log("CPU usage high - optimizing", category: "CPU")
        
        // Reduce animation complexity
        NotificationCenter.default.post(name: .reduceAnimations, object: nil)
        
        // Throttle background tasks
        networkOptimizer.throttleBackgroundTasks()
    }
    
    private func optimizeCaching() {
        DevelopmentConfig.log("Cache hit rate low - optimizing", category: "CACHE")
        
        // Preload frequently accessed data
        cacheManager.preloadFrequentData()
        
        // Adjust cache sizes
        cacheManager.optimizeCacheSizes()
    }
    
    // MARK: - Performance Metrics Collection
    
    private func getMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            let usedMemory = Double(info.resident_size)
            let totalMemory = Double(ProcessInfo.processInfo.physicalMemory)
            return usedMemory / totalMemory
        }
        
        return 0.0
    }
    
    private func getCPUUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        if kerr == KERN_SUCCESS {
            // This is a simplified CPU usage calculation
            // In a real implementation, you'd track CPU time over intervals
            return min(1.0, Double(info.resident_size) / Double(ProcessInfo.processInfo.physicalMemory))
        }
        
        return 0.0
    }
    
    // MARK: - Performance Optimization Actions
    
    func optimizeForLowMemory() {
        cacheManager.enableLowMemoryMode()
        imageCache.enableLowMemoryMode()
        networkOptimizer.enableLowMemoryMode()
        
        DevelopmentConfig.log("Low memory mode enabled", category: "PERF")
    }
    
    func optimizeForBattery() {
        networkOptimizer.enableBatterySavingMode()
        cacheManager.increaseCacheDuration()
        
        DevelopmentConfig.log("Battery saving mode enabled", category: "PERF")
    }
    
    func optimizeForNetwork() {
        networkOptimizer.enableNetworkOptimization()
        cacheManager.enableAggressiveCaching()
        
        DevelopmentConfig.log("Network optimization enabled", category: "PERF")
    }
    
    // MARK: - Performance Reporting
    
    func generatePerformanceReport() -> PerformanceReport {
        return PerformanceReport(
            metrics: performanceMetrics,
            cacheStats: cacheManager.getStatistics(),
            networkStats: networkOptimizer.getStatistics(),
            imageStats: imageCache.getStatistics(),
            timestamp: Date()
        )
    }
    
    func exportPerformanceData() -> Data? {
        let report = generatePerformanceReport()
        return try? JSONEncoder().encode(report)
    }
    
    // MARK: - Development Tools
    
    func enablePerformanceDebugging() {
        guard DevelopmentConfig.isDevelopmentMode else { return }
        
        startPerformanceMonitoring()
        cacheManager.enableDebugLogging()
        networkOptimizer.enableDebugLogging()
        imageCache.enableDebugLogging()
        
        DevelopmentConfig.log("Performance debugging enabled", category: "DEBUG")
    }
    
    func disablePerformanceDebugging() {
        stopPerformanceMonitoring()
        cacheManager.disableDebugLogging()
        networkOptimizer.disableDebugLogging()
        imageCache.disableDebugLogging()
        
        DevelopmentConfig.log("Performance debugging disabled", category: "DEBUG")
    }
    
    // MARK: - Cleanup
    
    deinit {
        stopPerformanceMonitoring()
        cancellables.removeAll()
    }
}

// MARK: - Performance Metrics
struct PerformanceMetrics: Codable {
    var memoryUsage: Double = 0.0
    var cpuUsage: Double = 0.0
    var uptime: TimeInterval = 0.0
    var cacheHitRate: Double = 0.0
    var networkLatency: TimeInterval = 0.0
    var frameRate: Double = 60.0
    var batteryLevel: Double = 1.0
    var thermalState: String = "nominal"
    
    mutating func updateMetrics(
        memoryUsage: Double,
        cpuUsage: Double,
        uptime: TimeInterval,
        cacheHitRate: Double,
        networkLatency: TimeInterval
    ) {
        self.memoryUsage = memoryUsage
        self.cpuUsage = cpuUsage
        self.uptime = uptime
        self.cacheHitRate = cacheHitRate
        self.networkLatency = networkLatency
        self.batteryLevel = Double(UIDevice.current.batteryLevel)
        
        switch ProcessInfo.processInfo.thermalState {
        case .nominal: self.thermalState = "nominal"
        case .fair: self.thermalState = "fair"
        case .serious: self.thermalState = "serious"
        case .critical: self.thermalState = "critical"
        @unknown default: self.thermalState = "unknown"
        }
    }
    
    var isPerformanceGood: Bool {
        return memoryUsage < 0.7 && cpuUsage < 0.8 && cacheHitRate > 0.7 && networkLatency < 1.0
    }
    
    var performanceGrade: String {
        let score = (1.0 - memoryUsage) * 0.3 + 
                   (1.0 - cpuUsage) * 0.3 + 
                   cacheHitRate * 0.2 + 
                   (networkLatency < 1.0 ? 0.2 : 0.0)
        
        switch score {
        case 0.9...1.0: return "A+"
        case 0.8..<0.9: return "A"
        case 0.7..<0.8: return "B"
        case 0.6..<0.7: return "C"
        case 0.5..<0.6: return "D"
        default: return "F"
        }
    }
}

// MARK: - Performance Report
struct PerformanceReport: Codable {
    let metrics: PerformanceMetrics
    let cacheStats: CacheStatistics
    let networkStats: NetworkStatistics
    let imageStats: ImageCacheStatistics
    let timestamp: Date
    
    var summary: String {
        return """
        Performance Report - \(timestamp)
        Grade: \(metrics.performanceGrade)
        Memory: \(String(format: "%.1f", metrics.memoryUsage * 100))%
        CPU: \(String(format: "%.1f", metrics.cpuUsage * 100))%
        Cache Hit Rate: \(String(format: "%.1f", metrics.cacheHitRate * 100))%
        Network Latency: \(String(format: "%.0f", metrics.networkLatency * 1000))ms
        """
    }
}

// MARK: - Notification Extensions
extension Notification.Name {
    static let performanceOptimizationNeeded = Notification.Name("performanceOptimizationNeeded")
    static let reduceAnimations = Notification.Name("reduceAnimations")
    static let memoryWarningReceived = Notification.Name("memoryWarningReceived")
}
