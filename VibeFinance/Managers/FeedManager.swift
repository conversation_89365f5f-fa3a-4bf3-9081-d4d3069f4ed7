//
//  FeedManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

@MainActor
class FeedManager: ObservableObject {
    @Published var feedItems: [FeedItem] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var hasMoreItems = true
    
    private let supabaseService = SupabaseService.shared
    private let aiService = GeminiAIService()
    private let newsService = NewsService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Feed Generation

    func generateDailyFeed(for user: User) async {
        isLoading = true
        errorMessage = nil

        do {
            // Create feed query based on user preferences
            let query = FeedQuery(
                interests: user.preferences.interests,
                goals: user.preferences.goals,
                limit: user.canAccessFeature(.unlimitedFeed) ? 20 : 3
            )

            // Fetch content from various sources
            let newsArticles = try await newsService.fetchNews(query: query.queryString)
            let socialPosts = try await newsService.fetchSocialMedia(query: query.queryString)

            // Process content with AI
            var newFeedItems: [FeedItem] = []

            for article in newsArticles.prefix(10) {
                if let feedItem = try await processNewsArticle(article, for: user) {
                    newFeedItems.append(feedItem)
                }
            }

            for post in socialPosts.prefix(5) {
                if let feedItem = try await processSocialPost(post, for: user) {
                    newFeedItems.append(feedItem)
                }
            }

            // Sort by relevance and vibe score
            newFeedItems.sort { $0.content.vibeScore > $1.content.vibeScore }

            // Save to database
            for feedItem in newFeedItems {
                try await supabaseService.create(feedItem, table: "feed_items")
            }

            await MainActor.run {
                self.feedItems = newFeedItems
                self.isLoading = false
            }

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }

    func refreshFeed(for user: User) async {
        feedItems.removeAll()
        await generateDailyFeed(for: user)
    }

    func loadMoreFeedItems() async {
        guard hasMoreItems, !isLoading else { return }

        isLoading = true

        do {
            // Load more items from database
            let moreItems: [FeedItem] = try await supabaseService.fetch(
                FeedItem.self,
                from: "feed_items",
                where: "user_id=eq.\(feedItems.first?.userID.uuidString ?? "")"
            )

            await MainActor.run {
                self.feedItems.append(contentsOf: moreItems)
                self.hasMoreItems = moreItems.count >= 10
                self.isLoading = false
            }

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }

    // MARK: - Social Actions

    func addReaction(_ type: ReactionType, to feedItem: FeedItem) async {
        guard let userID = getCurrentUserID() else { return }

        let reaction = Reaction(
            userID: userID,
            type: type,
            timestamp: Date()
        )

        do {
            // Update local state
            if let index = feedItems.firstIndex(where: { $0.id == feedItem.id }) {
                await MainActor.run {
                    self.feedItems[index].addReaction(reaction)
                }
            }

            // Save to database
            try await supabaseService.create(reaction, table: "feed_reactions")

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to add reaction: \(error.localizedDescription)"
            }
        }
    }

    func toggleBookmark(for feedItem: FeedItem) async {
        do {
            // Update local state
            if let index = feedItems.firstIndex(where: { $0.id == feedItem.id }) {
                await MainActor.run {
                    self.feedItems[index].isBookmarked.toggle()
                }
            }

            // Update database
            try await supabaseService.update(
                ["is_bookmarked": !feedItem.isBookmarked],
                table: "feed_items",
                where: "id=eq.\(feedItem.id.uuidString)"
            )

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to update bookmark: \(error.localizedDescription)"
            }
        }
    }

    private func getCurrentUserID() -> UUID? {
        // Get current user ID from auth manager or user manager
        return UUID() // Placeholder
    }
    
    func loadFeed(for userID: UUID) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let items = try await supabaseService.fetch(
                FeedItem.self,
                from: "feeds",
                where: "userID=eq.\(userID.uuidString)&order=createdAt.desc&limit=20"
            )
            
            await MainActor.run {
                self.feedItems = items
                self.isLoading = false
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    func loadMoreItems(for userID: UUID) async {
        guard hasMoreItems && !isLoading else { return }
        
        isLoading = true
        
        do {
            let offset = feedItems.count
            let items = try await supabaseService.fetch(
                FeedItem.self,
                from: "feeds",
                where: "userID=eq.\(userID.uuidString)&order=createdAt.desc&limit=20&offset=\(offset)"
            )
            
            await MainActor.run {
                self.feedItems.append(contentsOf: items)
                self.hasMoreItems = items.count == 20
                self.isLoading = false
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    // MARK: - Feed Interactions
    
    func markAsRead(_ item: FeedItem) async {
        var updatedItem = item
        updatedItem.isRead = true
        
        do {
            try await supabaseService.update(
                updatedItem,
                in: "feeds",
                where: "id=eq.\(item.id.uuidString)"
            )
            
            await MainActor.run {
                if let index = self.feedItems.firstIndex(where: { $0.id == item.id }) {
                    self.feedItems[index] = updatedItem
                }
            }
            
        } catch {
            print("Failed to mark item as read: \(error)")
        }
    }
    
    func toggleBookmark(_ item: FeedItem) async {
        var updatedItem = item
        updatedItem.isBookmarked.toggle()
        
        do {
            try await supabaseService.update(
                updatedItem,
                in: "feeds",
                where: "id=eq.\(item.id.uuidString)"
            )
            
            await MainActor.run {
                if let index = self.feedItems.firstIndex(where: { $0.id == item.id }) {
                    self.feedItems[index] = updatedItem
                }
            }
            
        } catch {
            print("Failed to toggle bookmark: \(error)")
        }
    }
    
    func addReaction(_ reaction: Reaction, to item: FeedItem) async {
        var updatedItem = item
        updatedItem.addReaction(reaction)
        
        do {
            try await supabaseService.update(
                updatedItem,
                in: "feeds",
                where: "id=eq.\(item.id.uuidString)"
            )
            
            await MainActor.run {
                if let index = self.feedItems.firstIndex(where: { $0.id == item.id }) {
                    self.feedItems[index] = updatedItem
                }
            }
            
        } catch {
            print("Failed to add reaction: \(error)")
        }
    }
    
    // MARK: - Content Processing
    
    private func processNewsArticle(_ article: NewsArticle, for user: User) async throws -> FeedItem? {
        // Use AI to summarize and categorize
        let summary = try await aiService.summarizeContent(
            article.content,
            maxLength: 200
        )
        
        let tags = try await aiService.generateTags(for: article.content, interests: user.preferences.interests)
        let category = try await aiService.categorizeContent(article.content)
        let vibeScore = try await aiService.calculateVibeScore(article.content, for: user.preferences)
        
        // Generate investment suggestion if relevant
        let investmentSuggestion = try await aiService.generateInvestmentSuggestion(
            based: article.content,
            for: user.preferences
        )
        
        let citation = Citation(
            source: "NewsAPI",
            author: article.author,
            url: article.url,
            publishedAt: article.publishedAt
        )
        
        let content = FeedContent(
            summary: summary,
            tags: tags,
            investmentSuggestion: InvestmentSuggestion(
                amount: 10.0,
                asset: "Suggested Investment",
                symbol: "SPY",
                reasoning: investmentSuggestion,
                riskLevel: .moderate,
                potentialReturn: "5-10% annually"
            ),
            citation: citation,
            category: FeedCategory(rawValue: category) ?? .news,
            readingTime: calculateReadingTime(summary),
            difficulty: .beginner,
            vibeScore: Int(vibeScore)
        )
        
        return FeedItem(userID: user.id, content: content)
    }
    
    private func processSocialPost(_ post: SocialPost, for user: User) async throws -> FeedItem? {
        // Similar processing for social media posts
        let summary = try await aiService.summarizeContent(
            post.content,
            maxLength: 150
        )
        
        let tags = try await aiService.generateTags(for: post.content, interests: user.preferences.interests)
        let category = try await aiService.categorizeContent(post.content)
        let vibeScore = try await aiService.calculateVibeScore(post.content, for: user.preferences)
        
        let citation = Citation(
            source: "X",
            author: "@\(post.username)",
            url: post.url,
            publishedAt: post.createdAt
        )
        
        let content = FeedContent(
            summary: summary,
            tags: tags,
            citation: citation,
            category: FeedCategory(rawValue: category) ?? .news,
            readingTime: 1,
            difficulty: .beginner,
            vibeScore: Int(vibeScore)
        )
        
        return FeedItem(userID: user.id, content: content)
    }
    
    private func calculateReadingTime(_ text: String) -> Int {
        let wordCount = text.split(separator: " ").count
        return max(1, wordCount / 200) // Assuming 200 words per minute
    }
    
    // MARK: - Helper Methods
    
    func clearError() {
        errorMessage = nil
    }
    

    
    func getBookmarkedItems() -> [FeedItem] {
        return feedItems.filter { $0.isBookmarked }
    }
    
    func getUnreadCount() -> Int {
        return feedItems.filter { !$0.isRead }.count
    }
    
    func getItemsByCategory(_ category: FeedCategory) -> [FeedItem] {
        return feedItems.filter { $0.content.category == category }
    }
}

// MARK: - News Models
struct NewsArticle {
    let title: String
    let content: String
    let author: String?
    let url: String
    let publishedAt: Date
}

struct SocialPost {
    let content: String
    let username: String
    let url: String?
    let createdAt: Date
}
