//
//  AnalyticsManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

@MainActor
class AnalyticsManager: ObservableObject {
    @Published var portfolioAnalytics: PortfolioAnalytics?
    @Published var performanceHistory: [PerformanceDataPoint] = []
    @Published var riskMetrics: RiskMetrics?
    @Published var marketSentiment: MarketSentiment?
    @Published var benchmarkComparison: BenchmarkComparison?
    @Published var sectorAnalysis: SectorAnalysis?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private let stockService = StockServiceManager.shared
    private let aiService = GeminiAIService()
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Portfolio Analytics
    
    func generatePortfolioAnalytics(portfolio: RealPortfolio?, holdings: [RealHolding], transactions: [RealTransaction]) async {
        isLoading = true
        errorMessage = nil
        
        do {
            // Calculate comprehensive portfolio metrics
            let analytics = await calculatePortfolioAnalytics(
                portfolio: portfolio,
                holdings: holdings,
                transactions: transactions
            )
            
            // Generate performance history
            let history = await generatePerformanceHistory(transactions: transactions)
            
            // Calculate risk metrics
            let risk = await calculateRiskMetrics(holdings: holdings)
            
            // Analyze market sentiment
            let sentiment = await analyzeMarketSentiment(holdings: holdings)
            
            // Compare with benchmarks
            let benchmark = await generateBenchmarkComparison(analytics: analytics)
            
            // Analyze sector allocation
            let sectors = await analyzeSectorAllocation(holdings: holdings)
            
            await MainActor.run {
                self.portfolioAnalytics = analytics
                self.performanceHistory = history
                self.riskMetrics = risk
                self.marketSentiment = sentiment
                self.benchmarkComparison = benchmark
                self.sectorAnalysis = sectors
                self.isLoading = false
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to generate analytics: \(error.localizedDescription)"
                self.isLoading = false
            }
        }
    }
    
    // MARK: - Portfolio Analytics Calculation
    
    private func calculatePortfolioAnalytics(portfolio: RealPortfolio?, holdings: [RealHolding], transactions: [RealTransaction]) async -> PortfolioAnalytics {
        let totalValue = portfolio?.totalValue ?? 0
        let totalInvested = holdings.reduce(0) { $0 + $1.costBasis }
        let totalGainLoss = holdings.reduce(0) { $0 + $1.unrealizedPL }
        let totalReturn = totalInvested > 0 ? (totalGainLoss / totalInvested) * 100 : 0
        
        // Calculate volatility (simplified)
        let volatility = calculateVolatility(holdings: holdings)
        
        // Calculate Sharpe ratio (simplified)
        let sharpeRatio = calculateSharpeRatio(returns: totalReturn, volatility: volatility)
        
        // Calculate diversification score
        let diversificationScore = calculateDiversificationScore(holdings: holdings)
        
        // Calculate win rate
        let winRate = calculateWinRate(transactions: transactions)
        
        // Calculate average holding period
        let avgHoldingPeriod = calculateAverageHoldingPeriod(transactions: transactions)
        
        return PortfolioAnalytics(
            totalValue: totalValue,
            totalInvested: totalInvested,
            totalGainLoss: totalGainLoss,
            totalReturn: totalReturn,
            volatility: volatility,
            sharpeRatio: sharpeRatio,
            diversificationScore: diversificationScore,
            winRate: winRate,
            averageHoldingPeriod: avgHoldingPeriod,
            numberOfPositions: holdings.count,
            largestPosition: holdings.max { $0.marketValue < $1.marketValue },
            cashPercentage: portfolio?.cash ?? 0 / max(totalValue, 1) * 100
        )
    }
    
    // MARK: - Performance History
    
    private func generatePerformanceHistory(transactions: [RealTransaction]) async -> [PerformanceDataPoint] {
        var history: [PerformanceDataPoint] = []
        var runningValue: Double = 10000 // Starting value
        
        // Group transactions by date
        let calendar = Calendar.current
        let groupedTransactions = Dictionary(grouping: transactions) { transaction in
            calendar.startOfDay(for: transaction.timestamp)
        }
        
        // Generate daily performance points
        let sortedDates = groupedTransactions.keys.sorted()
        
        for date in sortedDates {
            let dayTransactions = groupedTransactions[date] ?? []
            
            // Calculate day's impact
            let dayChange = dayTransactions.reduce(0) { total, transaction in
                switch transaction.side {
                case .buy:
                    return total - transaction.totalValue
                case .sell:
                    return total + transaction.totalValue
                }
            }
            
            runningValue += dayChange
            
            history.append(PerformanceDataPoint(
                date: date,
                portfolioValue: runningValue,
                dayChange: dayChange,
                dayChangePercent: runningValue > 0 ? (dayChange / runningValue) * 100 : 0
            ))
        }
        
        // Fill in missing days with previous value
        return fillMissingDays(history: history)
    }
    
    // MARK: - Risk Metrics
    
    private func calculateRiskMetrics(holdings: [RealHolding]) async -> RiskMetrics {
        let totalValue = holdings.reduce(0) { $0 + $1.marketValue }
        
        // Calculate Value at Risk (simplified)
        let var95 = calculateVaR(holdings: holdings, confidenceLevel: 0.95)
        let var99 = calculateVaR(holdings: holdings, confidenceLevel: 0.99)
        
        // Calculate beta (simplified - would need market data)
        let beta = calculatePortfolioBeta(holdings: holdings)
        
        // Calculate maximum drawdown
        let maxDrawdown = calculateMaxDrawdown(holdings: holdings)
        
        // Calculate concentration risk
        let concentrationRisk = calculateConcentrationRisk(holdings: holdings)
        
        return RiskMetrics(
            valueAtRisk95: var95,
            valueAtRisk99: var99,
            beta: beta,
            maxDrawdown: maxDrawdown,
            concentrationRisk: concentrationRisk,
            riskScore: calculateOverallRiskScore(var95: var95, beta: beta, concentration: concentrationRisk)
        )
    }
    
    // MARK: - Market Sentiment Analysis
    
    private func analyzeMarketSentiment(holdings: [RealHolding]) async -> MarketSentiment {
        var sentimentScores: [String: Double] = [:]
        var newsItems: [SentimentNewsItem] = []
        
        // Analyze sentiment for each holding
        for holding in holdings.prefix(5) { // Limit to top 5 holdings
            do {
                // Get recent news for the symbol
                let news = try await getRecentNews(symbol: holding.symbol)
                
                // Analyze sentiment using AI
                let sentiment = try await analyzeSentimentWithAI(news: news)
                sentimentScores[holding.symbol] = sentiment
                
                // Add to news items
                newsItems.append(contentsOf: news.map { newsItem in
                    SentimentNewsItem(
                        symbol: holding.symbol,
                        headline: newsItem.title,
                        sentiment: sentiment,
                        timestamp: newsItem.publishedAt,
                        source: newsItem.source
                    )
                })
                
            } catch {
                sentimentScores[holding.symbol] = 0.5 // Neutral if analysis fails
            }
        }
        
        // Calculate overall market sentiment
        let overallSentiment = sentimentScores.values.reduce(0, +) / Double(sentimentScores.count)
        
        return MarketSentiment(
            overallSentiment: overallSentiment,
            sentimentBySymbol: sentimentScores,
            sentimentTrend: calculateSentimentTrend(scores: Array(sentimentScores.values)),
            newsItems: newsItems.sorted { $0.timestamp > $1.timestamp }
        )
    }
    
    // MARK: - Benchmark Comparison
    
    private func generateBenchmarkComparison(analytics: PortfolioAnalytics) async -> BenchmarkComparison {
        // Compare against major indices (simplified)
        let spyReturn = 10.5 // S&P 500 annual return (would fetch real data)
        let qqqqReturn = 12.8 // NASDAQ return
        let vtiReturn = 9.8 // Total market return
        
        return BenchmarkComparison(
            portfolioReturn: analytics.totalReturn,
            sp500Return: spyReturn,
            nasdaqReturn: qqqqReturn,
            totalMarketReturn: vtiReturn,
            outperformance: analytics.totalReturn - spyReturn,
            alpha: calculateAlpha(portfolioReturn: analytics.totalReturn, marketReturn: spyReturn, beta: analytics.sharpeRatio),
            trackingError: abs(analytics.totalReturn - spyReturn)
        )
    }
    
    // MARK: - Sector Analysis
    
    private func analyzeSectorAllocation(holdings: [RealHolding]) async -> SectorAnalysis {
        var sectorAllocations: [SectorAllocation] = []
        let totalValue = holdings.reduce(0) { $0 + $1.marketValue }
        
        // Group holdings by sector (simplified mapping)
        let sectorMap = getSectorMapping()
        var sectorValues: [String: Double] = [:]
        
        for holding in holdings {
            let sector = sectorMap[holding.symbol] ?? "Other"
            sectorValues[sector, default: 0] += holding.marketValue
        }
        
        // Create sector allocations
        for (sector, value) in sectorValues {
            let percentage = totalValue > 0 ? (value / totalValue) * 100 : 0
            sectorAllocations.append(SectorAllocation(
                sector: sector,
                value: value,
                percentage: percentage,
                performance: calculateSectorPerformance(sector: sector, holdings: holdings)
            ))
        }
        
        return SectorAnalysis(
            allocations: sectorAllocations.sorted { $0.percentage > $1.percentage },
            mostAllocatedSector: sectorAllocations.max { $0.percentage < $1.percentage }?.sector ?? "Unknown",
            leastAllocatedSector: sectorAllocations.min { $0.percentage < $1.percentage }?.sector ?? "Unknown",
            diversificationRecommendation: generateDiversificationRecommendation(allocations: sectorAllocations)
        )
    }
    
    // MARK: - Helper Calculations
    
    private func calculateVolatility(holdings: [RealHolding]) -> Double {
        // Simplified volatility calculation
        let returns = holdings.map { $0.unrealizedPLPercent }
        let mean = returns.reduce(0, +) / Double(returns.count)
        let variance = returns.map { pow($0 - mean, 2) }.reduce(0, +) / Double(returns.count)
        return sqrt(variance)
    }
    
    private func calculateSharpeRatio(returns: Double, volatility: Double) -> Double {
        let riskFreeRate = 2.0 // 2% risk-free rate
        return volatility > 0 ? (returns - riskFreeRate) / volatility : 0
    }
    
    private func calculateDiversificationScore(holdings: [RealHolding]) -> Double {
        let totalValue = holdings.reduce(0) { $0 + $1.marketValue }
        let concentrations = holdings.map { $0.marketValue / totalValue }
        let herfindahlIndex = concentrations.map { $0 * $0 }.reduce(0, +)
        return max(0, (1 - herfindahlIndex) * 100) // Convert to 0-100 scale
    }
    
    private func calculateWinRate(transactions: [RealTransaction]) -> Double {
        let completedTrades = groupTradesBySymbol(transactions: transactions)
        let winningTrades = completedTrades.filter { $0.profit > 0 }.count
        return completedTrades.count > 0 ? Double(winningTrades) / Double(completedTrades.count) * 100 : 0
    }
    
    private func calculateAverageHoldingPeriod(transactions: [RealTransaction]) -> Double {
        let trades = groupTradesBySymbol(transactions: transactions)
        let holdingPeriods = trades.compactMap { $0.holdingPeriodDays }
        return holdingPeriods.isEmpty ? 0 : holdingPeriods.reduce(0, +) / Double(holdingPeriods.count)
    }
    
    private func calculateVaR(holdings: [RealHolding], confidenceLevel: Double) -> Double {
        // Simplified VaR calculation
        let totalValue = holdings.reduce(0) { $0 + $1.marketValue }
        let volatility = calculateVolatility(holdings: holdings)
        let zScore = confidenceLevel == 0.95 ? 1.645 : 2.326 // 95% or 99%
        return totalValue * (volatility / 100) * zScore
    }
    
    private func calculatePortfolioBeta(holdings: [RealHolding]) -> Double {
        // Simplified beta calculation (would need market correlation data)
        return 1.0 + (holdings.count > 10 ? -0.1 : 0.1) // More diversified = lower beta
    }
    
    private func calculateMaxDrawdown(holdings: [RealHolding]) -> Double {
        // Simplified max drawdown calculation
        let losses = holdings.filter { $0.unrealizedPL < 0 }
        return losses.isEmpty ? 0 : abs(losses.min { $0.unrealizedPLPercent < $1.unrealizedPLPercent }?.unrealizedPLPercent ?? 0)
    }
    
    private func calculateConcentrationRisk(holdings: [RealHolding]) -> Double {
        let totalValue = holdings.reduce(0) { $0 + $1.marketValue }
        let largestPosition = holdings.max { $0.marketValue < $1.marketValue }?.marketValue ?? 0
        return totalValue > 0 ? (largestPosition / totalValue) * 100 : 0
    }
    
    private func calculateOverallRiskScore(var95: Double, beta: Double, concentration: Double) -> Double {
        // Combine risk factors into overall score (0-100, higher = riskier)
        let varScore = min(var95 / 1000 * 100, 100) // Normalize VaR
        let betaScore = abs(beta - 1) * 50 // Distance from market beta
        let concentrationScore = concentration // Already in percentage
        
        return (varScore + betaScore + concentrationScore) / 3
    }
    
    // MARK: - Data Helpers
    
    private func fillMissingDays(history: [PerformanceDataPoint]) -> [PerformanceDataPoint] {
        guard !history.isEmpty else { return [] }
        
        var filledHistory: [PerformanceDataPoint] = []
        let calendar = Calendar.current
        
        let sortedHistory = history.sorted { $0.date < $1.date }
        let startDate = sortedHistory.first!.date
        let endDate = sortedHistory.last!.date
        
        var currentDate = startDate
        var historyIndex = 0
        var lastValue = sortedHistory.first!.portfolioValue
        
        while currentDate <= endDate {
            if historyIndex < sortedHistory.count && calendar.isDate(currentDate, inSameDayAs: sortedHistory[historyIndex].date) {
                filledHistory.append(sortedHistory[historyIndex])
                lastValue = sortedHistory[historyIndex].portfolioValue
                historyIndex += 1
            } else {
                filledHistory.append(PerformanceDataPoint(
                    date: currentDate,
                    portfolioValue: lastValue,
                    dayChange: 0,
                    dayChangePercent: 0
                ))
            }
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate)!
        }
        
        return filledHistory
    }
    
    private func getSectorMapping() -> [String: String] {
        return [
            "AAPL": "Technology",
            "MSFT": "Technology",
            "GOOGL": "Technology",
            "AMZN": "Consumer Discretionary",
            "TSLA": "Consumer Discretionary",
            "NVDA": "Technology",
            "META": "Technology",
            "JPM": "Financial Services",
            "JNJ": "Healthcare",
            "V": "Financial Services"
        ]
    }
    
    private func groupTradesBySymbol(transactions: [RealTransaction]) -> [CompletedTrade] {
        // Group buy/sell transactions to calculate completed trades
        var trades: [CompletedTrade] = []
        // Implementation would match buy/sell pairs
        return trades
    }
    
    private func getRecentNews(symbol: String) async throws -> [NewsItem] {
        // Would integrate with news API
        return []
    }
    
    private func analyzeSentimentWithAI(news: [NewsItem]) async throws -> Double {
        // Would use AI service to analyze sentiment
        return 0.5 // Neutral sentiment
    }
    
    private func calculateSentimentTrend(scores: [Double]) -> String {
        guard scores.count >= 2 else { return "Neutral" }
        let recent = scores.suffix(3).reduce(0, +) / Double(scores.suffix(3).count)
        let older = scores.prefix(3).reduce(0, +) / Double(scores.prefix(3).count)
        
        if recent > older + 0.1 { return "Improving" }
        else if recent < older - 0.1 { return "Declining" }
        else { return "Stable" }
    }
    
    private func calculateAlpha(portfolioReturn: Double, marketReturn: Double, beta: Double) -> Double {
        return portfolioReturn - (2.0 + beta * (marketReturn - 2.0)) // Risk-free rate = 2%
    }
    
    private func calculateSectorPerformance(sector: String, holdings: [RealHolding]) -> Double {
        let sectorHoldings = holdings.filter { getSectorMapping()[$0.symbol] == sector }
        return sectorHoldings.isEmpty ? 0 : sectorHoldings.map { $0.unrealizedPLPercent }.reduce(0, +) / Double(sectorHoldings.count)
    }
    
    private func generateDiversificationRecommendation(allocations: [SectorAllocation]) -> String {
        let maxAllocation = allocations.max { $0.percentage < $1.percentage }?.percentage ?? 0
        
        if maxAllocation > 50 {
            return "Consider reducing concentration in your largest sector allocation"
        } else if allocations.count < 3 {
            return "Consider diversifying across more sectors"
        } else {
            return "Good sector diversification"
        }
    }
}
