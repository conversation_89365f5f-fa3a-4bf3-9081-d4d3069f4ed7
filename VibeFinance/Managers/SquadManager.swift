//
//  SquadManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

@MainActor
class SquadManager: ObservableObject {
    @Published var squads: [Squad] = []
    @Published var userSquads: [Squad] = []
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let supabaseService = SupabaseService.shared
    private let aiService = GeminiAIService()
    private var cancellables = Set<AnyCancellable>()

    // MARK: - Squad Management

    func createSquad(_ squad: Squad) async {
        isLoading = true
        errorMessage = nil

        do {
            // Save squad to database
            try await supabaseService.create(squad, table: "squads")

            // Add creator as first member
            let membership = SquadMembership(
                squadID: squad.id,
                userID: squad.creatorID,
                role: .admin,
                joinedAt: Date()
            )
            try await supabaseService.create(membership, table: "squad_members")

            await MainActor.run {
                self.squads.append(squad)
                self.userSquads.append(squad)
                self.isLoading = false
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to create squad: \(error.localizedDescription)"
                self.isLoading = false
            }
        }
    }

    func joinSquad(_ squadID: UUID) async {
        guard let userID = getCurrentUserID() else { return }

        do {
            let membership = SquadMembership(
                squadID: squadID,
                userID: userID,
                role: .member,
                joinedAt: Date()
            )

            try await supabaseService.create(membership, table: "squad_members")

            // Update local state
            if let squad = squads.first(where: { $0.id == squadID }) {
                await MainActor.run {
                    self.userSquads.append(squad)
                }
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to join squad: \(error.localizedDescription)"
            }
        }
    }

    func leaveSquad(_ squadID: UUID) async {
        guard let userID = getCurrentUserID() else { return }

        do {
            try await supabaseService.delete(
                from: "squad_members",
                where: "squad_id=eq.\(squadID.uuidString) AND user_id=eq.\(userID.uuidString)"
            )

            await MainActor.run {
                self.userSquads.removeAll { $0.id == squadID }
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to leave squad: \(error.localizedDescription)"
            }
        }
    }

    func loadSquads() async {
        isLoading = true
        errorMessage = nil

        do {
            let allSquads: [Squad] = try await supabaseService.fetch(
                Squad.self,
                from: "squads",
                where: "is_active=eq.true"
            )

            await MainActor.run {
                self.squads = allSquads
                self.isLoading = false
            }

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }

    func loadUserSquads() async {
        guard let userID = getCurrentUserID() else { return }

        do {
            // Get user's squad memberships
            let memberships: [SquadMembership] = try await supabaseService.fetch(
                SquadMembership.self,
                from: "squad_members",
                where: "user_id=eq.\(userID.uuidString)"
            )

            // Get squad details for each membership
            var userSquadsList: [Squad] = []
            for membership in memberships {
                if let squad = squads.first(where: { $0.id == membership.squadID }) {
                    userSquadsList.append(squad)
                }
            }

            await MainActor.run {
                self.userSquads = userSquadsList
            }

        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }

    // MARK: - Voting System

    func createInvestmentProposal(_ proposal: InvestmentProposal) async {
        do {
            try await supabaseService.create(proposal, table: "investment_proposals")

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to create proposal: \(error.localizedDescription)"
            }
        }
    }

    func voteOnProposal(_ proposalID: UUID, vote: VoteType) async {
        guard let userID = getCurrentUserID() else { return }

        do {
            let vote = Vote(
                proposalID: proposalID,
                userID: userID,
                voteType: vote,
                timestamp: Date()
            )

            try await supabaseService.create(vote, table: "proposal_votes")

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to submit vote: \(error.localizedDescription)"
            }
        }
    }

    // MARK: - Helper Methods

    private func getCurrentUserID() -> UUID? {
        // Get from auth manager or user manager
        return UUID() // Placeholder
    }
    @Published var userSquads: [Squad] = []
    @Published var publicSquads: [Squad] = []
    @Published var squadInvestments: [UUID: [SquadInvestment]] = [:]
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let supabaseService = SupabaseService.shared
    private let marketService = MarketService.shared
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Squad Management
    
    func loadUserSquads(for userID: UUID) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let squads = try await supabaseService.fetch(
                Squad.self,
                from: "squads",
                where: "members.cs.{\"\(userID.uuidString)\"}"
            )
            
            await MainActor.run {
                self.userSquads = squads
                self.isLoading = false
            }
            
            // Load investments for each squad
            for squad in squads {
                await loadSquadInvestments(squadID: squad.id)
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    func loadPublicSquads() async {
        do {
            let squads = try await supabaseService.fetch(
                Squad.self,
                from: "squads",
                where: "isPublic=eq.true&order=createdAt.desc&limit=20"
            )
            
            await MainActor.run {
                self.publicSquads = squads
            }
            
        } catch {
            print("Failed to load public squads: \(error)")
        }
    }
    
    func createSquad(name: String, description: String, emoji: String, creatorID: UUID, isPublic: Bool = true) async {
        let squad = Squad(
            name: name,
            description: description,
            emoji: emoji,
            creatorID: creatorID,
            isPublic: isPublic
        )
        
        do {
            try await supabaseService.create(squad, table: "squads")
            
            await MainActor.run {
                self.userSquads.append(squad)
                if isPublic {
                    self.publicSquads.insert(squad, at: 0)
                }
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    func joinSquad(_ squad: Squad, userID: UUID, username: String) async {
        guard !squad.isFull else {
            errorMessage = "Squad is full"
            return
        }
        
        var updatedSquad = squad
        let member = SquadMember(userID: userID, username: username)
        updatedSquad.addMember(member)
        
        do {
            try await supabaseService.update(
                updatedSquad,
                in: "squads",
                where: "id=eq.\(squad.id.uuidString)"
            )
            
            await MainActor.run {
                if let index = self.userSquads.firstIndex(where: { $0.id == squad.id }) {
                    self.userSquads[index] = updatedSquad
                }
                if let index = self.publicSquads.firstIndex(where: { $0.id == squad.id }) {
                    self.publicSquads[index] = updatedSquad
                }
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    func leaveSquad(_ squad: Squad, userID: UUID) async {
        var updatedSquad = squad
        updatedSquad.removeMember(userID)
        
        do {
            try await supabaseService.update(
                updatedSquad,
                in: "squads",
                where: "id=eq.\(squad.id.uuidString)"
            )
            
            await MainActor.run {
                self.userSquads.removeAll { $0.id == squad.id }
                if let index = self.publicSquads.firstIndex(where: { $0.id == squad.id }) {
                    self.publicSquads[index] = updatedSquad
                }
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    // MARK: - Investment Management
    
    func loadSquadInvestments(squadID: UUID) async {
        do {
            let investments = try await supabaseService.fetch(
                SquadInvestment.self,
                from: "squad_investments",
                where: "squadID=eq.\(squadID.uuidString)&order=createdAt.desc"
            )
            
            await MainActor.run {
                self.squadInvestments[squadID] = investments
            }
            
        } catch {
            print("Failed to load squad investments: \(error)")
        }
    }
    
    func proposeInvestment(
        squadID: UUID,
        symbol: String,
        name: String,
        amount: Double,
        proposedBy: UUID
    ) async {
        do {
            // Get current market price
            let marketData = try await marketService.getQuote(symbol: symbol)
            let shares = amount / marketData.price
            
            let investment = SquadInvestment(
                squadID: squadID,
                symbol: symbol,
                name: name,
                amount: amount,
                shares: shares,
                pricePerShare: marketData.price,
                proposedBy: proposedBy
            )
            
            try await supabaseService.create(investment, table: "squad_investments")
            
            await MainActor.run {
                if self.squadInvestments[squadID] == nil {
                    self.squadInvestments[squadID] = []
                }
                self.squadInvestments[squadID]?.insert(investment, at: 0)
            }
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    func voteOnInvestment(
        investmentID: UUID,
        vote: VoteType,
        amount: Double,
        userID: UUID
    ) async {
        let voteRecord = InvestmentVote(
            userID: userID,
            investmentID: investmentID,
            vote: vote,
            amount: amount
        )
        
        do {
            try await supabaseService.create(voteRecord, table: "investment_votes")
            
            // Update investment with new vote
            // This would require fetching the investment and updating it
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    func executeInvestment(_ investment: SquadInvestment) async {
        var updatedInvestment = investment
        updatedInvestment.status = .executed
        updatedInvestment.executedAt = Date()
        
        do {
            try await supabaseService.update(
                updatedInvestment,
                in: "squad_investments",
                where: "id=eq.\(investment.id.uuidString)"
            )
            
            // Update squad total values
            await updateSquadValues(squadID: investment.squadID)
            
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
            }
        }
    }
    
    private func updateSquadValues(squadID: UUID) async {
        guard let squad = userSquads.first(where: { $0.id == squadID }),
              let investments = squadInvestments[squadID] else { return }
        
        var updatedSquad = squad
        
        // Calculate total invested and current value
        let executedInvestments = investments.filter { $0.status == .executed }
        updatedSquad.totalInvested = executedInvestments.reduce(0) { $0 + $1.amount }
        
        // Get current prices and calculate total value
        var totalValue = 0.0
        for investment in executedInvestments {
            do {
                let currentData = try await marketService.getQuote(symbol: investment.symbol)
                totalValue += investment.shares * currentData.price
            } catch {
                // Use last known price if current price unavailable
                totalValue += investment.currentValue
            }
        }
        
        updatedSquad.totalValue = totalValue
        
        do {
            try await supabaseService.update(
                updatedSquad,
                in: "squads",
                where: "id=eq.\(squadID.uuidString)"
            )
            
            await MainActor.run {
                if let index = self.userSquads.firstIndex(where: { $0.id == squadID }) {
                    self.userSquads[index] = updatedSquad
                }
            }
            
        } catch {
            print("Failed to update squad values: \(error)")
        }
    }
    
    // MARK: - Helper Methods
    
    func getSquadInvestments(for squadID: UUID) -> [SquadInvestment] {
        return squadInvestments[squadID] ?? []
    }
    
    func getUserSquadRole(squadID: UUID, userID: UUID) -> SquadRole? {
        guard let squad = userSquads.first(where: { $0.id == squadID }),
              let member = squad.members.first(where: { $0.userID == userID }) else {
            return nil
        }
        return member.role
    }
    
    func canUserPerformAction(_ action: SquadPermission, squadID: UUID, userID: UUID) -> Bool {
        guard let squad = userSquads.first(where: { $0.id == squadID }) else { return false }
        return squad.canUserPerform(action, userID: userID)
    }
    
    func clearError() {
        errorMessage = nil
    }
    
    func refreshSquads(for userID: UUID) async {
        userSquads.removeAll()
        squadInvestments.removeAll()
        await loadUserSquads(for: userID)
        await loadPublicSquads()
    }
}
