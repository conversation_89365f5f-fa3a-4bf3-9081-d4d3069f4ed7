//
//  SubscriptionManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import StoreKit
import Combine

@MainActor
class SubscriptionManager: ObservableObject {
    @Published var availableProducts: [Product] = []
    @Published var purchasedProducts: [Product] = []
    @Published var subscriptionStatus: SubscriptionTier = .free
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let productIDs = [
        "com.wealthvibe.basic.monthly",
        "com.wealthvibe.pro.monthly"
    ]

    private var updateListenerTask: Task<Void, Error>?

    init() {
        updateListenerTask = listenForTransactions()
    }

    deinit {
        updateListenerTask?.cancel()
    }

    // MARK: - Product Loading

    func loadProducts() async {
        isLoading = true
        errorMessage = nil

        do {
            let products = try await Product.products(for: productIDs)

            await MainActor.run {
                self.availableProducts = products.sorted { $0.price < $1.price }
                self.isLoading = false
            }

            await updateSubscriptionStatus()

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to load subscription options"
                self.isLoading = false
            }
        }
    }

    // MARK: - Purchase Handling

    func purchase(_ product: Product) async {
        isLoading = true
        errorMessage = nil

        do {
            let result = try await product.purchase()

            switch result {
            case .success(let verification):
                let transaction = try checkVerified(verification)
                await updateSubscriptionStatus()
                await transaction.finish()

                await MainActor.run {
                    self.isLoading = false
                }

            case .userCancelled:
                await MainActor.run {
                    self.isLoading = false
                }

            case .pending:
                await MainActor.run {
                    self.errorMessage = "Purchase is pending approval"
                    self.isLoading = false
                }

            @unknown default:
                await MainActor.run {
                    self.errorMessage = "Unknown purchase result"
                    self.isLoading = false
                }
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Purchase failed: \(error.localizedDescription)"
                self.isLoading = false
            }
        }
    }

    func restorePurchases() async {
        isLoading = true
        errorMessage = nil

        do {
            try await AppStore.sync()
            await updateSubscriptionStatus()

            await MainActor.run {
                self.isLoading = false
            }

        } catch {
            await MainActor.run {
                self.errorMessage = "Failed to restore purchases"
                self.isLoading = false
            }
        }
    }

    // MARK: - Subscription Status

    func updateSubscriptionStatus() async {
        var currentSubscriptions: [Product] = []

        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)

                if let product = availableProducts.first(where: { $0.id == transaction.productID }) {
                    currentSubscriptions.append(product)
                }
            } catch {
                print("Failed to verify transaction: \(error)")
            }
        }

        await MainActor.run {
            self.purchasedProducts = currentSubscriptions
            self.subscriptionStatus = self.determineSubscriptionTier(from: currentSubscriptions)
        }
    }

    private func determineSubscriptionTier(from products: [Product]) -> SubscriptionTier {
        if products.contains(where: { $0.id == "com.wealthvibe.pro.monthly" }) {
            return .pro
        } else if products.contains(where: { $0.id == "com.wealthvibe.basic.monthly" }) {
            return .basic
        } else {
            return .free
        }
    }

    // MARK: - Transaction Verification

    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        switch result {
        case .unverified:
            throw SubscriptionError.failedVerification
        case .verified(let safe):
            return safe
        }
    }

    private func listenForTransactions() -> Task<Void, Error> {
        return Task {
            for await result in Transaction.updates {
                do {
                    let transaction = try checkVerified(result)
                    await updateSubscriptionStatus()
                    await transaction.finish()
                } catch {
                    print("Transaction failed verification: \(error)")
                }
            }
        }
    }

    // MARK: - Subscription Benefits

    func getSubscriptionBenefits(for tier: SubscriptionTier) -> [String] {
        return tier.features
    }

    func canAccessFeature(_ feature: FeatureAccess) -> Bool {
        switch feature {
        case .unlimitedFeed:
            return subscriptionStatus != .free
        case .allQuests:
            return subscriptionStatus != .free
        case .fullChat:
            return subscriptionStatus != .free
        case .squads:
            return subscriptionStatus == .pro
        case .simulator:
            return subscriptionStatus == .pro
        case .priorityChat:
            return subscriptionStatus == .pro
        case .realInvestments:
            return subscriptionStatus == .pro
        }
    }

    func getFeatureLimit(for feature: FeatureAccess) -> Int? {
        switch feature {
        case .unlimitedFeed:
            return subscriptionStatus == .free ? 3 : nil
        default:
            return nil
        }
    }

    // MARK: - Pricing

    func getFormattedPrice(for productID: String) -> String {
        guard let product = availableProducts.first(where: { $0.id == productID }) else {
            return "N/A"
        }
        return product.displayPrice
    }

    func getProduct(for tier: SubscriptionTier) -> Product? {
        switch tier {
        case .basic:
            return availableProducts.first { $0.id == "com.wealthvibe.basic.monthly" }
        case .pro:
            return availableProducts.first { $0.id == "com.wealthvibe.pro.monthly" }
        case .free:
            return nil
        }
    }

    // MARK: - Subscription Management

    func manageSubscriptions() {
        Task {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                try? await AppStore.showManageSubscriptions(in: windowScene)
            }
        }
    }

    func cancelSubscription() async {
        // This would typically redirect to App Store subscription management
        // or handle cancellation through the app if supported
        manageSubscriptions()
    }

    // MARK: - Helper Methods

    func clearError() {
        errorMessage = nil
    }

    func isSubscribed(to tier: SubscriptionTier) -> Bool {
        switch tier {
        case .free:
            return subscriptionStatus == .free
        case .basic:
            return subscriptionStatus == .basic || subscriptionStatus == .pro
        case .pro:
            return subscriptionStatus == .pro
        }
    }

    func getUpgradeRecommendation() -> SubscriptionTier? {
        switch subscriptionStatus {
        case .free:
            return .basic
        case .basic:
            return .pro
        case .pro:
            return nil
        }
    }

    // MARK: - Revenue Tracking

    func trackRevenue(for transaction: Transaction) {
        // This would typically send analytics events for revenue tracking
        print("Revenue tracked: \(transaction.productID)")
    }

    func getMonthlyRevenue() -> Double {
        // This would calculate estimated monthly revenue based on active subscriptions
        // For the target of $2M monthly revenue:
        // 150,000 users × $9.99 = $1,498,500
        // 25,000 users × $19.99 = $499,750
        // Total = $1,998,250 ≈ $2M

        let basicUsers = 150_000
        let proUsers = 25_000

        return Double(basicUsers) * 9.99 + Double(proUsers) * 19.99
    }
}

// MARK: - Subscription Errors
enum SubscriptionError: LocalizedError {
    case failedVerification
    case purchaseFailed
    case restoreFailed
    case productNotFound

    var errorDescription: String? {
        switch self {
        case .failedVerification:
            return "Failed to verify purchase"
        case .purchaseFailed:
            return "Purchase failed"
        case .restoreFailed:
            return "Failed to restore purchases"
        case .productNotFound:
            return "Product not found"
        }
    }
}
