//
//  ImageCacheManager.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import UIKit
import Combine

class ImageCacheManager: NSObject, ObservableObject {
    static let shared = ImageCacheManager()
    
    private let memoryCache = NSCache<NSString, UIImage>()
    private let diskCache: ImageDiskCache
    private let downloadQueue = DispatchQueue(label: "image.download", qos: .utility, attributes: .concurrent)
    private let processingQueue = DispatchQueue(label: "image.processing", qos: .utility)
    
    private var activeDownloads: [URL: Task<UIImage?, Error>] = [:]
    private var imageStatistics = ImageCacheStatistics()
    private var isDebugLoggingEnabled = false
    private var isLowMemoryMode = false
    
    // Configuration
    private let maxMemoryCacheSize: Int = 100 * 1024 * 1024 // 100MB
    private let maxDiskCacheSize: Int = 500 * 1024 * 1024 // 500MB
    private let compressionQuality: CGFloat = 0.8
    private let maxImageDimension: CGFloat = 1024
    
    private override init() {
        diskCache = ImageDiskCache()
        super.init()
        setupMemoryCache()
        setupMemoryWarningObserver()
    }
    
    // MARK: - Setup
    
    private func setupMemoryCache() {
        memoryCache.totalCostLimit = maxMemoryCacheSize
        memoryCache.countLimit = 200
        memoryCache.delegate = self
    }
    
    private func setupMemoryWarningObserver() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }
    
    @objc private func handleMemoryWarning() {
        clearMemoryCache()
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Memory warning - cleared image memory cache", category: "IMAGE_CACHE")
        }
    }
    
    // MARK: - Image Loading
    
    func loadImage(from url: URL, size: CGSize? = nil) async -> UIImage? {
        let cacheKey = generateCacheKey(for: url, size: size)
        
        // Check memory cache first
        if let cachedImage = memoryCache.object(forKey: NSString(string: cacheKey)) {
            imageStatistics.memoryHits += 1
            if isDebugLoggingEnabled {
                DevelopmentConfig.log("Memory cache hit for: \(url.lastPathComponent)", category: "IMAGE_CACHE")
            }
            return cachedImage
        }
        
        // Check disk cache
        if let diskImage = await diskCache.getImage(forKey: cacheKey) {
            // Store in memory cache for faster access
            let cost = Int(diskImage.size.width * diskImage.size.height * 4) // Estimate memory cost
            memoryCache.setObject(diskImage, forKey: NSString(string: cacheKey), cost: cost)
            
            imageStatistics.diskHits += 1
            if isDebugLoggingEnabled {
                DevelopmentConfig.log("Disk cache hit for: \(url.lastPathComponent)", category: "IMAGE_CACHE")
            }
            return diskImage
        }
        
        // Check if download is already in progress
        if let existingTask = activeDownloads[url] {
            return try? await existingTask.value
        }
        
        // Download image
        let downloadTask = Task<UIImage?, Error> {
            return await downloadAndProcessImage(from: url, targetSize: size, cacheKey: cacheKey)
        }
        
        activeDownloads[url] = downloadTask
        
        do {
            let image = try await downloadTask.value
            activeDownloads.removeValue(forKey: url)
            return image
        } catch {
            activeDownloads.removeValue(forKey: url)
            imageStatistics.misses += 1
            if isDebugLoggingEnabled {
                DevelopmentConfig.log("Failed to load image from: \(url) - \(error)", category: "IMAGE_CACHE")
            }
            return nil
        }
    }
    
    private func downloadAndProcessImage(from url: URL, targetSize: CGSize?, cacheKey: String) async -> UIImage? {
        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            
            guard let image = UIImage(data: data) else {
                imageStatistics.misses += 1
                return nil
            }
            
            // Process image (resize, compress)
            let processedImage = await processImage(image, targetSize: targetSize)
            
            // Cache the processed image
            await cacheImage(processedImage, forKey: cacheKey)
            
            imageStatistics.downloads += 1
            if isDebugLoggingEnabled {
                DevelopmentConfig.log("Downloaded and cached: \(url.lastPathComponent)", category: "IMAGE_CACHE")
            }
            
            return processedImage
            
        } catch {
            imageStatistics.misses += 1
            return nil
        }
    }
    
    // MARK: - Image Processing
    
    private func processImage(_ image: UIImage, targetSize: CGSize?) async -> UIImage {
        return await withCheckedContinuation { continuation in
            processingQueue.async {
                var processedImage = image
                
                // Resize if needed
                if let targetSize = targetSize {
                    processedImage = self.resizeImage(processedImage, to: targetSize)
                } else if max(image.size.width, image.size.height) > self.maxImageDimension {
                    let aspectRatio = image.size.width / image.size.height
                    let newSize: CGSize
                    
                    if image.size.width > image.size.height {
                        newSize = CGSize(width: self.maxImageDimension, height: self.maxImageDimension / aspectRatio)
                    } else {
                        newSize = CGSize(width: self.maxImageDimension * aspectRatio, height: self.maxImageDimension)
                    }
                    
                    processedImage = self.resizeImage(processedImage, to: newSize)
                }
                
                // Compress if needed
                if !self.isLowMemoryMode {
                    processedImage = self.compressImage(processedImage) ?? processedImage
                }
                
                continuation.resume(returning: processedImage)
            }
        }
    }
    
    private func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: size))
        }
    }
    
    private func compressImage(_ image: UIImage) -> UIImage? {
        guard let data = image.jpegData(compressionQuality: compressionQuality),
              let compressedImage = UIImage(data: data) else {
            return nil
        }
        return compressedImage
    }
    
    // MARK: - Caching
    
    private func cacheImage(_ image: UIImage, forKey key: String) async {
        // Cache in memory
        let cost = Int(image.size.width * image.size.height * 4)
        memoryCache.setObject(image, forKey: NSString(string: key), cost: cost)
        
        // Cache on disk (async)
        await diskCache.setImage(image, forKey: key)
        
        imageStatistics.totalCached += 1
    }
    
    private func generateCacheKey(for url: URL, size: CGSize?) -> String {
        var key = url.absoluteString
        if let size = size {
            key += "_\(Int(size.width))x\(Int(size.height))"
        }
        return key.addingPercentEncoding(withAllowedCharacters: .urlPathAllowed) ?? key
    }
    
    // MARK: - Cache Management
    
    func clearMemoryCache() {
        memoryCache.removeAllObjects()
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Cleared image memory cache", category: "IMAGE_CACHE")
        }
    }
    
    func clearDiskCache() async {
        await diskCache.clearAll()
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Cleared image disk cache", category: "IMAGE_CACHE")
        }
    }
    
    func clearAllCache() async {
        clearMemoryCache()
        await clearDiskCache()
        imageStatistics.reset()
    }
    
    func reduceMemoryFootprint() {
        // Reduce memory cache size
        memoryCache.totalCostLimit = maxMemoryCacheSize / 2
        memoryCache.countLimit = 100
        
        // Clear some cached images
        clearMemoryCache()
        
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Reduced image cache memory footprint", category: "IMAGE_CACHE")
        }
    }
    
    func enableLowMemoryMode() {
        isLowMemoryMode = true
        reduceMemoryFootprint()
        
        DevelopmentConfig.log("Image cache low memory mode enabled", category: "IMAGE_CACHE")
    }
    
    func disableLowMemoryMode() {
        isLowMemoryMode = false
        memoryCache.totalCostLimit = maxMemoryCacheSize
        memoryCache.countLimit = 200
        
        DevelopmentConfig.log("Image cache low memory mode disabled", category: "IMAGE_CACHE")
    }
    
    // MARK: - Preloading
    
    func preloadImages(urls: [URL], sizes: [CGSize] = []) async {
        let tasks = urls.enumerated().map { index, url in
            let size = sizes.indices.contains(index) ? sizes[index] : nil
            return Task {
                await loadImage(from: url, size: size)
            }
        }
        
        // Wait for all preloading tasks to complete
        for task in tasks {
            _ = await task.value
        }
        
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Preloaded \(urls.count) images", category: "IMAGE_CACHE")
        }
    }
    
    // MARK: - Statistics and Monitoring
    
    func getStatistics() -> ImageCacheStatistics {
        imageStatistics.memoryCacheSize = memoryCache.totalCostLimit
        imageStatistics.diskCacheSize = diskCache.getCurrentSize()
        imageStatistics.activeDownloads = activeDownloads.count
        return imageStatistics
    }
    
    func getHitRate() -> Double {
        let totalRequests = imageStatistics.memoryHits + imageStatistics.diskHits + imageStatistics.misses
        guard totalRequests > 0 else { return 0.0 }
        
        let totalHits = imageStatistics.memoryHits + imageStatistics.diskHits
        return Double(totalHits) / Double(totalRequests)
    }
    
    // MARK: - Debug and Development
    
    func enableDebugLogging() {
        isDebugLoggingEnabled = true
    }
    
    func disableDebugLogging() {
        isDebugLoggingEnabled = false
    }
    
    func printStatistics() {
        guard DevelopmentConfig.isDevelopmentMode else { return }
        
        let stats = getStatistics()
        let hitRate = getHitRate()
        
        print("""
        🖼️ Image Cache Statistics:
        Hit Rate: \(String(format: "%.1f", hitRate * 100))%
        Memory Hits: \(stats.memoryHits)
        Disk Hits: \(stats.diskHits)
        Misses: \(stats.misses)
        Downloads: \(stats.downloads)
        Total Cached: \(stats.totalCached)
        Active Downloads: \(stats.activeDownloads)
        Memory Cache: \(stats.memoryCacheSize / 1024 / 1024)MB
        Disk Cache: \(stats.diskCacheSize / 1024 / 1024)MB
        """)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - NSCacheDelegate
extension ImageCacheManager: NSCacheDelegate {
    func cache(_ cache: NSCache<AnyObject, AnyObject>, willEvictObject obj: AnyObject) {
        if isDebugLoggingEnabled {
            DevelopmentConfig.log("Image memory cache evicting object", category: "IMAGE_CACHE")
        }
    }
}

// MARK: - Image Cache Statistics
struct ImageCacheStatistics: Codable {
    var memoryHits: Int = 0
    var diskHits: Int = 0
    var misses: Int = 0
    var downloads: Int = 0
    var totalCached: Int = 0
    var activeDownloads: Int = 0
    var memoryCacheSize: Int = 0
    var diskCacheSize: Int = 0
    
    mutating func reset() {
        memoryHits = 0
        diskHits = 0
        misses = 0
        downloads = 0
        totalCached = 0
        activeDownloads = 0
        memoryCacheSize = 0
        diskCacheSize = 0
    }
}

// MARK: - Image Disk Cache
class ImageDiskCache {
    private let cacheDirectory: URL
    private let fileManager = FileManager.default
    private let maxDiskCacheSize: Int = 500 * 1024 * 1024 // 500MB
    
    init() {
        let cachesDirectory = fileManager.urls(for: .cachesDirectory, in: .userDomainMask).first!
        cacheDirectory = cachesDirectory.appendingPathComponent("VibeFinanceImageCache")
        
        try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
    }
    
    func setImage(_ image: UIImage, forKey key: String) async {
        let url = cacheDirectory.appendingPathComponent(key)
        
        guard let data = image.jpegData(compressionQuality: 0.8) else { return }
        
        do {
            try data.write(to: url)
        } catch {
            DevelopmentConfig.log("Failed to write image to disk cache: \(error)", category: "IMAGE_CACHE")
        }
    }
    
    func getImage(forKey key: String) async -> UIImage? {
        let url = cacheDirectory.appendingPathComponent(key)
        
        guard fileManager.fileExists(atPath: url.path) else { return nil }
        
        do {
            let data = try Data(contentsOf: url)
            return UIImage(data: data)
        } catch {
            return nil
        }
    }
    
    func removeImage(forKey key: String) {
        let url = cacheDirectory.appendingPathComponent(key)
        try? fileManager.removeItem(at: url)
    }
    
    func clearAll() async {
        try? fileManager.removeItem(at: cacheDirectory)
        try? fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
    }
    
    func getCurrentSize() -> Int {
        guard let urls = try? fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.fileSizeKey]) else { return 0 }
        
        return urls.compactMap { url in
            try? url.resourceValues(forKeys: [.fileSizeKey]).fileSize
        }.reduce(0, +)
    }
    
    func cleanupIfNeeded() {
        let currentSize = getCurrentSize()
        
        if currentSize > maxDiskCacheSize {
            // Remove oldest files to free up space
            guard let urls = try? fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: [.contentModificationDateKey]) else { return }
            
            let sortedUrls = urls.sorted { url1, url2 in
                let date1 = (try? url1.resourceValues(forKeys: [.contentModificationDateKey]).contentModificationDate) ?? Date.distantPast
                let date2 = (try? url2.resourceValues(forKeys: [.contentModificationDateKey]).contentModificationDate) ?? Date.distantPast
                return date1 < date2
            }
            
            var sizeToRemove = currentSize - (maxDiskCacheSize * 3 / 4) // Remove to 75% capacity
            
            for url in sortedUrls {
                if sizeToRemove <= 0 { break }
                
                if let fileSize = try? url.resourceValues(forKeys: [.fileSizeKey]).fileSize {
                    try? fileManager.removeItem(at: url)
                    sizeToRemove -= fileSize
                }
            }
            
            DevelopmentConfig.log("Cleaned up image disk cache - removed \((currentSize - getCurrentSize()) / 1024 / 1024)MB", category: "IMAGE_CACHE")
        }
    }
}
