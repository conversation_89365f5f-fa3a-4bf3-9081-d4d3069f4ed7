//
//  InvisiblePerformanceEngine.swift
//  VibeFinance - Behind-the-Scenes Performance Intelligence
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import Foundation
import SwiftUI
import Combine

// MARK: - Invisible Performance Engine

@MainActor
class InvisiblePerformanceEngine: ObservableObject {
    static let shared = InvisiblePerformanceEngine()
    
    // Simple user-facing status (only shown when relevant)
    @Published var optimizationStatus: OptimizationStatus = .optimal
    @Published var shouldShowStatusIndicator: Bool = false
    @Published var contextualMessage: String = ""
    
    // Internal optimization components
    private let smartOptimizer = SmartOptimizer()
    private let batteryIntelligence = BatteryIntelligence()
    private let thermalIntelligence = ThermalIntelligence()
    private let networkIntelligence = NetworkIntelligence()
    private let memoryIntelligence = MemoryIntelligence()
    
    // Monitoring and adaptation
    private var adaptiveTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    private var lastOptimizationTime = Date()
    
    private init() {
        setupInvisibleMonitoring()
        startAdaptiveOptimization()
    }
    
    // MARK: - Invisible Setup
    
    private func setupInvisibleMonitoring() {
        // Monitor system conditions silently
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                self?.handleMemoryPressure()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: ProcessInfo.thermalStateDidChangeNotification)
            .sink { [weak self] _ in
                self?.handleThermalChange()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIDevice.batteryStateDidChangeNotification)
            .sink { [weak self] _ in
                self?.handleBatteryChange()
            }
            .store(in: &cancellables)
        
        // Monitor app lifecycle for optimization opportunities
        NotificationCenter.default.publisher(for: UIApplication.didEnterBackgroundNotification)
            .sink { [weak self] _ in
                self?.optimizeForBackground()
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)
            .sink { [weak self] _ in
                self?.optimizeForForeground()
            }
            .store(in: &cancellables)
    }
    
    private func startAdaptiveOptimization() {
        // Intelligent optimization every 30 seconds (invisible to user)
        adaptiveTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.performIntelligentOptimization()
        }
    }
    
    // MARK: - Intelligent Optimization
    
    private func performIntelligentOptimization() {
        let systemConditions = gatherSystemConditions()
        let optimizationDecision = smartOptimizer.analyzeAndDecide(conditions: systemConditions)
        
        applyOptimizationsInvisibly(decision: optimizationDecision)
        updateUserStatusIfNeeded(decision: optimizationDecision)
    }
    
    private func gatherSystemConditions() -> SystemConditions {
        return SystemConditions(
            memoryPressure: memoryIntelligence.getCurrentPressure(),
            batteryLevel: batteryIntelligence.getCurrentLevel(),
            thermalState: thermalIntelligence.getCurrentState(),
            networkQuality: networkIntelligence.getCurrentQuality(),
            appUsagePattern: detectCurrentUsagePattern(),
            timeOfDay: Calendar.current.component(.hour, from: Date())
        )
    }
    
    private func detectCurrentUsagePattern() -> AppUsagePattern {
        // Intelligent detection of how user is currently using the app
        let currentView = getCurrentActiveView()
        
        switch currentView {
        case "trading", "charts":
            return .activeTradingSession
        case "analytics", "portfolio":
            return .dataAnalysis
        case "feed", "news":
            return .casualBrowsing
        default:
            return .general
        }
    }
    
    private func getCurrentActiveView() -> String {
        // This would integrate with navigation tracking
        return "general" // Placeholder
    }
    
    // MARK: - Invisible Optimization Application
    
    private func applyOptimizationsInvisibly(decision: OptimizationDecision) {
        // Apply optimizations without user awareness
        
        // Memory optimization
        if decision.shouldOptimizeMemory {
            memoryIntelligence.optimizeInBackground()
        }
        
        // Battery optimization
        if decision.shouldOptimizeBattery {
            batteryIntelligence.optimizeInBackground()
        }
        
        // Thermal optimization
        if decision.shouldOptimizeThermal {
            thermalIntelligence.optimizeInBackground()
        }
        
        // Network optimization
        if decision.shouldOptimizeNetwork {
            networkIntelligence.optimizeInBackground()
        }
        
        // Rendering optimization
        if decision.shouldOptimizeRendering {
            optimizeRenderingInBackground(level: decision.renderingOptimizationLevel)
        }
    }
    
    private func optimizeRenderingInBackground(level: RenderingOptimizationLevel) {
        switch level {
        case .subtle:
            // Barely noticeable optimizations
            reduceAnimationComplexity(by: 0.1)
            optimizeGradientRendering()
        case .moderate:
            // Noticeable but not jarring optimizations
            reduceAnimationComplexity(by: 0.3)
            simplifyChartRendering()
        case .aggressive:
            // Significant but temporary optimizations
            reduceAnimationComplexity(by: 0.5)
            enableLowPowerRendering()
        }
    }
    
    // MARK: - User Status Updates (Contextual & Minimal)
    
    private func updateUserStatusIfNeeded(decision: OptimizationDecision) {
        // Only show status to user when it's beneficial or necessary
        
        if decision.severity == .critical {
            showCriticalOptimizationStatus(decision)
        } else if decision.severity == .significant && shouldInformUser() {
            showHelpfulOptimizationHint(decision)
        } else {
            hideStatusIndicator()
        }
    }
    
    private func showCriticalOptimizationStatus(_ decision: OptimizationDecision) {
        optimizationStatus = .optimizing
        shouldShowStatusIndicator = true
        
        if decision.primaryCause == .lowBattery {
            contextualMessage = "Optimizing for battery life"
        } else if decision.primaryCause == .thermalPressure {
            contextualMessage = "Cooling down for optimal performance"
        } else if decision.primaryCause == .memoryPressure {
            contextualMessage = "Optimizing for smooth performance"
        } else {
            contextualMessage = "Optimizing experience"
        }
        
        // Auto-hide after optimization completes
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            self.hideStatusIndicator()
        }
    }
    
    private func showHelpfulOptimizationHint(_ decision: OptimizationDecision) {
        // Show subtle, helpful hints only when user would benefit
        optimizationStatus = .optimized
        shouldShowStatusIndicator = true
        
        if decision.primaryCause == .poorNetwork {
            contextualMessage = "Using cached data for faster loading"
        } else if decision.primaryCause == .backgroundOptimization {
            contextualMessage = "Performance optimized"
        }
        
        // Auto-hide hint after 3 seconds
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            self.hideStatusIndicator()
        }
    }
    
    private func hideStatusIndicator() {
        shouldShowStatusIndicator = false
        contextualMessage = ""
        optimizationStatus = .optimal
    }
    
    private func shouldInformUser() -> Bool {
        // Only inform user if:
        // 1. Last notification was more than 10 minutes ago
        // 2. Optimization will be noticeable
        // 3. User is actively using the app
        
        let timeSinceLastNotification = Date().timeIntervalSince(lastOptimizationTime)
        return timeSinceLastNotification > 600 // 10 minutes
    }
    
    // MARK: - Event Handlers
    
    private func handleMemoryPressure() {
        memoryIntelligence.handleEmergency()
        
        // Show brief status only if severe
        if memoryIntelligence.getCurrentPressure() == .severe {
            showCriticalOptimizationStatus(
                OptimizationDecision(
                    severity: .critical,
                    primaryCause: .memoryPressure,
                    shouldOptimizeMemory: true
                )
            )
        }
    }
    
    private func handleThermalChange() {
        let thermalState = ProcessInfo.processInfo.thermalState
        
        if thermalState == .serious || thermalState == .critical {
            thermalIntelligence.handleThermalPressure()
            
            showCriticalOptimizationStatus(
                OptimizationDecision(
                    severity: .critical,
                    primaryCause: .thermalPressure,
                    shouldOptimizeThermal: true
                )
            )
        }
    }
    
    private func handleBatteryChange() {
        let batteryLevel = UIDevice.current.batteryLevel
        
        if batteryLevel < 0.15 && UIDevice.current.batteryState != .charging {
            batteryIntelligence.enableEmergencyMode()
            
            showCriticalOptimizationStatus(
                OptimizationDecision(
                    severity: .critical,
                    primaryCause: .lowBattery,
                    shouldOptimizeBattery: true
                )
            )
        }
    }
    
    private func optimizeForBackground() {
        // Aggressive optimization when app goes to background
        memoryIntelligence.optimizeForBackground()
        batteryIntelligence.enableBackgroundMode()
        networkIntelligence.pauseNonEssentialRequests()
        
        ProductionConfig.log("📱 Background optimization applied", category: "PERFORMANCE", level: .info)
    }
    
    private func optimizeForForeground() {
        // Restore optimal performance when app returns to foreground
        memoryIntelligence.optimizeForForeground()
        batteryIntelligence.resumeNormalMode()
        networkIntelligence.resumeNormalRequests()
        
        ProductionConfig.log("📱 Foreground optimization applied", category: "PERFORMANCE", level: .info)
    }
    
    // MARK: - Rendering Optimizations
    
    private func reduceAnimationComplexity(by factor: Double) {
        let newDuration = 0.3 * (1.0 - factor)
        UserDefaults.standard.set(newDuration, forKey: "OptimizedAnimationDuration")
    }
    
    private func optimizeGradientRendering() {
        UserDefaults.standard.set(6, forKey: "OptimizedGradientStops")
    }
    
    private func simplifyChartRendering() {
        UserDefaults.standard.set(true, forKey: "SimplifiedChartRendering")
    }
    
    private func enableLowPowerRendering() {
        UserDefaults.standard.set(30.0, forKey: "OptimizedFrameRate")
        UserDefaults.standard.set(true, forKey: "LowPowerRenderingMode")
    }
    
    // MARK: - Public Interface (Minimal)
    
    func getCurrentOptimizationStatus() -> OptimizationStatus {
        return optimizationStatus
    }
    
    func forceOptimization() {
        performIntelligentOptimization()
    }
    
    func isOptimizationActive() -> Bool {
        return shouldShowStatusIndicator
    }
}

// MARK: - Supporting Types

enum OptimizationStatus: String, CaseIterable {
    case optimal = "optimal"
    case optimizing = "optimizing"
    case optimized = "optimized"
    
    var displayMessage: String {
        switch self {
        case .optimal: return ""
        case .optimizing: return "Optimizing..."
        case .optimized: return "Optimized"
        }
    }
    
    var color: Color {
        switch self {
        case .optimal: return .clear
        case .optimizing: return .orange
        case .optimized: return .green
        }
    }
    
    var iconName: String {
        switch self {
        case .optimal: return ""
        case .optimizing: return "gear"
        case .optimized: return "checkmark.circle"
        }
    }
}

struct SystemConditions {
    let memoryPressure: MemoryPressureLevel
    let batteryLevel: Float
    let thermalState: ProcessInfo.ThermalState
    let networkQuality: NetworkQuality
    let appUsagePattern: AppUsagePattern
    let timeOfDay: Int
}

enum AppUsagePattern: String, CaseIterable {
    case activeTradingSession = "trading"
    case dataAnalysis = "analysis"
    case casualBrowsing = "browsing"
    case general = "general"
    
    var requiresHighPerformance: Bool {
        switch self {
        case .activeTradingSession: return true
        case .dataAnalysis: return true
        case .casualBrowsing: return false
        case .general: return false
        }
    }
}

// Using OptimizationDecision from PerformanceModels.swift

enum OptimizationSeverity: String, CaseIterable {
    case minimal = "minimal"
    case moderate = "moderate"
    case significant = "significant"
    case critical = "critical"
}

enum OptimizationCause: String, CaseIterable {
    case memoryPressure = "memory"
    case lowBattery = "battery"
    case thermalPressure = "thermal"
    case poorNetwork = "network"
    case backgroundOptimization = "background"
}

enum RenderingOptimizationLevel: String, CaseIterable {
    case subtle = "subtle"
    case moderate = "moderate"
    case aggressive = "aggressive"
}
