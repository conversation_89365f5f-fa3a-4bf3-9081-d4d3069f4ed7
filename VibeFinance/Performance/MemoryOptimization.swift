//
//  MemoryOptimization.swift
//  VibeFinance - Advanced Memory Optimization
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import Foundation
import SwiftUI
import UIKit
import Combine

// MARK: - Memory Optimization Manager

@MainActor
class MemoryOptimizationManager: ObservableObject {
    static let shared = MemoryOptimizationManager()
    
    @Published var currentMemoryUsage: UInt64 = 0
    @Published var memoryPressureLevel: MemoryPressureLevel = .normal
    @Published var isMemoryOptimizationActive = false
    
    private var memoryMonitorTimer: Timer?
    private var cancellables = Set<AnyCancellable>()
    
    // Optimized caches with automatic eviction
    private let imageCache = OptimizedCache<String, UIImage>(
        name: "ImageCache",
        maxMemorySize: 50 * 1024 * 1024, // 50MB
        maxItemCount: 100
    )
    
    private let dataCache = NSCache<NSString, NSData>()
    private let portfolioCache = NSCache<NSString, NSString>()
    
    private init() {
        setupMemoryMonitoring()
        setupMemoryWarningHandling()
        startMemoryOptimization()
    }
    
    // MARK: - Memory Monitoring
    
    private func setupMemoryMonitoring() {
        memoryMonitorTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { _ in
            Task { @MainActor in
                self.updateMemoryUsage()
            }
        }
    }
    
    private func setupMemoryWarningHandling() {
        NotificationCenter.default.publisher(for: UIApplication.didReceiveMemoryWarningNotification)
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.handleMemoryWarning()
                }
            }
            .store(in: &cancellables)
    }
    
    private func updateMemoryUsage() {
        currentMemoryUsage = getMemoryUsage()
        memoryPressureLevel = determineMemoryPressureLevel()
        
        if memoryPressureLevel != .normal {
            performMemoryOptimization()
        }
    }
    
    private func getMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        return kerr == KERN_SUCCESS ? info.resident_size : 0
    }
    
    private func determineMemoryPressureLevel() -> MemoryPressureLevel {
        let memoryInMB = Double(currentMemoryUsage) / (1024 * 1024)
        
        switch memoryInMB {
        case 0..<100:
            return .normal
        case 100..<200:
            return .moderate
        case 200..<400:
            return .high
        default:
            return .critical
        }
    }
    
    // MARK: - Memory Optimization
    
    func startMemoryOptimization() {
        isMemoryOptimizationActive = true
        
        // Configure automatic cache eviction
        configureAutomaticCacheEviction()
        
        // Setup lazy loading for large datasets
        configureLazyLoading()
        
        ProductionConfig.log("🧠 Memory optimization activated", category: "MEMORY", level: .info)
    }
    
    private func performMemoryOptimization() {
        switch memoryPressureLevel {
        case .normal:
            break
        case .moderate:
            performModerateOptimization()
        case .high:
            performAggressiveOptimization()
        case .critical:
            performCriticalOptimization()
        }
    }
    
    private func performModerateOptimization() {
        // Clear 25% of caches
        imageCache.evictOldestItems(percentage: 0.25)
        dataCache.evictOldestItems(percentage: 0.25)
        
        ProductionConfig.log("🧹 Moderate memory optimization performed", category: "MEMORY", level: .info)
    }
    
    private func performAggressiveOptimization() {
        // Clear 50% of caches
        imageCache.evictOldestItems(percentage: 0.50)
        dataCache.evictOldestItems(percentage: 0.50)
        portfolioCache.evictOldestItems(percentage: 0.30)
        
        // Force garbage collection
        autoreleasepool {
            // Trigger memory cleanup
        }
        
        ProductionConfig.log("🧹 Aggressive memory optimization performed", category: "MEMORY", level: .warning)
    }
    
    private func performCriticalOptimization() {
        // Clear all non-essential caches
        imageCache.removeAllObjects()
        dataCache.removeAllObjects()
        
        // Keep only essential portfolio data
        portfolioCache.evictOldestItems(percentage: 0.80)
        
        // Notify other managers to reduce memory usage
        NotificationCenter.default.post(name: .memoryPressureCritical, object: nil)
        
        ProductionConfig.log("🚨 Critical memory optimization performed", category: "MEMORY", level: .error)
    }
    
    private func handleMemoryWarning() {
        performCriticalOptimization()
        
        // Additional emergency measures
        emergencyMemoryCleanup()
    }
    
    private func emergencyMemoryCleanup() {
        // Clear all possible caches
        URLCache.shared.removeAllCachedResponses()
        
        // Reduce image quality temporarily
        ImageOptimizer.shared.enableEmergencyMode()
        
        // Suspend non-essential operations
        OperationQueue.main.isSuspended = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            OperationQueue.main.isSuspended = false
        }
        
        ProductionConfig.log("🆘 Emergency memory cleanup performed", category: "MEMORY", level: .error)
    }
    
    // MARK: - Cache Configuration
    
    private func configureAutomaticCacheEviction() {
        // Configure caches to automatically evict based on memory pressure
        imageCache.setEvictionPolicy(.leastRecentlyUsed)
        dataCache.setEvictionPolicy(.leastRecentlyUsed)
        portfolioCache.setEvictionPolicy(.leastRecentlyUsed)
    }
    
    private func configureLazyLoading() {
        // Configure lazy loading for large datasets
        LazyLoadingManager.shared.configure(
            chunkSize: 50,
            preloadDistance: 10,
            memoryThreshold: 200 * 1024 * 1024 // 200MB
        )
    }
    
    // MARK: - Public Interface
    
    func cacheImage(_ image: UIImage, forKey key: String) {
        imageCache.setObject(image, forKey: key)
    }
    
    func getCachedImage(forKey key: String) -> UIImage? {
        return imageCache.object(forKey: key)
    }
    
    func cacheData(_ data: Data, forKey key: String) {
        dataCache.setObject(data, forKey: key)
    }
    
    func getCachedData(forKey key: String) -> Data? {
        return dataCache.object(forKey: key)
    }
    
    func cachePortfolioSnapshot(_ snapshot: PortfolioSnapshot, forKey key: String) {
        portfolioCache.setObject(snapshot, forKey: key)
    }
    
    func getCachedPortfolioSnapshot(forKey key: String) -> PortfolioSnapshot? {
        return portfolioCache.object(forKey: key)
    }
}

// MARK: - Optimized Cache Implementation

class OptimizedCache<Key: Hashable, Value: AnyObject> {
    private let cache = NSCache<NSString, Value>()
    private let name: String
    private var accessTimes: [Key: Date] = [:]
    private let accessQueue = DispatchQueue(label: "cache.access", attributes: .concurrent)
    
    init(name: String, maxMemorySize: Int, maxItemCount: Int) {
        self.name = name
        cache.name = name
        cache.totalCostLimit = maxMemorySize
        cache.countLimit = maxItemCount
        cache.evictsObjectsWithDiscardedContent = true
    }
    
    func setObject(_ object: Value, forKey key: Key) {
        let nsKey = String(describing: key) as NSString
        cache.setObject(object, forKey: nsKey)
        
        accessQueue.async(flags: .barrier) {
            self.accessTimes[key] = Date()
        }
    }
    
    func object(forKey key: Key) -> Value? {
        let nsKey = String(describing: key) as NSString
        let object = cache.object(forKey: nsKey)
        
        if object != nil {
            accessQueue.async(flags: .barrier) {
                self.accessTimes[key] = Date()
            }
        }
        
        return object
    }
    
    func removeObject(forKey key: Key) {
        let nsKey = String(describing: key) as NSString
        cache.removeObject(forKey: nsKey)
        
        accessQueue.async(flags: .barrier) {
            self.accessTimes.removeValue(forKey: key)
        }
    }
    
    func removeAllObjects() {
        cache.removeAllObjects()
        
        accessQueue.async(flags: .barrier) {
            self.accessTimes.removeAll()
        }
    }
    
    func setEvictionPolicy(_ policy: CacheEvictionPolicy) {
        // Configure eviction policy
        switch policy {
        case .leastRecentlyUsed:
            cache.evictsObjectsWithDiscardedContent = true
        case .firstInFirstOut:
            cache.evictsObjectsWithDiscardedContent = false
        }
    }
    
    func evictOldestItems(percentage: Double) {
        accessQueue.async(flags: .barrier) {
            let sortedKeys = self.accessTimes.sorted { $0.value < $1.value }
            let itemsToEvict = Int(Double(sortedKeys.count) * percentage)
            
            for i in 0..<min(itemsToEvict, sortedKeys.count) {
                let key = sortedKeys[i].key
                let nsKey = String(describing: key) as NSString
                self.cache.removeObject(forKey: nsKey)
                self.accessTimes.removeValue(forKey: key)
            }
        }
    }
}

// MARK: - Lazy Loading Manager

class LazyLoadingManager {
    static let shared = LazyLoadingManager()
    
    private var chunkSize = 50
    private var preloadDistance = 10
    private var memoryThreshold: UInt64 = 200 * 1024 * 1024
    
    private init() {}
    
    func configure(chunkSize: Int, preloadDistance: Int, memoryThreshold: UInt64) {
        self.chunkSize = chunkSize
        self.preloadDistance = preloadDistance
        self.memoryThreshold = memoryThreshold
    }
    
    func shouldLoadMoreData(currentIndex: Int, totalItems: Int) -> Bool {
        let memoryUsage = MemoryOptimizationManager.shared.currentMemoryUsage
        
        // Don't load more if memory usage is high
        guard memoryUsage < memoryThreshold else { return false }
        
        // Load more if we're within preload distance of the end
        return currentIndex >= totalItems - preloadDistance
    }
    
    func getOptimalChunkSize() -> Int {
        let memoryUsage = MemoryOptimizationManager.shared.currentMemoryUsage
        let memoryInMB = Double(memoryUsage) / (1024 * 1024)
        
        // Reduce chunk size based on memory pressure
        switch memoryInMB {
        case 0..<100:
            return chunkSize
        case 100..<200:
            return chunkSize / 2
        default:
            return chunkSize / 4
        }
    }
}

// MARK: - Image Optimizer

class ImageOptimizer {
    static let shared = ImageOptimizer()
    
    private var isEmergencyMode = false
    
    private init() {}
    
    func enableEmergencyMode() {
        isEmergencyMode = true
        ProductionConfig.log("📸 Image optimizer emergency mode enabled", category: "MEMORY", level: .warning)
    }
    
    func disableEmergencyMode() {
        isEmergencyMode = false
        ProductionConfig.log("📸 Image optimizer emergency mode disabled", category: "MEMORY", level: .info)
    }
    
    func optimizeImage(_ image: UIImage) -> UIImage {
        guard isEmergencyMode else { return image }
        
        // Reduce image quality in emergency mode
        let compressionQuality: CGFloat = 0.3
        
        guard let imageData = image.jpegData(compressionQuality: compressionQuality),
              let optimizedImage = UIImage(data: imageData) else {
            return image
        }
        
        return optimizedImage
    }
}

// MARK: - Data Models

// Using MemoryPressureLevel from BatteryThermalOptimizer.swift

enum CacheEvictionPolicy {
    case leastRecentlyUsed, firstInFirstOut
}

struct PortfolioSnapshot: Codable {
    let timestamp: Date
    let totalValue: Double
    let holdings: [String: Double]
    let performance: Double
}

// MARK: - Notifications

extension Notification.Name {
    static let memoryPressureCritical = Notification.Name("memoryPressureCritical")
}

// MARK: - Memory Efficient View Modifier

struct MemoryEfficientModifier: ViewModifier {
    @ObservedObject private var memoryManager = MemoryOptimizationManager.shared

    func body(content: Content) -> some View {
        content
            .onAppear {
                // Track view appearance for memory monitoring
                memoryManager.updateMemoryUsage()
            }
            .onDisappear {
                // Cleanup when view disappears
                if memoryManager.memoryPressureLevel != .normal {
                    memoryManager.performMemoryOptimization()
                }
            }
    }
}

// Using memoryEfficient() from PerformanceOptimizationSystem.swift
