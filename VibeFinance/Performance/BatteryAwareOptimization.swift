//
//  BatteryAwareOptimization.swift
//  VibeFinance - Battery-Aware Performance Optimization
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import Foundation
import SwiftUI
import UIKit
import Combine

// MARK: - Battery-Aware Performance Manager

@MainActor
class BatteryAwarePerformanceManager: ObservableObject {
    static let shared = BatteryAwarePerformanceManager()
    
    @Published var batteryLevel: Float = 1.0
    @Published var batteryState: UIDevice.BatteryState = .unknown
    @Published var isLowPowerModeEnabled = false
    @Published var performanceMode: PerformanceMode = .optimal
    
    private var cancellables = Set<AnyCancellable>()
    private var backgroundTaskIdentifier: UIBackgroundTaskIdentifier = .invalid
    
    private init() {
        setupBatteryMonitoring()
        setupLowPowerModeMonitoring()
        configurePerformanceMode()
    }
    
    // MARK: - Battery Monitoring Setup
    
    private func setupBatteryMonitoring() {
        UIDevice.current.isBatteryMonitoringEnabled = true
        
        // Monitor battery level changes
        NotificationCenter.default.publisher(for: UIDevice.batteryLevelDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateBatteryStatus()
            }
            .store(in: &cancellables)
        
        // Monitor battery state changes
        NotificationCenter.default.publisher(for: UIDevice.batteryStateDidChangeNotification)
            .sink { [weak self] _ in
                self?.updateBatteryStatus()
            }
            .store(in: &cancellables)
        
        updateBatteryStatus()
    }
    
    private func setupLowPowerModeMonitoring() {
        NotificationCenter.default.publisher(for: .NSProcessInfoPowerStateDidChange)
            .sink { [weak self] _ in
                self?.updateLowPowerModeStatus()
            }
            .store(in: &cancellables)
        
        updateLowPowerModeStatus()
    }
    
    private func updateBatteryStatus() {
        batteryLevel = UIDevice.current.batteryLevel
        batteryState = UIDevice.current.batteryState
        configurePerformanceMode()
        
        ProductionConfig.log("🔋 Battery status updated - Level: \(Int(batteryLevel * 100))%, State: \(batteryState)", category: "BATTERY", level: .debug)
    }
    
    private func updateLowPowerModeStatus() {
        isLowPowerModeEnabled = ProcessInfo.processInfo.isLowPowerModeEnabled
        configurePerformanceMode()
        
        ProductionConfig.log("⚡ Low Power Mode: \(isLowPowerModeEnabled)", category: "BATTERY", level: .info)
    }
    
    // MARK: - Performance Mode Configuration
    
    private func configurePerformanceMode() {
        let newMode = determineOptimalPerformanceMode()
        
        if newMode != performanceMode {
            performanceMode = newMode
            applyPerformanceOptimizations()
            
            ProductionConfig.log("🎯 Performance mode changed to: \(newMode)", category: "BATTERY", level: .info)
        }
    }
    
    private func determineOptimalPerformanceMode() -> PerformanceMode {
        // Low Power Mode takes precedence
        if isLowPowerModeEnabled {
            return .powerSaver
        }
        
        // Battery level based optimization
        switch batteryLevel {
        case 0.0..<0.15: // Less than 15%
            return .powerSaver
        case 0.15..<0.30: // 15-30%
            return .balanced
        case 0.30..<0.50: // 30-50%
            return .balanced
        default: // Above 50%
            return batteryState == .charging ? .optimal : .balanced
        }
    }
    
    private func applyPerformanceOptimizations() {
        switch performanceMode {
        case .optimal:
            enableOptimalPerformance()
        case .balanced:
            enableBalancedPerformance()
        case .powerSaver:
            enablePowerSaverMode()
        }
    }
    
    // MARK: - Performance Optimization Modes
    
    private func enableOptimalPerformance() {
        // Enable all features and animations
        AnimationManager.shared.setAnimationLevel(.full)
        NetworkManager.shared.setUpdateFrequency(.realTime)
        ChartRenderingManager.shared.setRenderingQuality(.high)
        DataSyncManager.shared.setBackgroundSyncEnabled(true)
        
        ProductionConfig.log("🚀 Optimal performance enabled", category: "BATTERY", level: .info)
    }
    
    private func enableBalancedPerformance() {
        // Moderate performance with some optimizations
        AnimationManager.shared.setAnimationLevel(.reduced)
        NetworkManager.shared.setUpdateFrequency(.moderate)
        ChartRenderingManager.shared.setRenderingQuality(.medium)
        DataSyncManager.shared.setBackgroundSyncEnabled(true)
        
        ProductionConfig.log("⚖️ Balanced performance enabled", category: "BATTERY", level: .info)
    }
    
    private func enablePowerSaverMode() {
        // Minimal performance to conserve battery
        AnimationManager.shared.setAnimationLevel(.minimal)
        NetworkManager.shared.setUpdateFrequency(.conservative)
        ChartRenderingManager.shared.setRenderingQuality(.low)
        DataSyncManager.shared.setBackgroundSyncEnabled(false)
        
        // Reduce background processing
        suspendNonEssentialTasks()
        
        ProductionConfig.log("🔋 Power saver mode enabled", category: "BATTERY", level: .info)
    }
    
    // MARK: - Background Task Management
    
    func beginBackgroundTask(name: String) -> UIBackgroundTaskIdentifier {
        return UIApplication.shared.beginBackgroundTask(withName: name) {
            self.endBackgroundTask()
        }
    }
    
    func endBackgroundTask() {
        if backgroundTaskIdentifier != .invalid {
            UIApplication.shared.endBackgroundTask(backgroundTaskIdentifier)
            backgroundTaskIdentifier = .invalid
        }
    }
    
    private func suspendNonEssentialTasks() {
        // Suspend non-essential background tasks
        endBackgroundTask()
        
        // Reduce timer frequencies
        PerformanceManager.shared.reduceTimerFrequencies()
        
        // Clear non-essential caches
        CacheManager.shared.clearNonEssentialCaches()
    }
    
    // MARK: - Battery Usage Analytics
    
    func trackBatteryUsage(for feature: String, duration: TimeInterval) {
        let batteryDrain = calculateBatteryDrain(duration: duration)
        
        AnalyticsManager.shared.track("battery_usage", parameters: [
            "feature": feature,
            "duration": duration,
            "battery_drain": batteryDrain,
            "performance_mode": performanceMode.rawValue,
            "low_power_mode": isLowPowerModeEnabled
        ])
    }
    
    private func calculateBatteryDrain(duration: TimeInterval) -> Double {
        // Estimate battery drain based on duration and current performance mode
        let baseDrainRate: Double
        
        switch performanceMode {
        case .optimal:
            baseDrainRate = 0.1 // 0.1% per minute
        case .balanced:
            baseDrainRate = 0.05 // 0.05% per minute
        case .powerSaver:
            baseDrainRate = 0.02 // 0.02% per minute
        }
        
        return baseDrainRate * (duration / 60.0)
    }
}

// MARK: - Animation Manager

class AnimationManager {
    static let shared = AnimationManager()
    
    private var animationLevel: AnimationLevel = .full
    
    private init() {}
    
    func setAnimationLevel(_ level: AnimationLevel) {
        animationLevel = level
        
        // Configure UIView animation settings
        switch level {
        case .full:
            UIView.setAnimationsEnabled(true)
        case .reduced:
            UIView.setAnimationsEnabled(true)
            // Reduce animation duration globally
        case .minimal:
            UIView.setAnimationsEnabled(false)
        }
    }
    
    func animationDuration(base: TimeInterval) -> TimeInterval {
        switch animationLevel {
        case .full:
            return base
        case .reduced:
            return base * 0.5
        case .minimal:
            return 0.0
        }
    }
    
    func shouldAnimate() -> Bool {
        return animationLevel != .minimal
    }
}

// MARK: - Network Manager Extensions

extension NetworkManager {
    func setUpdateFrequency(_ frequency: UpdateFrequency) {
        switch frequency {
        case .realTime:
            // Update every 1-5 seconds
            configureUpdateInterval(1.0)
        case .moderate:
            // Update every 10-30 seconds
            configureUpdateInterval(15.0)
        case .conservative:
            // Update every 60+ seconds
            configureUpdateInterval(60.0)
        }
    }
    
    private func configureUpdateInterval(_ interval: TimeInterval) {
        // Configure network update intervals
        ProductionConfig.log("🌐 Network update interval set to \(interval)s", category: "NETWORK", level: .debug)
    }
}

// MARK: - Chart Rendering Manager

class ChartRenderingManager {
    static let shared = ChartRenderingManager()
    
    private var renderingQuality: RenderingQuality = .high
    
    private init() {}
    
    func setRenderingQuality(_ quality: RenderingQuality) {
        renderingQuality = quality
        
        ProductionConfig.log("📊 Chart rendering quality set to: \(quality)", category: "RENDERING", level: .debug)
    }
    
    func getOptimalFrameRate() -> Int {
        switch renderingQuality {
        case .high:
            return 60 // 60 FPS
        case .medium:
            return 30 // 30 FPS
        case .low:
            return 15 // 15 FPS
        }
    }
    
    func shouldUseMetalRendering() -> Bool {
        return renderingQuality == .high && !ProcessInfo.processInfo.isLowPowerModeEnabled
    }
}

// MARK: - Data Sync Manager

class DataSyncManager {
    static let shared = DataSyncManager()
    
    private var isBackgroundSyncEnabled = true
    private var syncTimer: Timer?
    
    private init() {}
    
    func setBackgroundSyncEnabled(_ enabled: Bool) {
        isBackgroundSyncEnabled = enabled
        
        if enabled {
            startBackgroundSync()
        } else {
            stopBackgroundSync()
        }
        
        ProductionConfig.log("🔄 Background sync \(enabled ? "enabled" : "disabled")", category: "SYNC", level: .info)
    }
    
    private func startBackgroundSync() {
        syncTimer = Timer.scheduledTimer(withTimeInterval: 300.0, repeats: true) { _ in
            self.performBackgroundSync()
        }
    }
    
    private func stopBackgroundSync() {
        syncTimer?.invalidate()
        syncTimer = nil
    }
    
    private func performBackgroundSync() {
        guard isBackgroundSyncEnabled else { return }
        
        // Perform background data synchronization
        Task {
            await syncPortfolioData()
            await syncMarketData()
        }
    }
    
    private func syncPortfolioData() async {
        // Sync portfolio data in background
        ProductionConfig.log("📈 Syncing portfolio data", category: "SYNC", level: .debug)
    }
    
    private func syncMarketData() async {
        // Sync market data in background
        ProductionConfig.log("📊 Syncing market data", category: "SYNC", level: .debug)
    }
}

// MARK: - Performance Extensions

extension PerformanceManager {
    func reduceTimerFrequencies() {
        // Reduce frequency of all timers to conserve battery
        ProductionConfig.log("⏱️ Reducing timer frequencies for battery conservation", category: "PERFORMANCE", level: .info)
    }
}

extension CacheManager {
    func clearNonEssentialCaches() {
        // Clear caches that are not essential for core functionality
        ProductionConfig.log("🧹 Clearing non-essential caches", category: "CACHE", level: .info)
    }
}

// MARK: - Data Models

enum PerformanceMode: String, CaseIterable {
    case optimal = "optimal"
    case balanced = "balanced"
    case powerSaver = "power_saver"
}

enum AnimationLevel {
    case full, reduced, minimal
}

enum UpdateFrequency {
    case realTime, moderate, conservative
}

// Using RenderingQuality from RenderingOptimizer.swift

// MARK: - Battery Optimization View Modifier

struct BatteryOptimizedModifier: ViewModifier {
    @StateObject private var batteryManager = BatteryAwarePerformanceManager.shared
    
    func body(content: Content) -> some View {
        content
            .animation(
                batteryManager.performanceMode == .powerSaver ? .none : .easeInOut(duration: 0.3),
                value: batteryManager.performanceMode
            )
            .onAppear {
                batteryManager.trackBatteryUsage(for: "view_appearance", duration: 1.0)
            }
    }
}

extension View {
    func batteryOptimized() -> some View {
        self.modifier(BatteryOptimizedModifier())
    }
}
