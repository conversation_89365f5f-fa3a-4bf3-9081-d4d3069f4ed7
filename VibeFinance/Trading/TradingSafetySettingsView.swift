//
//  TradingSafetySettingsView.swift
//  VibeFinance - Trading Safety Settings Configuration
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import SwiftUI

// MARK: - Trading Safety Settings View

struct TradingSafetySettingsView: View {
    @StateObject private var safetyManager = TradingSafetyManager.shared
    @State private var showingResetConfirmation = false
    @State private var showingAdvancedSettings = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Warren Buffett inspired gradient background
                VibeFinanceDesignSystem.Colors.primaryGradient
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 20) {
                        // Header Section
                        SafetySettingsHeader()
                        
                        // Authentication Settings
                        AuthenticationSettingsSection()
                        
                        // Daily Limits Settings
                        DailyLimitsSettingsSection()
                        
                        // Risk Tolerance Settings
                        RiskToleranceSettingsSection()
                        
                        // Advanced Safety Features
                        AdvancedSafetyFeaturesSection()
                        
                        // Quick Presets
                        SafetyPresetsSection()
                        
                        // Reset to Defaults
                        ResetSettingsSection(showingConfirmation: $showingResetConfirmation)
                    }
                    .padding()
                }
            }
            .navigationTitle("Trading Safety")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("Advanced") {
                    showingAdvancedSettings = true
                }
            )
        }
        .sheet(isPresented: $showingAdvancedSettings) {
            AdvancedTradingSafetySettingsView()
        }
        .alert("Reset Settings", isPresented: $showingResetConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Reset", role: .destructive) {
                resetToDefaults()
            }
        } message: {
            Text("This will reset all trading safety settings to their default values. This action cannot be undone.")
        }
    }
    
    private func resetToDefaults() {
        safetyManager.safetySettings = .default
        safetyManager.dailyTradingLimits = .default
        safetyManager.riskToleranceSettings = .moderate
    }
}

// MARK: - Settings Sections

struct SafetySettingsHeader: View {
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "shield.checkered")
                    .font(.title2)
                    .foregroundColor(.green)
                
                Text("Trading Safety")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
            }
            
            Text("Configure safety measures to protect your investments and ensure responsible trading practices.")
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.leading)
            
            // Warren Buffett Quote
            BuffettWisdomCard(
                quote: "Rule No. 1: Never lose money. Rule No. 2: Never forget rule No. 1.",
                context: "Safety settings help you follow Warren's most important rules."
            )
        }
    }
}

struct AuthenticationSettingsSection: View {
    @StateObject private var safetyManager = TradingSafetyManager.shared
    
    var body: some View {
        SettingsSection(
            title: "Authentication",
            icon: "faceid",
            iconColor: .blue
        ) {
            VStack(spacing: 16) {
                SettingsToggle(
                    title: "Require Authentication",
                    description: "Require biometric or passcode authentication for trades",
                    isOn: $safetyManager.isAuthenticationRequired
                )
                
                if safetyManager.isAuthenticationRequired {
                    VStack(spacing: 12) {
                        SettingsSlider(
                            title: "Authentication Timeout",
                            description: "Time before re-authentication is required",
                            value: Binding(
                                get: { safetyManager.safetySettings.authenticationTimeout / 60 },
                                set: { newValue in
                                    var settings = safetyManager.safetySettings
                                    settings = TradingSafetySettings(
                                        maxOrderSize: settings.maxOrderSize,
                                        largeOrderThreshold: settings.largeOrderThreshold,
                                        authenticationTimeout: newValue * 60,
                                        requireBiometricAuth: settings.requireBiometricAuth,
                                        enableDailyLimits: settings.enableDailyLimits,
                                        enableRiskWarnings: settings.enableRiskWarnings,
                                        enableConcentrationChecks: settings.enableConcentrationChecks,
                                        maxConcentrationPercentage: settings.maxConcentrationPercentage
                                    )
                                    safetyManager.safetySettings = settings
                                }
                            ),
                            range: 1...30,
                            unit: "minutes"
                        )
                        
                        SettingsSlider(
                            title: "Large Order Threshold",
                            description: "Orders above this amount always require authentication",
                            value: Binding(
                                get: { safetyManager.safetySettings.largeOrderThreshold },
                                set: { newValue in
                                    var settings = safetyManager.safetySettings
                                    settings = TradingSafetySettings(
                                        maxOrderSize: settings.maxOrderSize,
                                        largeOrderThreshold: newValue,
                                        authenticationTimeout: settings.authenticationTimeout,
                                        requireBiometricAuth: settings.requireBiometricAuth,
                                        enableDailyLimits: settings.enableDailyLimits,
                                        enableRiskWarnings: settings.enableRiskWarnings,
                                        enableConcentrationChecks: settings.enableConcentrationChecks,
                                        maxConcentrationPercentage: settings.maxConcentrationPercentage
                                    )
                                    safetyManager.safetySettings = settings
                                }
                            ),
                            range: 1000...50000,
                            unit: "$",
                            step: 1000
                        )
                    }
                }
            }
        }
    }
}

struct DailyLimitsSettingsSection: View {
    @StateObject private var safetyManager = TradingSafetyManager.shared
    
    var body: some View {
        SettingsSection(
            title: "Daily Limits",
            icon: "gauge",
            iconColor: .orange
        ) {
            VStack(spacing: 16) {
                SettingsSlider(
                    title: "Max Trades Per Day",
                    description: "Maximum number of trades allowed per day",
                    value: Binding(
                        get: { Double(safetyManager.dailyTradingLimits.maxTradesPerDay) },
                        set: { newValue in
                            var limits = safetyManager.dailyTradingLimits
                            limits = DailyTradingLimits(
                                maxTradesPerDay: Int(newValue),
                                maxVolumePerDay: limits.maxVolumePerDay,
                                maxLossPerDay: limits.maxLossPerDay
                            )
                            safetyManager.dailyTradingLimits = limits
                        }
                    ),
                    range: 1...50,
                    unit: "trades"
                )
                
                SettingsSlider(
                    title: "Max Volume Per Day",
                    description: "Maximum dollar volume of trades per day",
                    value: Binding(
                        get: { safetyManager.dailyTradingLimits.maxVolumePerDay },
                        set: { newValue in
                            var limits = safetyManager.dailyTradingLimits
                            limits = DailyTradingLimits(
                                maxTradesPerDay: limits.maxTradesPerDay,
                                maxVolumePerDay: newValue,
                                maxLossPerDay: limits.maxLossPerDay
                            )
                            safetyManager.dailyTradingLimits = limits
                        }
                    ),
                    range: 5000...200000,
                    unit: "$",
                    step: 5000
                )
                
                SettingsSlider(
                    title: "Max Loss Per Day",
                    description: "Maximum allowed loss per day",
                    value: Binding(
                        get: { safetyManager.dailyTradingLimits.maxLossPerDay },
                        set: { newValue in
                            var limits = safetyManager.dailyTradingLimits
                            limits = DailyTradingLimits(
                                maxTradesPerDay: limits.maxTradesPerDay,
                                maxVolumePerDay: limits.maxVolumePerDay,
                                maxLossPerDay: newValue
                            )
                            safetyManager.dailyTradingLimits = limits
                        }
                    ),
                    range: 1000...25000,
                    unit: "$",
                    step: 500
                )
                
                // Current Usage Display
                DailyUsageDisplay()
            }
        }
    }
}

struct RiskToleranceSettingsSection: View {
    @StateObject private var safetyManager = TradingSafetyManager.shared
    
    var body: some View {
        SettingsSection(
            title: "Risk Tolerance",
            icon: "exclamationmark.triangle",
            iconColor: .red
        ) {
            VStack(spacing: 16) {
                // Risk Level Picker
                VStack(alignment: .leading, spacing: 8) {
                    Text("Risk Tolerance Level")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                    
                    Picker("Risk Tolerance", selection: Binding(
                        get: { safetyManager.riskToleranceSettings.level },
                        set: { newLevel in
                            switch newLevel {
                            case .conservative:
                                safetyManager.riskToleranceSettings = .conservative
                            case .moderate:
                                safetyManager.riskToleranceSettings = .moderate
                            case .aggressive:
                                safetyManager.riskToleranceSettings = .aggressive
                            }
                        }
                    )) {
                        ForEach(RiskToleranceLevel.allCases, id: \.self) { level in
                            Text(level.displayName).tag(level)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
                
                // Risk Thresholds
                SettingsSlider(
                    title: "Max Volatility Threshold",
                    description: "Warn when stock volatility exceeds this level",
                    value: Binding(
                        get: { safetyManager.riskToleranceSettings.maxVolatilityThreshold * 100 },
                        set: { newValue in
                            var settings = safetyManager.riskToleranceSettings
                            settings = RiskToleranceSettings(
                                level: settings.level,
                                maxVolatilityThreshold: newValue / 100,
                                maxBetaThreshold: settings.maxBetaThreshold,
                                requireConfirmationForHighRisk: settings.requireConfirmationForHighRisk
                            )
                            safetyManager.riskToleranceSettings = settings
                        }
                    ),
                    range: 10...50,
                    unit: "%"
                )
                
                SettingsSlider(
                    title: "Max Beta Threshold",
                    description: "Warn when stock beta exceeds this level",
                    value: Binding(
                        get: { safetyManager.riskToleranceSettings.maxBetaThreshold },
                        set: { newValue in
                            var settings = safetyManager.riskToleranceSettings
                            settings = RiskToleranceSettings(
                                level: settings.level,
                                maxVolatilityThreshold: settings.maxVolatilityThreshold,
                                maxBetaThreshold: newValue,
                                requireConfirmationForHighRisk: settings.requireConfirmationForHighRisk
                            )
                            safetyManager.riskToleranceSettings = settings
                        }
                    ),
                    range: 0.5...2.5,
                    unit: "",
                    step: 0.1
                )
            }
        }
    }
}

struct AdvancedSafetyFeaturesSection: View {
    @StateObject private var safetyManager = TradingSafetyManager.shared
    
    var body: some View {
        SettingsSection(
            title: "Advanced Features",
            icon: "gearshape.2",
            iconColor: .purple
        ) {
            VStack(spacing: 16) {
                SettingsToggle(
                    title: "Risk Warnings",
                    description: "Show warnings for high-risk trades",
                    isOn: Binding(
                        get: { safetyManager.safetySettings.enableRiskWarnings },
                        set: { newValue in
                            var settings = safetyManager.safetySettings
                            settings = TradingSafetySettings(
                                maxOrderSize: settings.maxOrderSize,
                                largeOrderThreshold: settings.largeOrderThreshold,
                                authenticationTimeout: settings.authenticationTimeout,
                                requireBiometricAuth: settings.requireBiometricAuth,
                                enableDailyLimits: settings.enableDailyLimits,
                                enableRiskWarnings: newValue,
                                enableConcentrationChecks: settings.enableConcentrationChecks,
                                maxConcentrationPercentage: settings.maxConcentrationPercentage
                            )
                            safetyManager.safetySettings = settings
                        }
                    )
                )
                
                SettingsToggle(
                    title: "Concentration Checks",
                    description: "Warn when a single position becomes too large",
                    isOn: Binding(
                        get: { safetyManager.safetySettings.enableConcentrationChecks },
                        set: { newValue in
                            var settings = safetyManager.safetySettings
                            settings = TradingSafetySettings(
                                maxOrderSize: settings.maxOrderSize,
                                largeOrderThreshold: settings.largeOrderThreshold,
                                authenticationTimeout: settings.authenticationTimeout,
                                requireBiometricAuth: settings.requireBiometricAuth,
                                enableDailyLimits: settings.enableDailyLimits,
                                enableRiskWarnings: settings.enableRiskWarnings,
                                enableConcentrationChecks: newValue,
                                maxConcentrationPercentage: settings.maxConcentrationPercentage
                            )
                            safetyManager.safetySettings = settings
                        }
                    )
                )
                
                if safetyManager.safetySettings.enableConcentrationChecks {
                    SettingsSlider(
                        title: "Max Concentration",
                        description: "Maximum percentage of portfolio in single position",
                        value: Binding(
                            get: { safetyManager.safetySettings.maxConcentrationPercentage },
                            set: { newValue in
                                var settings = safetyManager.safetySettings
                                settings = TradingSafetySettings(
                                    maxOrderSize: settings.maxOrderSize,
                                    largeOrderThreshold: settings.largeOrderThreshold,
                                    authenticationTimeout: settings.authenticationTimeout,
                                    requireBiometricAuth: settings.requireBiometricAuth,
                                    enableDailyLimits: settings.enableDailyLimits,
                                    enableRiskWarnings: settings.enableRiskWarnings,
                                    enableConcentrationChecks: settings.enableConcentrationChecks,
                                    maxConcentrationPercentage: newValue
                                )
                                safetyManager.safetySettings = settings
                            }
                        ),
                        range: 5...50,
                        unit: "%"
                    )
                }
            }
        }
    }
}

struct SafetyPresetsSection: View {
    @StateObject private var safetyManager = TradingSafetyManager.shared
    
    var body: some View {
        SettingsSection(
            title: "Quick Presets",
            icon: "slider.horizontal.3",
            iconColor: .cyan
        ) {
            VStack(spacing: 12) {
                Text("Choose a preset that matches your trading style:")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.leading)
                
                HStack(spacing: 12) {
                    PresetButton(
                        title: "Conservative",
                        description: "Maximum safety",
                        color: .green
                    ) {
                        applyConservativePreset()
                    }
                    
                    PresetButton(
                        title: "Moderate",
                        description: "Balanced approach",
                        color: .blue
                    ) {
                        applyModeratePreset()
                    }
                    
                    PresetButton(
                        title: "Aggressive",
                        description: "Minimal restrictions",
                        color: .orange
                    ) {
                        applyAggressivePreset()
                    }
                }
            }
        }
    }
    
    private func applyConservativePreset() {
        safetyManager.dailyTradingLimits = .conservative
        safetyManager.riskToleranceSettings = .conservative
    }
    
    private func applyModeratePreset() {
        safetyManager.dailyTradingLimits = .default
        safetyManager.riskToleranceSettings = .moderate
    }
    
    private func applyAggressivePreset() {
        safetyManager.dailyTradingLimits = .aggressive
        safetyManager.riskToleranceSettings = .aggressive
    }
}

struct ResetSettingsSection: View {
    @Binding var showingConfirmation: Bool
    
    var body: some View {
        SettingsSection(
            title: "Reset Settings",
            icon: "arrow.clockwise",
            iconColor: .red
        ) {
            VStack(spacing: 12) {
                Text("Reset all trading safety settings to their default values.")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.leading)
                
                Button("Reset to Defaults") {
                    showingConfirmation = true
                }
                .buttonStyle(SecondaryButtonStyle())
            }
        }
    }
}

// MARK: - Supporting UI Components

struct SettingsSection<Content: View>: View {
    let title: String
    let icon: String
    let iconColor: Color
    let content: Content

    init(title: String, icon: String, iconColor: Color, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.iconColor = iconColor
        self.content = content()
    }

    var body: some View {
        VStack(spacing: 16) {
            // Section Header
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(iconColor)

                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                Spacer()
            }

            // Section Content
            content
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

struct SettingsToggle: View {
    let title: String
    let description: String
    @Binding var isOn: Bool

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                        .fixedSize(horizontal: false, vertical: true)
                }

                Spacer()

                Toggle("", isOn: $isOn)
                    .tint(VibeFinanceDesignSystem.Colors.accentGold)
            }
        }
    }
}

struct SettingsSlider: View {
    let title: String
    let description: String
    @Binding var value: Double
    let range: ClosedRange<Double>
    let unit: String
    var step: Double = 1.0

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white)

                    Spacer()

                    Text("\(unit == "$" ? "$" : "")\(String(format: unit == "%" || unit == "$" ? "%.0f" : "%.1f", value))\(unit == "%" ? "%" : unit == "$" ? "" : " \(unit)")")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                }

                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .fixedSize(horizontal: false, vertical: true)
            }

            Slider(value: $value, in: range, step: step)
                .tint(VibeFinanceDesignSystem.Colors.accentGold)
        }
    }
}

struct DailyUsageDisplay: View {
    @StateObject private var safetyManager = TradingSafetyManager.shared

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Today's Usage")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)

                Spacer()
            }

            VStack(spacing: 8) {
                UsageBar(
                    label: "Trades",
                    current: safetyManager.dailyTradeCount,
                    limit: safetyManager.dailyTradingLimits.maxTradesPerDay,
                    unit: "trades"
                )

                UsageBar(
                    label: "Volume",
                    current: safetyManager.dailyTradeVolume,
                    limit: safetyManager.dailyTradingLimits.maxVolumePerDay,
                    unit: "$",
                    isDouble: true
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
        )
    }
}

struct UsageBar: View {
    let label: String
    let current: Double
    let limit: Double
    let unit: String
    var isDouble: Bool = false

    private var percentage: Double {
        guard limit > 0 else { return 0 }
        return min(current / limit, 1.0)
    }

    private var color: Color {
        switch percentage {
        case 0..<0.7: return .green
        case 0.7..<0.9: return .orange
        default: return .red
        }
    }

    var body: some View {
        VStack(spacing: 4) {
            HStack {
                Text(label)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))

                Spacer()

                if isDouble {
                    Text("\(unit)\(String(format: "%.0f", current)) / \(unit)\(String(format: "%.0f", limit))")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                } else {
                    Text("\(String(format: "%.0f", current)) / \(String(format: "%.0f", limit)) \(unit)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.white)
                }
            }

            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color.white.opacity(0.2))
                        .frame(height: 6)

                    RoundedRectangle(cornerRadius: 4)
                        .fill(color)
                        .frame(width: geometry.size.width * percentage, height: 6)
                }
            }
            .frame(height: 6)
        }
    }
}

struct PresetButton: View {
    let title: String
    let description: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                Text(description)
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(color.opacity(0.2))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(color.opacity(0.5), lineWidth: 1)
                    )
            )
        }
    }
}
