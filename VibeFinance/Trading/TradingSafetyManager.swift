//
//  TradingSafetyManager.swift
//  VibeFinance - Trading Safety & Confirmations Manager
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import Foundation
import SwiftUI
import LocalAuthentication

// Use TradeOrder from DataModels.swift
typealias SafetyTradeOrder = TradeOrder
import Combine

// MARK: - Trading Safety Manager

@MainActor
class TradingSafetyManager: ObservableObject {
    static let shared = TradingSafetyManager()
    
    @Published var safetySettings: TradingSafetySettings = .default
    @Published var isAuthenticationRequired = true
    @Published var dailyTradingLimits: DailyTradingLimits = .default
    @Published var riskToleranceSettings: RiskToleranceSettings = .moderate
    
    // Current session tracking
    @Published var currentSessionTrades: [TradeRecord] = []
    @Published var dailyTradeCount = 0
    @Published var dailyTradeVolume: Double = 0
    @Published var lastAuthenticationTime: Date?
    
    private var cancellables = Set<AnyCancellable>()
    private let biometricContext = LAContext()
    
    private init() {
        loadSafetySettings()
        setupDailyReset()
    }
    
    // MARK: - Safety Validation
    
    func validateTradeOrder(_ order: TradeOrder) async -> TradeValidationResult {
        var validationIssues: [ValidationIssue] = []
        var warnings: [TradeWarning] = []
        var requiresConfirmation = false
        
        // 1. Basic Order Validation
        let basicValidation = validateBasicOrder(order)
        if !basicValidation.isValid {
            validationIssues.append(contentsOf: basicValidation.issues)
        }
        
        // 2. Risk Assessment
        let riskAssessment = await assessTradeRisk(order)
        if riskAssessment.riskLevel == .high || riskAssessment.riskLevel == .extreme {
            warnings.append(contentsOf: riskAssessment.warnings)
            requiresConfirmation = true
        }
        
        // 3. Daily Limits Check
        let limitsCheck = checkDailyLimits(order)
        if !limitsCheck.isWithinLimits {
            if limitsCheck.isHardLimit {
                validationIssues.append(.dailyLimitExceeded(limitsCheck.limitType))
            } else {
                warnings.append(.approachingDailyLimit(limitsCheck.limitType))
                requiresConfirmation = true
            }
        }
        
        // 4. Pattern Day Trading Check
        let pdtCheck = checkPatternDayTradingRules(order)
        if !pdtCheck.isCompliant {
            validationIssues.append(.pdtViolation(pdtCheck.reason))
        }
        
        // 5. Market Hours Check
        let marketHoursCheck = checkMarketHours(order)
        if !marketHoursCheck.isDuringMarketHours {
            warnings.append(.afterHoursTrading(marketHoursCheck.nextMarketOpen))
            requiresConfirmation = true
        }
        
        // 6. Concentration Risk Check
        let concentrationCheck = await checkConcentrationRisk(order)
        if concentrationCheck.exceedsThreshold {
            warnings.append(.concentrationRisk(concentrationCheck.currentPercentage))
            requiresConfirmation = true
        }
        
        // 7. Volatility Check
        let volatilityCheck = await checkVolatilityRisk(order)
        if volatilityCheck.isHighVolatility {
            warnings.append(.highVolatility(volatilityCheck.volatilityLevel))
            requiresConfirmation = true
        }
        
        return TradeValidationResult(
            isValid: validationIssues.isEmpty,
            issues: validationIssues,
            warnings: warnings,
            requiresConfirmation: requiresConfirmation,
            riskLevel: riskAssessment.riskLevel
        )
    }
    
    // MARK: - Authentication & Confirmation
    
    func requiresAuthentication(for order: TradeOrder) -> Bool {
        // Check if authentication is required based on:
        // 1. Time since last authentication
        // 2. Order size
        // 3. Risk level
        // 4. User settings
        
        guard isAuthenticationRequired else { return false }
        
        // Large orders always require authentication
        if order.totalValue > safetySettings.largeOrderThreshold {
            return true
        }
        
        // Check time-based authentication
        if let lastAuth = lastAuthenticationTime {
            let timeSinceAuth = Date().timeIntervalSince(lastAuth)
            if timeSinceAuth > safetySettings.authenticationTimeout {
                return true
            }
        } else {
            return true // First trade of session
        }
        
        // High-risk trades require authentication
        if order.riskLevel == .high || order.riskLevel == .extreme {
            return true
        }
        
        return false
    }
    
    func authenticateUser() async -> AuthenticationResult {
        guard biometricContext.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: nil) else {
            return .fallbackToPasscode
        }
        
        do {
            let reason = "Authenticate to confirm your trade order"
            let success = try await biometricContext.evaluatePolicy(
                .deviceOwnerAuthenticationWithBiometrics,
                localizedReason: reason
            )
            
            if success {
                lastAuthenticationTime = Date()
                return .success
            } else {
                return .failed("Authentication failed")
            }
        } catch {
            return .failed(error.localizedDescription)
        }
    }
    
    // MARK: - Trade Confirmation Flow
    
    func createConfirmationFlow(for order: TradeOrder, validation: TradeValidationResult) -> TradeConfirmationFlow {
        var steps: [ConfirmationStep] = []
        
        // Step 1: Order Review
        steps.append(.orderReview(order))
        
        // Step 2: Risk Warnings (if any)
        if !validation.warnings.isEmpty {
            steps.append(.riskWarnings(validation.warnings))
        }
        
        // Step 3: Cost Breakdown
        steps.append(.costBreakdown(calculateTradeCosts(order)))
        
        // Step 4: Impact Analysis
        steps.append(.impactAnalysis(calculatePortfolioImpact(order)))
        
        // Step 5: Authentication (if required)
        if requiresAuthentication(for: order) {
            steps.append(.authentication)
        }
        
        // Step 6: Final Confirmation
        steps.append(.finalConfirmation)
        
        return TradeConfirmationFlow(
            orderId: order.id,
            steps: steps,
            currentStepIndex: 0,
            isCompleted: false
        )
    }
    
    // MARK: - Risk Assessment
    
    private func assessTradeRisk(_ order: TradeOrder) async -> RiskAssessment {
        var riskFactors: [RiskFactor] = []
        var riskLevel: RiskLevel = .low
        var warnings: [TradeWarning] = []
        
        // 1. Order Size Risk
        let orderSizeRisk = assessOrderSizeRisk(order)
        riskFactors.append(orderSizeRisk)
        
        // 2. Volatility Risk
        let volatilityRisk = await assessVolatilityRisk(order)
        riskFactors.append(volatilityRisk)
        
        // 3. Concentration Risk
        let concentrationRisk = await assessConcentrationRisk(order)
        riskFactors.append(concentrationRisk)
        
        // 4. Market Timing Risk
        let timingRisk = assessMarketTimingRisk(order)
        riskFactors.append(timingRisk)
        
        // Calculate overall risk level
        let averageRiskScore = riskFactors.map { $0.score }.reduce(0, +) / Double(riskFactors.count)
        
        switch averageRiskScore {
        case 0..<0.3:
            riskLevel = .low
        case 0.3..<0.6:
            riskLevel = .medium
        case 0.6..<0.8:
            riskLevel = .high
        default:
            riskLevel = .extreme
        }
        
        // Generate warnings based on risk factors
        for factor in riskFactors where factor.score > 0.6 {
            warnings.append(.highRiskFactor(factor.type, factor.description))
        }
        
        return RiskAssessment(
            riskLevel: riskLevel,
            riskFactors: riskFactors,
            warnings: warnings,
            overallScore: averageRiskScore
        )
    }
    
    // MARK: - Daily Limits Management
    
    private func checkDailyLimits(_ order: TradeOrder) -> DailyLimitsCheck {
        let projectedTradeCount = dailyTradeCount + 1
        let projectedVolume = dailyTradeVolume + order.totalValue
        
        // Check trade count limit
        if projectedTradeCount > dailyTradingLimits.maxTradesPerDay {
            return DailyLimitsCheck(
                isWithinLimits: false,
                isHardLimit: true,
                limitType: .tradeCount,
                currentValue: Double(dailyTradeCount),
                limitValue: Double(dailyTradingLimits.maxTradesPerDay)
            )
        }
        
        // Check volume limit
        if projectedVolume > dailyTradingLimits.maxVolumePerDay {
            return DailyLimitsCheck(
                isWithinLimits: false,
                isHardLimit: true,
                limitType: .volume,
                currentValue: dailyTradeVolume,
                limitValue: dailyTradingLimits.maxVolumePerDay
            )
        }
        
        // Check warning thresholds
        let tradeCountWarningThreshold = Double(dailyTradingLimits.maxTradesPerDay) * 0.8
        let volumeWarningThreshold = dailyTradingLimits.maxVolumePerDay * 0.8
        
        if Double(projectedTradeCount) > tradeCountWarningThreshold {
            return DailyLimitsCheck(
                isWithinLimits: true,
                isHardLimit: false,
                limitType: .tradeCount,
                currentValue: Double(dailyTradeCount),
                limitValue: Double(dailyTradingLimits.maxTradesPerDay)
            )
        }
        
        if projectedVolume > volumeWarningThreshold {
            return DailyLimitsCheck(
                isWithinLimits: true,
                isHardLimit: false,
                limitType: .volume,
                currentValue: dailyTradeVolume,
                limitValue: dailyTradingLimits.maxVolumePerDay
            )
        }
        
        return DailyLimitsCheck(
            isWithinLimits: true,
            isHardLimit: false,
            limitType: .none,
            currentValue: 0,
            limitValue: 0
        )
    }
    
    // MARK: - Helper Methods
    
    private func validateBasicOrder(_ order: TradeOrder) -> BasicValidationResult {
        var issues: [ValidationIssue] = []
        
        // Check minimum order size
        if order.quantity <= 0 {
            issues.append(.invalidQuantity("Quantity must be greater than zero"))
        }
        
        // Check maximum order size
        if order.totalValue > safetySettings.maxOrderSize {
            issues.append(.orderTooLarge("Order exceeds maximum allowed size"))
        }
        
        // Check symbol validity
        if order.symbol.isEmpty || order.symbol.count > 5 {
            issues.append(.invalidSymbol("Invalid stock symbol"))
        }
        
        // Check sufficient funds (for buy orders)
        if order.side == "buy" {
            // This would check against actual account balance
            // Simplified for now
        }
        
        return BasicValidationResult(
            isValid: issues.isEmpty,
            issues: issues
        )
    }
    
    private func loadSafetySettings() {
        // Load from UserDefaults or secure storage
        // For now, using defaults
    }
    
    private func setupDailyReset() {
        // Setup timer to reset daily counters at market open
        Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { _ in
            Task { @MainActor in
                self.checkForDailyReset()
            }
        }
    }
    
    private func checkForDailyReset() {
        let calendar = Calendar.current
        let now = Date()
        
        // Reset at 9:30 AM ET (market open)
        if calendar.component(.hour, from: now) == 9 && calendar.component(.minute, from: now) == 30 {
            dailyTradeCount = 0
            dailyTradeVolume = 0
            currentSessionTrades.removeAll()
        }
    }
    
    // MARK: - Record Keeping
    
    func recordTrade(_ order: TradeOrder, result: TradeExecutionResult) {
        let record = TradeRecord(
            orderId: order.id,
            symbol: order.symbol,
            side: OrderSide(rawValue: order.side) ?? .buy,
            quantity: order.quantity,
            price: result.executionPrice,
            timestamp: Date(),
            fees: result.fees,
            status: result.status
        )
        
        currentSessionTrades.append(record)
        
        if result.status == .filled {
            dailyTradeCount += 1
            dailyTradeVolume += order.totalValue
        }
    }

    // MARK: - Missing Method Implementations

    private func checkPatternDayTradingRules(_ order: TradeOrder) -> PDTCheck {
        return PDTCheck(isCompliant: true, reason: "")
    }

    private func checkMarketHours(_ order: TradeOrder) -> MarketHoursCheck {
        return MarketHoursCheck(isDuringMarketHours: true, nextMarketOpen: Date())
    }

    private func checkConcentrationRisk(_ order: TradeOrder) async -> ConcentrationCheck {
        return ConcentrationCheck(exceedsThreshold: false, currentPercentage: 0.0)
    }

    private func checkVolatilityRisk(_ order: TradeOrder) async -> VolatilityCheck {
        return VolatilityCheck(isHighVolatility: false, volatilityLevel: 0.0)
    }

    private func calculateTradeCosts(_ order: TradeOrder) -> TradeCosts {
        return TradeCosts(commission: 0.0, fees: 0.0, total: 0.0)
    }

    private func calculatePortfolioImpact(_ order: TradeOrder) -> PortfolioImpact {
        return PortfolioImpact(percentageChange: 0.0, newAllocation: [:])
    }

    private func assessOrderSizeRisk(_ order: TradeOrder) -> RiskFactor {
        return RiskFactor(type: "orderSize", score: 0.1, description: "Low order size risk")
    }

    private func assessVolatilityRisk(_ order: TradeOrder) async -> RiskFactor {
        return RiskFactor(type: "volatility", score: 0.1, description: "Low volatility risk")
    }

    private func assessConcentrationRisk(_ order: TradeOrder) async -> RiskFactor {
        return RiskFactor(type: "concentration", score: 0.1, description: "Low concentration risk")
    }

    private func assessMarketTimingRisk(_ order: TradeOrder) -> RiskFactor {
        return RiskFactor(type: "timing", score: 0.1, description: "Low timing risk")
    }
}
