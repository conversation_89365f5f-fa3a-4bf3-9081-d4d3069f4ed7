//
//  TradingSafetyTests.swift
//  VibeFinance - Trading Safety System Tests
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import XCTest
@testable import VibeFinance

// MARK: - Trading Safety Tests

class TradingSafetyTests: XCTestCase {
    var safetyManager: TradingSafetyManager!
    
    override func setUp() {
        super.setUp()
        safetyManager = TradingSafetyManager.shared
        // Reset to default settings for testing
        safetyManager.safetySettings = .default
        safetyManager.dailyTradingLimits = .default
        safetyManager.riskToleranceSettings = .moderate
        safetyManager.dailyTradeCount = 0
        safetyManager.dailyTradeVolume = 0
    }
    
    override func tearDown() {
        safetyManager = nil
        super.tearDown()
    }
    
    // MARK: - Basic Validation Tests
    
    func testValidOrderValidation() async {
        // Given
        let validOrder = TradeOrder(
            symbol: "AAPL",
            side: .buy,
            quantity: 10,
            orderType: .market,
            totalValue: 1500,
            riskLevel: .low
        )
        
        // When
        let result = await safetyManager.validateTradeOrder(validOrder)
        
        // Then
        XCTAssertTrue(result.isValid)
        XCTAssertTrue(result.issues.isEmpty)
        XCTAssertEqual(result.riskLevel, .low)
    }
    
    func testInvalidQuantityValidation() async {
        // Given
        let invalidOrder = TradeOrder(
            symbol: "AAPL",
            side: .buy,
            quantity: 0,
            orderType: .market,
            totalValue: 0,
            riskLevel: .low
        )
        
        // When
        let result = await safetyManager.validateTradeOrder(invalidOrder)
        
        // Then
        XCTAssertFalse(result.isValid)
        XCTAssertTrue(result.issues.contains { issue in
            if case .invalidQuantity = issue { return true }
            return false
        })
    }
    
    func testLargeOrderValidation() async {
        // Given
        let largeOrder = TradeOrder(
            symbol: "AAPL",
            side: .buy,
            quantity: 1000,
            orderType: .market,
            totalValue: 150000, // Exceeds default max order size
            riskLevel: .high
        )
        
        // When
        let result = await safetyManager.validateTradeOrder(largeOrder)
        
        // Then
        XCTAssertFalse(result.isValid)
        XCTAssertTrue(result.issues.contains { issue in
            if case .orderTooLarge = issue { return true }
            return false
        })
    }
    
    // MARK: - Daily Limits Tests
    
    func testDailyTradeCountLimit() async {
        // Given
        safetyManager.dailyTradeCount = 9 // One below limit
        let order = TradeOrder(
            symbol: "AAPL",
            side: .buy,
            quantity: 10,
            orderType: .market,
            totalValue: 1500,
            riskLevel: .low
        )
        
        // When - First order should pass
        var result = await safetyManager.validateTradeOrder(order)
        XCTAssertTrue(result.isValid)
        
        // Simulate placing the order
        safetyManager.dailyTradeCount = 10 // At limit
        
        // When - Second order should fail
        result = await safetyManager.validateTradeOrder(order)
        XCTAssertFalse(result.isValid)
        XCTAssertTrue(result.issues.contains { issue in
            if case .dailyLimitExceeded(let limitType) = issue {
                return limitType == .tradeCount
            }
            return false
        })
    }
    
    func testDailyVolumeLimit() async {
        // Given
        safetyManager.dailyTradeVolume = 45000 // Close to limit
        let order = TradeOrder(
            symbol: "AAPL",
            side: .buy,
            quantity: 100,
            orderType: .market,
            totalValue: 10000, // Would exceed daily volume limit
            riskLevel: .medium
        )
        
        // When
        let result = await safetyManager.validateTradeOrder(order)
        
        // Then
        XCTAssertFalse(result.isValid)
        XCTAssertTrue(result.issues.contains { issue in
            if case .dailyLimitExceeded(let limitType) = issue {
                return limitType == .volume
            }
            return false
        })
    }
    
    // MARK: - Authentication Tests
    
    func testAuthenticationRequiredForLargeOrder() {
        // Given
        let largeOrder = TradeOrder(
            symbol: "AAPL",
            side: .buy,
            quantity: 100,
            orderType: .market,
            totalValue: 15000, // Above large order threshold
            riskLevel: .medium
        )
        
        // When
        let requiresAuth = safetyManager.requiresAuthentication(for: largeOrder)
        
        // Then
        XCTAssertTrue(requiresAuth)
    }
    
    func testAuthenticationNotRequiredForSmallOrder() {
        // Given
        safetyManager.lastAuthenticationTime = Date() // Recent authentication
        let smallOrder = TradeOrder(
            symbol: "AAPL",
            side: .buy,
            quantity: 10,
            orderType: .market,
            totalValue: 1500,
            riskLevel: .low
        )
        
        // When
        let requiresAuth = safetyManager.requiresAuthentication(for: smallOrder)
        
        // Then
        XCTAssertFalse(requiresAuth)
    }
    
    func testAuthenticationRequiredAfterTimeout() {
        // Given
        safetyManager.lastAuthenticationTime = Date().addingTimeInterval(-600) // 10 minutes ago
        let order = TradeOrder(
            symbol: "AAPL",
            side: .buy,
            quantity: 10,
            orderType: .market,
            totalValue: 1500,
            riskLevel: .low
        )
        
        // When
        let requiresAuth = safetyManager.requiresAuthentication(for: order)
        
        // Then
        XCTAssertTrue(requiresAuth)
    }
    
    // MARK: - Risk Assessment Tests
    
    func testHighVolatilityWarning() async {
        // Given
        let volatileOrder = TradeOrder(
            symbol: "MEME", // Hypothetical volatile stock
            side: .buy,
            quantity: 100,
            orderType: .market,
            totalValue: 5000,
            riskLevel: .high
        )
        
        // When
        let result = await safetyManager.validateTradeOrder(volatileOrder)
        
        // Then
        XCTAssertTrue(result.isValid) // Should be valid but with warnings
        XCTAssertTrue(result.requiresConfirmation)
        XCTAssertEqual(result.riskLevel, .high)
    }
    
    func testConcentrationRiskWarning() async {
        // Given
        let concentratedOrder = TradeOrder(
            symbol: "AAPL",
            side: .buy,
            quantity: 500,
            orderType: .market,
            totalValue: 75000, // Large position
            riskLevel: .medium
        )
        
        // When
        let result = await safetyManager.validateTradeOrder(concentratedOrder)
        
        // Then
        XCTAssertTrue(result.requiresConfirmation)
        XCTAssertTrue(result.warnings.contains { warning in
            if case .concentrationRisk = warning { return true }
            return false
        })
    }
    
    // MARK: - Confirmation Flow Tests
    
    func testConfirmationFlowCreation() {
        // Given
        let order = TradeOrder(
            symbol: "AAPL",
            side: .buy,
            quantity: 100,
            orderType: .market,
            totalValue: 15000,
            riskLevel: .high
        )
        
        let validation = TradeValidationResult(
            isValid: true,
            issues: [],
            warnings: [.highVolatility(0.35)],
            requiresConfirmation: true,
            riskLevel: .high
        )
        
        // When
        let flow = safetyManager.createConfirmationFlow(for: order, validation: validation)
        
        // Then
        XCTAssertEqual(flow.orderId, order.id)
        XCTAssertFalse(flow.steps.isEmpty)
        XCTAssertEqual(flow.currentStepIndex, 0)
        XCTAssertFalse(flow.isCompleted)
        
        // Should include authentication step for high-risk order
        XCTAssertTrue(flow.steps.contains { step in
            if case .authentication = step { return true }
            return false
        })
    }
    
    // MARK: - Settings Tests
    
    func testConservativePresetSettings() {
        // Given
        let conservativeSettings = DailyTradingLimits.conservative
        
        // Then
        XCTAssertEqual(conservativeSettings.maxTradesPerDay, 5)
        XCTAssertEqual(conservativeSettings.maxVolumePerDay, 25000)
        XCTAssertEqual(conservativeSettings.maxLossPerDay, 2500)
    }
    
    func testAggressivePresetSettings() {
        // Given
        let aggressiveSettings = DailyTradingLimits.aggressive
        
        // Then
        XCTAssertEqual(aggressiveSettings.maxTradesPerDay, 25)
        XCTAssertEqual(aggressiveSettings.maxVolumePerDay, 100000)
        XCTAssertEqual(aggressiveSettings.maxLossPerDay, 10000)
    }
    
    func testRiskToleranceSettings() {
        // Given
        let conservativeRisk = RiskToleranceSettings.conservative
        let aggressiveRisk = RiskToleranceSettings.aggressive
        
        // Then
        XCTAssertTrue(conservativeRisk.maxVolatilityThreshold < aggressiveRisk.maxVolatilityThreshold)
        XCTAssertTrue(conservativeRisk.maxBetaThreshold < aggressiveRisk.maxBetaThreshold)
        XCTAssertTrue(conservativeRisk.requireConfirmationForHighRisk)
        XCTAssertFalse(aggressiveRisk.requireConfirmationForHighRisk)
    }
    
    // MARK: - Trade Recording Tests
    
    func testTradeRecording() {
        // Given
        let order = TradeOrder(
            symbol: "AAPL",
            side: .buy,
            quantity: 10,
            orderType: .market,
            totalValue: 1500,
            riskLevel: .low
        )
        
        let executionResult = TradeExecutionResult(
            status: .filled,
            executionPrice: 150.0,
            fees: 0,
            timestamp: Date(),
            message: nil
        )
        
        let initialTradeCount = safetyManager.dailyTradeCount
        let initialVolume = safetyManager.dailyTradeVolume
        
        // When
        safetyManager.recordTrade(order, result: executionResult)
        
        // Then
        XCTAssertEqual(safetyManager.dailyTradeCount, initialTradeCount + 1)
        XCTAssertEqual(safetyManager.dailyTradeVolume, initialVolume + order.totalValue)
        XCTAssertFalse(safetyManager.currentSessionTrades.isEmpty)
    }
    
    // MARK: - Performance Tests
    
    func testValidationPerformance() {
        // Given
        let order = TradeOrder(
            symbol: "AAPL",
            side: .buy,
            quantity: 10,
            orderType: .market,
            totalValue: 1500,
            riskLevel: .low
        )
        
        // When & Then
        measure {
            Task {
                _ = await safetyManager.validateTradeOrder(order)
            }
        }
    }
}

// MARK: - Mock Data Extensions

extension TradingSafetyTests {
    func createMockOrder(
        symbol: String = "AAPL",
        side: OrderSide = .buy,
        quantity: Double = 10,
        totalValue: Double = 1500,
        riskLevel: RiskLevel = .low
    ) -> TradeOrder {
        return TradeOrder(
            symbol: symbol,
            side: side,
            quantity: quantity,
            orderType: .market,
            totalValue: totalValue,
            riskLevel: riskLevel
        )
    }
}
