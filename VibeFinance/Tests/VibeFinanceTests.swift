//
//  VibeFinanceTests.swift
//  WealthVibe - Vibe Finance iOS App Tests
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import XCTest
import Combine
@testable import VibeFinance

final class VibeFinanceTests: XCTestCase {
    var cancellables: Set<AnyCancellable>!
    
    override func setUpWithError() throws {
        cancellables = Set<AnyCancellable>()
    }
    
    override func tearDownWithError() throws {
        cancellables = nil
    }
    
    // MARK: - User Manager Tests
    
    func testUserManagerXPCalculation() throws {
        let userManager = UserManager()
        
        // Test XP to level calculation
        XCTAssertEqual(userManager.calculateLevel(from: 0), 0)
        XCTAssertEqual(userManager.calculateLevel(from: 100), 1)
        XCTAssertEqual(userManager.calculateLevel(from: 250), 2)
        XCTAssertEqual(userManager.calculateLevel(from: 500), 3)
        XCTAssertEqual(userManager.calculateLevel(from: 1000), 4)
    }
    
    func testUserPreferencesUpdate() async throws {
        let userManager = UserManager()
        let preferences = UserPreferences(
            interests: ["Stocks", "Crypto"],
            goals: ["Build Wealth", "Learn Finance"],
            experienceLevel: .intermediate
        )
        
        // Test preference update
        await userManager.updateUserPreferences(preferences)
        
        XCTAssertEqual(userManager.user?.preferences.interests, ["Stocks", "Crypto"])
        XCTAssertEqual(userManager.user?.preferences.goals, ["Build Wealth", "Learn Finance"])
        XCTAssertEqual(userManager.user?.preferences.experienceLevel, .intermediate)
    }
    
    // MARK: - Quest Manager Tests
    
    func testQuestCompletion() async throws {
        let questManager = QuestManager()
        let quest = Quest(
            title: "Test Quest",
            description: "Test Description",
            category: .stocks,
            difficulty: .beginner,
            xpReward: 100,
            estimatedTime: 15,
            tasks: []
        )
        
        // Test quest completion
        await questManager.completeQuest(quest.id)
        
        // Verify quest is marked as completed
        let completedQuest = questManager.availableQuests.first { $0.id == quest.id }
        XCTAssertTrue(completedQuest?.isCompleted ?? false)
    }
    
    func testDailyQuestGeneration() async throws {
        let questManager = QuestManager()
        let user = User(
            email: "<EMAIL>",
            username: "testuser",
            preferences: UserPreferences(),
            subscriptionStatus: .free
        )
        
        // Test daily quest generation
        await questManager.generateDailyQuest(for: user)
        
        XCTAssertNotNil(questManager.dailyQuest)
        XCTAssertEqual(questManager.dailyQuest?.category, .stocks) // Default category
    }
    
    // MARK: - Simulator Manager Tests
    
    func testSimulatorBuyStock() async throws {
        let simulatorManager = SimulatorManager()
        simulatorManager.virtualBalance = 10000.0
        
        // Test buying stock
        let success = await simulatorManager.buyStock(
            symbol: "AAPL",
            shares: 10,
            price: 150.0
        )
        
        XCTAssertTrue(success)
        XCTAssertEqual(simulatorManager.virtualBalance, 8500.0) // 10000 - (10 * 150)
        XCTAssertEqual(simulatorManager.holdings.count, 1)
        XCTAssertEqual(simulatorManager.holdings.first?.symbol, "AAPL")
        XCTAssertEqual(simulatorManager.holdings.first?.shares, 10)
    }
    
    func testSimulatorSellStock() async throws {
        let simulatorManager = SimulatorManager()
        simulatorManager.virtualBalance = 8500.0
        
        // Add a holding first
        let holding = SimulatorHolding(
            userID: UUID(),
            symbol: "AAPL",
            shares: 10,
            averagePrice: 150.0,
            currentPrice: 160.0,
            purchaseDate: Date()
        )
        simulatorManager.holdings = [holding]
        
        // Test selling stock
        let success = await simulatorManager.sellStock(
            symbol: "AAPL",
            shares: 5,
            price: 160.0
        )
        
        XCTAssertTrue(success)
        XCTAssertEqual(simulatorManager.virtualBalance, 9300.0) // 8500 + (5 * 160)
        XCTAssertEqual(simulatorManager.holdings.first?.shares, 5)
    }
    
    func testSimulatorPortfolioValue() throws {
        let simulatorManager = SimulatorManager()
        simulatorManager.virtualBalance = 5000.0
        
        let holding1 = SimulatorHolding(
            userID: UUID(),
            symbol: "AAPL",
            shares: 10,
            averagePrice: 150.0,
            currentPrice: 160.0,
            purchaseDate: Date()
        )
        
        let holding2 = SimulatorHolding(
            userID: UUID(),
            symbol: "TSLA",
            shares: 5,
            averagePrice: 200.0,
            currentPrice: 220.0,
            purchaseDate: Date()
        )
        
        simulatorManager.holdings = [holding1, holding2]
        
        // Test portfolio value calculation
        let expectedValue = 5000.0 + (10 * 160.0) + (5 * 220.0) // 5000 + 1600 + 1100 = 7700
        XCTAssertEqual(simulatorManager.totalPortfolioValue, expectedValue)
    }
    
    // MARK: - Squad Manager Tests
    
    func testSquadCreation() async throws {
        let squadManager = SquadManager()
        let squad = Squad(
            name: "Test Squad",
            description: "Test Description",
            emoji: "💰",
            creatorID: UUID(),
            isPublic: true,
            maxMembers: 50
        )
        
        // Test squad creation
        await squadManager.createSquad(squad)
        
        XCTAssertTrue(squadManager.squads.contains { $0.id == squad.id })
        XCTAssertTrue(squadManager.userSquads.contains { $0.id == squad.id })
    }
    
    func testSquadJoining() async throws {
        let squadManager = SquadManager()
        let squadID = UUID()
        
        // Add a squad to the available squads
        let squad = Squad(
            id: squadID,
            name: "Test Squad",
            description: "Test Description",
            emoji: "💰",
            creatorID: UUID(),
            isPublic: true,
            maxMembers: 50
        )
        squadManager.squads = [squad]
        
        // Test joining squad
        await squadManager.joinSquad(squadID)
        
        XCTAssertTrue(squadManager.userSquads.contains { $0.id == squadID })
    }
    
    // MARK: - Feed Manager Tests
    
    func testFeedGeneration() async throws {
        let feedManager = FeedManager()
        let user = User(
            email: "<EMAIL>",
            username: "testuser",
            preferences: UserPreferences(
                interests: ["Stocks", "Tech"],
                goals: ["Build Wealth"]
            ),
            subscriptionStatus: .pro
        )
        
        // Test feed generation
        await feedManager.generateDailyFeed(for: user)
        
        XCTAssertFalse(feedManager.feedItems.isEmpty)
        XCTAssertFalse(feedManager.isLoading)
    }
    
    func testFeedReactions() async throws {
        let feedManager = FeedManager()
        let feedItem = FeedItem(
            userID: UUID(),
            content: FeedContent(
                summary: "Test content",
                category: .stocks,
                tags: ["test"],
                vibeScore: 80,
                citation: Citation(source: "Test Source", url: "https://test.com")
            )
        )
        feedManager.feedItems = [feedItem]
        
        // Test adding reaction
        await feedManager.addReaction(.like, to: feedItem)
        
        let updatedItem = feedManager.feedItems.first { $0.id == feedItem.id }
        XCTAssertEqual(updatedItem?.getReactionCount(for: .like), 1)
    }
    
    // MARK: - Notification Manager Tests
    
    func testNotificationScheduling() async throws {
        let notificationManager = NotificationManager()
        let quest = Quest(
            title: "Test Quest",
            description: "Test Description",
            category: .stocks,
            difficulty: .beginner,
            xpReward: 100,
            estimatedTime: 15,
            tasks: []
        )
        
        // Test quest reminder scheduling
        await notificationManager.scheduleQuestReminder(quest: quest, delay: 1)
        
        let pendingNotifications = await notificationManager.getPendingNotifications()
        XCTAssertTrue(pendingNotifications.contains { $0.identifier == "quest_\(quest.id.uuidString)" })
    }
    
    // MARK: - Stock Service Tests
    
    func testStockDataFetching() async throws {
        let stockService = StockServiceManager.shared
        
        // Test fetching stock data
        let stockData = await stockService.getStockData(symbol: "AAPL")
        
        XCTAssertNotNil(stockData)
        XCTAssertEqual(stockData?.symbol, "AAPL")
        XCTAssertGreaterThan(stockData?.price ?? 0, 0)
    }
    
    func testMarketOverview() async throws {
        let stockService = StockServiceManager.shared
        
        // Test market overview
        let marketData = await stockService.getMarketOverview()
        
        XCTAssertNotNil(marketData)
        XCTAssertFalse(marketData?.indices.isEmpty ?? true)
    }
    
    // MARK: - AI Service Tests
    
    func testAIContentGeneration() async throws {
        let aiService = GeminiAIService()
        
        // Test content summarization
        let summary = try await aiService.summarizeContent(
            "Apple Inc. reported strong quarterly earnings with revenue growth of 15%.",
            maxLength: 100
        )
        
        XCTAssertFalse(summary.isEmpty)
        XCTAssertLessThanOrEqual(summary.count, 100)
    }
    
    func testAIVibeScoreCalculation() async throws {
        let aiService = GeminiAIService()
        let preferences = UserPreferences(
            interests: ["Stocks", "Tech"],
            goals: ["Build Wealth"]
        )
        
        // Test vibe score calculation
        let vibeScore = try await aiService.calculateVibeScore(
            "Apple stock hits new all-time high with strong iPhone sales",
            for: preferences
        )
        
        XCTAssertGreaterThanOrEqual(vibeScore, 1)
        XCTAssertLessThanOrEqual(vibeScore, 100)
    }
    
    // MARK: - Integration Tests
    
    func testCompleteUserJourney() async throws {
        // Test complete user journey from onboarding to quest completion
        let userManager = UserManager()
        let questManager = QuestManager()
        let simulatorManager = SimulatorManager()
        
        // 1. User sets preferences
        let preferences = UserPreferences(
            interests: ["Stocks"],
            goals: ["Learn Finance"],
            experienceLevel: .beginner
        )
        await userManager.updateUserPreferences(preferences)
        
        // 2. User completes a quest
        let quest = Quest(
            title: "Stock Basics",
            description: "Learn about stocks",
            category: .stocks,
            difficulty: .beginner,
            xpReward: 100,
            estimatedTime: 15,
            tasks: []
        )
        await questManager.completeQuest(quest.id)
        
        // 3. User gains XP
        await userManager.addXP(quest.xpReward)
        
        // 4. User makes first investment in simulator
        let success = await simulatorManager.buyStock(
            symbol: "AAPL",
            shares: 1,
            price: 150.0
        )
        
        // Verify journey completion
        XCTAssertEqual(userManager.user?.preferences.interests, ["Stocks"])
        XCTAssertEqual(userManager.user?.xp, 100)
        XCTAssertTrue(success)
        XCTAssertEqual(simulatorManager.holdings.count, 1)
    }
    
    // MARK: - Performance Tests
    
    func testFeedGenerationPerformance() throws {
        let feedManager = FeedManager()
        let user = User(
            email: "<EMAIL>",
            username: "testuser",
            preferences: UserPreferences(),
            subscriptionStatus: .pro
        )
        
        measure {
            Task {
                await feedManager.generateDailyFeed(for: user)
            }
        }
    }
    
    func testPortfolioCalculationPerformance() throws {
        let simulatorManager = SimulatorManager()
        
        // Add many holdings
        for i in 0..<1000 {
            let holding = SimulatorHolding(
                userID: UUID(),
                symbol: "STOCK\(i)",
                shares: Double(i),
                averagePrice: 100.0,
                currentPrice: 110.0,
                purchaseDate: Date()
            )
            simulatorManager.holdings.append(holding)
        }
        
        measure {
            _ = simulatorManager.totalPortfolioValue
        }
    }
}
