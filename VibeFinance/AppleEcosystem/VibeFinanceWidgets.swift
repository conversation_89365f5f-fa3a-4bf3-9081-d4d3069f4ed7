//
//  VibeFinanceWidgets.swift
//  VibeFinance - Home Screen Widgets
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import SwiftUI
import WidgetKit

// MARK: - Portfolio Widget

struct PortfolioWidget: Widget {
    let kind: String = "PortfolioWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: PortfolioProvider()) { entry in
            PortfolioWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Portfolio")
        .description("Track your investment portfolio performance")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
    }
}

struct PortfolioProvider: TimelineProvider {
    func placeholder(in context: Context) -> PortfolioEntry {
        PortfolioEntry(
            date: Date(),
            totalValue: 112847.50,
            todayChange: 1247.50,
            todayChangePercent: 1.12,
            topHolding: "AAPL",
            topHoldingChange: 2.15
        )
    }
    
    func getSnapshot(in context: Context, completion: @escaping (PortfolioEntry) -> ()) {
        let entry = PortfolioEntry(
            date: Date(),
            totalValue: 112847.50,
            todayChange: 1247.50,
            todayChangePercent: 1.12,
            topHolding: "AAPL",
            topHoldingChange: 2.15
        )
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<PortfolioEntry>) -> ()) {
        var entries: [PortfolioEntry] = []
        
        // Generate timeline entries for the next 4 hours, updating every 15 minutes
        let currentDate = Date()
        for hourOffset in 0 ..< 16 {
            let entryDate = Calendar.current.date(byAdding: .minute, value: hourOffset * 15, to: currentDate)!
            
            // Simulate portfolio changes
            let baseValue = 112847.50
            let variation = Double.random(in: -2000...2000)
            let totalValue = baseValue + variation
            let todayChange = variation
            let todayChangePercent = (variation / baseValue) * 100
            
            let entry = PortfolioEntry(
                date: entryDate,
                totalValue: totalValue,
                todayChange: todayChange,
                todayChangePercent: todayChangePercent,
                topHolding: "AAPL",
                topHoldingChange: Double.random(in: -5...5)
            )
            entries.append(entry)
        }
        
        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct PortfolioEntry: TimelineEntry {
    let date: Date
    let totalValue: Double
    let todayChange: Double
    let todayChangePercent: Double
    let topHolding: String
    let topHoldingChange: Double
}

struct PortfolioWidgetEntryView: View {
    var entry: PortfolioProvider.Entry
    @Environment(\.widgetFamily) var family
    
    var body: some View {
        switch family {
        case .systemSmall:
            SmallPortfolioWidget(entry: entry)
        case .systemMedium:
            MediumPortfolioWidget(entry: entry)
        case .systemLarge:
            LargePortfolioWidget(entry: entry)
        default:
            SmallPortfolioWidget(entry: entry)
        }
    }
}

// MARK: - Small Portfolio Widget

struct SmallPortfolioWidget: View {
    let entry: PortfolioEntry
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("Portfolio")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white.opacity(0.8))
                
                Spacer()
                
                Image(systemName: "chart.pie.fill")
                    .font(.caption)
                    .foregroundColor(.blue)
            }
            
            Spacer()
            
            VStack(alignment: .leading, spacing: 2) {
                Text("$\(String(format: "%.0f", entry.totalValue))")
                    .font(.title3)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                HStack(spacing: 2) {
                    Image(systemName: entry.todayChange >= 0 ? "arrow.up.right" : "arrow.down.right")
                        .font(.caption2)
                    
                    Text("\(entry.todayChange >= 0 ? "+" : "")\(String(format: "%.1f", entry.todayChangePercent))%")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                .foregroundColor(entry.todayChange >= 0 ? .green : .red)
            }
        }
        .padding()
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.2, blue: 0.4),
                    Color(red: 0.05, green: 0.15, blue: 0.35)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }
}

// MARK: - Medium Portfolio Widget

struct MediumPortfolioWidget: View {
    let entry: PortfolioEntry
    
    var body: some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Portfolio")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Spacer()
                    
                    Image(systemName: "chart.pie.fill")
                        .font(.subheadline)
                        .foregroundColor(.blue)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("$\(String(format: "%.2f", entry.totalValue))")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    HStack(spacing: 4) {
                        Image(systemName: entry.todayChange >= 0 ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        
                        Text("\(entry.todayChange >= 0 ? "+" : "")$\(String(format: "%.2f", entry.todayChange))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("(\(entry.todayChange >= 0 ? "+" : "")\(String(format: "%.2f", entry.todayChangePercent))%)")
                            .font(.caption)
                    }
                    .foregroundColor(entry.todayChange >= 0 ? .green : .red)
                }
            }
            
            Divider()
                .background(Color.white.opacity(0.3))
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Top Holding")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(entry.topHolding)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    HStack(spacing: 2) {
                        Image(systemName: entry.topHoldingChange >= 0 ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption2)
                        
                        Text("\(entry.topHoldingChange >= 0 ? "+" : "")\(String(format: "%.2f", entry.topHoldingChange))%")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(entry.topHoldingChange >= 0 ? .green : .red)
                }
            }
        }
        .padding()
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.2, blue: 0.4),
                    Color(red: 0.05, green: 0.15, blue: 0.35)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }
}

// MARK: - Large Portfolio Widget

struct LargePortfolioWidget: View {
    let entry: PortfolioEntry
    
    var body: some View {
        VStack(spacing: 16) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Portfolio Performance")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("Last updated: \(entry.date, style: .time)")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.6))
                }
                
                Spacer()
                
                Image(systemName: "chart.pie.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
            
            // Portfolio Value
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Total Value")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Spacer()
                }
                
                Text("$\(String(format: "%.2f", entry.totalValue))")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                HStack(spacing: 6) {
                    Image(systemName: entry.todayChange >= 0 ? "arrow.up.right" : "arrow.down.right")
                        .font(.subheadline)
                    
                    Text("\(entry.todayChange >= 0 ? "+" : "")$\(String(format: "%.2f", entry.todayChange))")
                        .font(.title3)
                        .fontWeight(.semibold)
                    
                    Text("(\(entry.todayChange >= 0 ? "+" : "")\(String(format: "%.2f", entry.todayChangePercent))%)")
                        .font(.subheadline)
                }
                .foregroundColor(entry.todayChange >= 0 ? .green : .red)
            }
            
            // Top Holding
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Top Holding")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text(entry.topHolding)
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Today")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    
                    HStack(spacing: 2) {
                        Image(systemName: entry.topHoldingChange >= 0 ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        
                        Text("\(entry.topHoldingChange >= 0 ? "+" : "")\(String(format: "%.2f", entry.topHoldingChange))%")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(entry.topHoldingChange >= 0 ? .green : .red)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.white.opacity(0.1))
            )
        }
        .padding()
        .background(
            LinearGradient(
                colors: [
                    Color(red: 0.1, green: 0.2, blue: 0.4),
                    Color(red: 0.05, green: 0.15, blue: 0.35)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }
}

// MARK: - Market Widget

struct MarketWidget: Widget {
    let kind: String = "MarketWidget"
    
    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: MarketProvider()) { entry in
            MarketWidgetEntryView(entry: entry)
        }
        .configurationDisplayName("Market Overview")
        .description("Track major market indices and trends")
        .supportedFamilies([.systemSmall, .systemMedium])
    }
}

struct MarketProvider: TimelineProvider {
    func placeholder(in context: Context) -> MarketEntry {
        MarketEntry(
            date: Date(),
            spyPrice: 450.25,
            spyChange: 0.75,
            nasdaqPrice: 14125.80,
            nasdaqChange: 1.25,
            vixLevel: 18.5
        )
    }
    
    func getSnapshot(in context: Context, completion: @escaping (MarketEntry) -> ()) {
        let entry = MarketEntry(
            date: Date(),
            spyPrice: 450.25,
            spyChange: 0.75,
            nasdaqPrice: 14125.80,
            nasdaqChange: 1.25,
            vixLevel: 18.5
        )
        completion(entry)
    }
    
    func getTimeline(in context: Context, completion: @escaping (Timeline<MarketEntry>) -> ()) {
        var entries: [MarketEntry] = []
        
        let currentDate = Date()
        for minuteOffset in 0 ..< 60 {
            let entryDate = Calendar.current.date(byAdding: .minute, value: minuteOffset * 5, to: currentDate)!
            
            let entry = MarketEntry(
                date: entryDate,
                spyPrice: 450.25 + Double.random(in: -5...5),
                spyChange: Double.random(in: -2...2),
                nasdaqPrice: 14125.80 + Double.random(in: -100...100),
                nasdaqChange: Double.random(in: -2...2),
                vixLevel: 18.5 + Double.random(in: -3...3)
            )
            entries.append(entry)
        }
        
        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

struct MarketEntry: TimelineEntry {
    let date: Date
    let spyPrice: Double
    let spyChange: Double
    let nasdaqPrice: Double
    let nasdaqChange: Double
    let vixLevel: Double
}

struct MarketWidgetEntryView: View {
    var entry: MarketProvider.Entry
    @Environment(\.widgetFamily) var family
    
    var body: some View {
        switch family {
        case .systemSmall:
            SmallMarketWidget(entry: entry)
        case .systemMedium:
            MediumMarketWidget(entry: entry)
        default:
            SmallMarketWidget(entry: entry)
        }
    }
}

struct SmallMarketWidget: View {
    let entry: MarketEntry
    
    var body: some View {
        VStack(alignment: .leading, spacing: 6) {
            HStack {
                Text("Market")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white.opacity(0.8))
                
                Spacer()
                
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .font(.caption)
                    .foregroundColor(.green)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text("SPY")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Spacer()
                    
                    Text("\(entry.spyChange >= 0 ? "+" : "")\(String(format: "%.2f", entry.spyChange))%")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(entry.spyChange >= 0 ? .green : .red)
                }
                
                HStack {
                    Text("NASDAQ")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Spacer()
                    
                    Text("\(entry.nasdaqChange >= 0 ? "+" : "")\(String(format: "%.2f", entry.nasdaqChange))%")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(entry.nasdaqChange >= 0 ? .green : .red)
                }
                
                HStack {
                    Text("VIX")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Spacer()
                    
                    Text(String(format: "%.1f", entry.vixLevel))
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.orange)
                }
            }
        }
        .padding()
        .background(
            LinearGradient(
                colors: [Color.black.opacity(0.8), Color.black.opacity(0.6)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }
}

struct MediumMarketWidget: View {
    let entry: MarketEntry
    
    var body: some View {
        HStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Market Overview")
                    .font(.subheadline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                VStack(alignment: .leading, spacing: 6) {
                    MarketIndexRow(name: "S&P 500", price: entry.spyPrice, change: entry.spyChange)
                    MarketIndexRow(name: "NASDAQ", price: entry.nasdaqPrice, change: entry.nasdaqChange)
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 8) {
                Text("Fear & Greed")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text(String(format: "%.1f", entry.vixLevel))
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.orange)
                    
                    Text("VIX")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
        }
        .padding()
        .background(
            LinearGradient(
                colors: [Color.black.opacity(0.8), Color.black.opacity(0.6)],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
        )
    }
}

struct MarketIndexRow: View {
    let name: String
    let price: Double
    let change: Double
    
    var body: some View {
        HStack {
            Text(name)
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 1) {
                Text(String(format: "%.2f", price))
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text("\(change >= 0 ? "+" : "")\(String(format: "%.2f", change))%")
                    .font(.caption2)
                    .foregroundColor(change >= 0 ? .green : .red)
            }
        }
    }
}

// MARK: - Widget Bundle

@main
struct VibeFinanceWidgets: WidgetBundle {
    var body: some Widget {
        PortfolioWidget()
        MarketWidget()
    }
}
