//
//  LiveActivities.swift
//  VibeFinance - Live Activities Implementation
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import SwiftUI
import ActivityKit
import WidgetKit

// MARK: - Market Live Activity

@available(iOS 16.1, *)
struct MarketLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: MarketActivityAttributes.self) { context in
            // Lock screen/banner UI
            MarketLiveActivityView(context: context)
        } dynamicIsland: { context in
            // Dynamic Island UI
            DynamicIsland {
                // Expanded UI
                DynamicIslandExpandedRegion(.leading) {
                    MarketExpandedLeading(context: context)
                }
                DynamicIslandExpandedRegion(.trailing) {
                    MarketExpandedTrailing(context: context)
                }
                DynamicIslandExpandedRegion(.bottom) {
                    MarketExpandedBottom(context: context)
                }
            } compactLeading: {
                // Compact leading
                MarketCompactLeading(context: context)
            } compactTrailing: {
                // Compact trailing
                MarketCompactTrailing(context: context)
            } minimal: {
                // Minimal UI
                MarketMinimal(context: context)
            }
        }
    }
}

// MARK: - Market Activity Attributes

@available(iOS 16.1, *)
struct MarketActivityAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        let spyPrice: Double
        let spyChange: Double
        let nasdaqPrice: Double
        let nasdaqChange: Double
        let lastUpdated: Date
        let marketStatus: String
        
        init(spyPrice: Double, spyChange: Double, nasdaqPrice: Double, nasdaqChange: Double, lastUpdated: Date, marketStatus: String = "Open") {
            self.spyPrice = spyPrice
            self.spyChange = spyChange
            self.nasdaqPrice = nasdaqPrice
            self.nasdaqChange = nasdaqChange
            self.lastUpdated = lastUpdated
            self.marketStatus = marketStatus
        }
    }
    
    let startTime: Date
}

// MARK: - Live Activity Views

@available(iOS 16.1, *)
struct MarketLiveActivityView: View {
    let context: ActivityViewContext<MarketActivityAttributes>
    
    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                HStack(spacing: 6) {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                        .font(.headline)
                        .foregroundColor(.blue)
                    
                    Text("Market Watch")
                        .font(.headline)
                        .fontWeight(.bold)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Text(context.state.marketStatus)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.green)
                    
                    Text(context.state.lastUpdated, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            // Market Data
            HStack(spacing: 20) {
                MarketIndexCard(
                    name: "S&P 500",
                    price: context.state.spyPrice,
                    change: context.state.spyChange
                )
                
                MarketIndexCard(
                    name: "NASDAQ",
                    price: context.state.nasdaqPrice,
                    change: context.state.nasdaqChange
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(radius: 4)
        )
    }
}

@available(iOS 16.1, *)
struct MarketIndexCard: View {
    let name: String
    let price: Double
    let change: Double
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(name)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            Text("$\(String(format: "%.2f", price))")
                .font(.subheadline)
                .fontWeight(.bold)
            
            HStack(spacing: 2) {
                Image(systemName: change >= 0 ? "arrow.up.right" : "arrow.down.right")
                    .font(.caption2)
                
                Text("\(change >= 0 ? "+" : "")\(String(format: "%.2f", change))%")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(change >= 0 ? .green : .red)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Dynamic Island Views

@available(iOS 16.1, *)
struct MarketExpandedLeading: View {
    let context: ActivityViewContext<MarketActivityAttributes>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("S&P 500")
                .font(.caption2)
                .foregroundColor(.secondary)
            
            Text("$\(String(format: "%.2f", context.state.spyPrice))")
                .font(.caption)
                .fontWeight(.bold)
            
            HStack(spacing: 2) {
                Image(systemName: context.state.spyChange >= 0 ? "arrow.up.right" : "arrow.down.right")
                    .font(.caption2)
                
                Text("\(context.state.spyChange >= 0 ? "+" : "")\(String(format: "%.2f", context.state.spyChange))%")
                    .font(.caption2)
                    .fontWeight(.medium)
            }
            .foregroundColor(context.state.spyChange >= 0 ? .green : .red)
        }
    }
}

@available(iOS 16.1, *)
struct MarketExpandedTrailing: View {
    let context: ActivityViewContext<MarketActivityAttributes>
    
    var body: some View {
        VStack(alignment: .trailing, spacing: 4) {
            Text("NASDAQ")
                .font(.caption2)
                .foregroundColor(.secondary)
            
            Text("$\(String(format: "%.2f", context.state.nasdaqPrice))")
                .font(.caption)
                .fontWeight(.bold)
            
            HStack(spacing: 2) {
                Image(systemName: context.state.nasdaqChange >= 0 ? "arrow.up.right" : "arrow.down.right")
                    .font(.caption2)
                
                Text("\(context.state.nasdaqChange >= 0 ? "+" : "")\(String(format: "%.2f", context.state.nasdaqChange))%")
                    .font(.caption2)
                    .fontWeight(.medium)
            }
            .foregroundColor(context.state.nasdaqChange >= 0 ? .green : .red)
        }
    }
}

@available(iOS 16.1, *)
struct MarketExpandedBottom: View {
    let context: ActivityViewContext<MarketActivityAttributes>
    
    var body: some View {
        HStack {
            HStack(spacing: 4) {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .font(.caption2)
                    .foregroundColor(.blue)
                
                Text("Market \(context.state.marketStatus)")
                    .font(.caption2)
                    .fontWeight(.medium)
            }
            
            Spacer()
            
            Text("Updated \(context.state.lastUpdated, style: .time)")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

@available(iOS 16.1, *)
struct MarketCompactLeading: View {
    let context: ActivityViewContext<MarketActivityAttributes>
    
    var body: some View {
        HStack(spacing: 2) {
            Image(systemName: "chart.line.uptrend.xyaxis")
                .font(.caption2)
                .foregroundColor(.blue)
            
            Text("\(context.state.spyChange >= 0 ? "+" : "")\(String(format: "%.1f", context.state.spyChange))%")
                .font(.caption2)
                .fontWeight(.bold)
                .foregroundColor(context.state.spyChange >= 0 ? .green : .red)
        }
    }
}

@available(iOS 16.1, *)
struct MarketCompactTrailing: View {
    let context: ActivityViewContext<MarketActivityAttributes>
    
    var body: some View {
        HStack(spacing: 2) {
            Text("\(context.state.nasdaqChange >= 0 ? "+" : "")\(String(format: "%.1f", context.state.nasdaqChange))%")
                .font(.caption2)
                .fontWeight(.bold)
                .foregroundColor(context.state.nasdaqChange >= 0 ? .green : .red)
            
            Image(systemName: "chart.bar.fill")
                .font(.caption2)
                .foregroundColor(.orange)
        }
    }
}

@available(iOS 16.1, *)
struct MarketMinimal: View {
    let context: ActivityViewContext<MarketActivityAttributes>
    
    var body: some View {
        Image(systemName: context.state.spyChange >= 0 ? "arrow.up.right" : "arrow.down.right")
            .font(.caption2)
            .foregroundColor(context.state.spyChange >= 0 ? .green : .red)
    }
}

// MARK: - Portfolio Live Activity

@available(iOS 16.1, *)
struct PortfolioLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: PortfolioActivityAttributes.self) { context in
            // Lock screen/banner UI
            PortfolioLiveActivityView(context: context)
        } dynamicIsland: { context in
            // Dynamic Island UI
            DynamicIsland {
                // Expanded UI
                DynamicIslandExpandedRegion(.leading) {
                    PortfolioExpandedLeading(context: context)
                }
                DynamicIslandExpandedRegion(.trailing) {
                    PortfolioExpandedTrailing(context: context)
                }
                DynamicIslandExpandedRegion(.bottom) {
                    PortfolioExpandedBottom(context: context)
                }
            } compactLeading: {
                // Compact leading
                PortfolioCompactLeading(context: context)
            } compactTrailing: {
                // Compact trailing
                PortfolioCompactTrailing(context: context)
            } minimal: {
                // Minimal UI
                PortfolioMinimal(context: context)
            }
        }
    }
}

// MARK: - Portfolio Activity Attributes

@available(iOS 16.1, *)
struct PortfolioActivityAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        let totalValue: Double
        let todayChange: Double
        let todayChangePercent: Double
        let topPerformer: String
        let topPerformerChange: Double
        let lastUpdated: Date
    }
    
    let portfolioName: String
}

// MARK: - Portfolio Live Activity Views

@available(iOS 16.1, *)
struct PortfolioLiveActivityView: View {
    let context: ActivityViewContext<PortfolioActivityAttributes>
    
    var body: some View {
        VStack(spacing: 12) {
            // Header
            HStack {
                HStack(spacing: 6) {
                    Image(systemName: "chart.pie.fill")
                        .font(.headline)
                        .foregroundColor(.blue)
                    
                    Text(context.attributes.portfolioName)
                        .font(.headline)
                        .fontWeight(.bold)
                }
                
                Spacer()
                
                Text(context.state.lastUpdated, style: .time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // Portfolio Value
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Total Value")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("$\(String(format: "%.2f", context.state.totalValue))")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    HStack(spacing: 4) {
                        Image(systemName: context.state.todayChange >= 0 ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        
                        Text("\(context.state.todayChange >= 0 ? "+" : "")$\(String(format: "%.2f", context.state.todayChange))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Text("(\(context.state.todayChangePercent >= 0 ? "+" : "")\(String(format: "%.2f", context.state.todayChangePercent))%)")
                            .font(.caption)
                    }
                    .foregroundColor(context.state.todayChange >= 0 ? .green : .red)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("Top Performer")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(context.state.topPerformer)
                        .font(.subheadline)
                        .fontWeight(.bold)
                    
                    Text("\(context.state.topPerformerChange >= 0 ? "+" : "")\(String(format: "%.2f", context.state.topPerformerChange))%")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(context.state.topPerformerChange >= 0 ? .green : .red)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(radius: 4)
        )
    }
}

// MARK: - Portfolio Dynamic Island Views

@available(iOS 16.1, *)
struct PortfolioExpandedLeading: View {
    let context: ActivityViewContext<PortfolioActivityAttributes>
    
    var body: some View {
        VStack(alignment: .leading, spacing: 2) {
            Text("Portfolio")
                .font(.caption2)
                .foregroundColor(.secondary)
            
            Text("$\(String(format: "%.0f", context.state.totalValue))")
                .font(.caption)
                .fontWeight(.bold)
        }
    }
}

@available(iOS 16.1, *)
struct PortfolioExpandedTrailing: View {
    let context: ActivityViewContext<PortfolioActivityAttributes>
    
    var body: some View {
        VStack(alignment: .trailing, spacing: 2) {
            Text("Today")
                .font(.caption2)
                .foregroundColor(.secondary)
            
            Text("\(context.state.todayChangePercent >= 0 ? "+" : "")\(String(format: "%.1f", context.state.todayChangePercent))%")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(context.state.todayChangePercent >= 0 ? .green : .red)
        }
    }
}

@available(iOS 16.1, *)
struct PortfolioExpandedBottom: View {
    let context: ActivityViewContext<PortfolioActivityAttributes>
    
    var body: some View {
        HStack {
            Text("Top: \(context.state.topPerformer)")
                .font(.caption2)
                .fontWeight(.medium)
            
            Spacer()
            
            Text("\(context.state.topPerformerChange >= 0 ? "+" : "")\(String(format: "%.1f", context.state.topPerformerChange))%")
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(context.state.topPerformerChange >= 0 ? .green : .red)
        }
    }
}

@available(iOS 16.1, *)
struct PortfolioCompactLeading: View {
    let context: ActivityViewContext<PortfolioActivityAttributes>
    
    var body: some View {
        Image(systemName: "chart.pie.fill")
            .font(.caption2)
            .foregroundColor(.blue)
    }
}

@available(iOS 16.1, *)
struct PortfolioCompactTrailing: View {
    let context: ActivityViewContext<PortfolioActivityAttributes>
    
    var body: some View {
        Text("\(context.state.todayChangePercent >= 0 ? "+" : "")\(String(format: "%.1f", context.state.todayChangePercent))%")
            .font(.caption2)
            .fontWeight(.bold)
            .foregroundColor(context.state.todayChangePercent >= 0 ? .green : .red)
    }
}

@available(iOS 16.1, *)
struct PortfolioMinimal: View {
    let context: ActivityViewContext<PortfolioActivityAttributes>
    
    var body: some View {
        Image(systemName: context.state.todayChangePercent >= 0 ? "arrow.up.right" : "arrow.down.right")
            .font(.caption2)
            .foregroundColor(context.state.todayChangePercent >= 0 ? .green : .red)
    }
}

// MARK: - Live Activity Bundle

@available(iOS 16.1, *)
@main
struct VibeFinanceLiveActivities: WidgetBundle {
    var body: some Widget {
        MarketLiveActivity()
        PortfolioLiveActivity()
    }
}
