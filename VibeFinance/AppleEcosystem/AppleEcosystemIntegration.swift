//
//  AppleEcosystemIntegration.swift
//  VibeFinance - Apple Ecosystem Integration
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import Foundation
import WidgetKit
import ActivityKit
import UserNotifications
import CloudKit
import WatchConnectivity
import Combine

// MARK: - Apple Ecosystem Manager

@MainActor
class AppleEcosystemManager: NSObject, ObservableObject {
    static let shared = AppleEcosystemManager()
    
    @Published var isCloudKitAvailable = false
    @Published var isWatchConnected = false
    @Published var isHandoffAvailable = false
    @Published var isContinuityAvailable = false
    
    private var cancellables = Set<AnyCancellable>()
    private var cloudKitManager: CloudKitManager?
    private var watchConnectivityManager: WatchConnectivityManager?
    private var handoffManager: HandoffManager?
    private var widgetManager: WidgetManager?
    private var liveActivityManager: LiveActivityManager?
    
    override init() {
        super.init()
        setupAppleEcosystemIntegration()
    }
    
    private func setupAppleEcosystemIntegration() {
        initializeCloudKit()
        setupWatchConnectivity()
        configureHandoff()
        setupWidgets()
        configureLiveActivities()
        setupFocusModes()
        
        ProductionConfig.log("🍎 Apple Ecosystem integration initialized", category: "ECOSYSTEM", level: .info)
    }
    
    // MARK: - CloudKit Integration
    
    private func initializeCloudKit() {
        cloudKitManager = CloudKitManager()
        
        Task {
            isCloudKitAvailable = await cloudKitManager?.checkAvailability() ?? false
            
            if isCloudKitAvailable {
                await cloudKitManager?.setupSubscriptions()
                ProductionConfig.log("☁️ CloudKit integration active", category: "ECOSYSTEM", level: .info)
            }
        }
    }
    
    // MARK: - Watch Connectivity
    
    private func setupWatchConnectivity() {
        guard WCSession.isSupported() else { return }
        
        watchConnectivityManager = WatchConnectivityManager()
        watchConnectivityManager?.delegate = self
        
        let session = WCSession.default
        session.delegate = watchConnectivityManager
        session.activate()
        
        ProductionConfig.log("⌚ Watch Connectivity setup initiated", category: "ECOSYSTEM", level: .info)
    }
    
    // MARK: - Handoff Configuration
    
    private func configureHandoff() {
        handoffManager = HandoffManager()
        isHandoffAvailable = true
        
        ProductionConfig.log("🔄 Handoff configuration complete", category: "ECOSYSTEM", level: .info)
    }
    
    // MARK: - Widget Management
    
    private func setupWidgets() {
        widgetManager = WidgetManager()
        widgetManager?.configureWidgets()
        
        ProductionConfig.log("📱 Widget configuration complete", category: "ECOSYSTEM", level: .info)
    }
    
    // MARK: - Live Activities
    
    private func configureLiveActivities() {
        if #available(iOS 16.1, *) {
            liveActivityManager = LiveActivityManager()
            liveActivityManager?.configure()
            
            ProductionConfig.log("🔴 Live Activities configured", category: "ECOSYSTEM", level: .info)
        }
    }
    
    // MARK: - Focus Modes
    
    private func setupFocusModes() {
        FocusModeManager.shared.configure()
        
        ProductionConfig.log("🎯 Focus Modes integration complete", category: "ECOSYSTEM", level: .info)
    }
    
    // MARK: - Public Interface
    
    func syncPortfolioToCloud(_ portfolio: Portfolio) async {
        await cloudKitManager?.syncPortfolio(portfolio)
    }
    
    func sendPortfolioToWatch(_ portfolio: Portfolio) {
        watchConnectivityManager?.sendPortfolioUpdate(portfolio)
    }
    
    func updateHandoffActivity(_ activity: NSUserActivity) {
        handoffManager?.updateActivity(activity)
    }
    
    func refreshWidgets() {
        widgetManager?.refreshAllWidgets()
    }
    
    func startMarketLiveActivity() {
        if #available(iOS 16.1, *) {
            liveActivityManager?.startMarketActivity()
        }
    }
    
    func stopMarketLiveActivity() {
        if #available(iOS 16.1, *) {
            liveActivityManager?.stopMarketActivity()
        }
    }
}

// MARK: - CloudKit Manager

class CloudKitManager {
    private let container = CKContainer.default()
    private let database: CKDatabase
    
    init() {
        database = container.privateCloudDatabase
    }
    
    func checkAvailability() async -> Bool {
        do {
            let status = try await container.accountStatus()
            return status == .available
        } catch {
            ProductionConfig.log("❌ CloudKit unavailable: \(error)", category: "CLOUDKIT", level: .error)
            return false
        }
    }
    
    func setupSubscriptions() async {
        do {
            // Subscribe to portfolio changes
            let subscription = CKQuerySubscription(
                recordType: "Portfolio",
                predicate: NSPredicate(value: true),
                options: [.firesOnRecordCreation, .firesOnRecordUpdate]
            )
            
            let notificationInfo = CKSubscription.NotificationInfo()
            notificationInfo.shouldSendContentAvailable = true
            subscription.notificationInfo = notificationInfo
            
            try await database.save(subscription)
            
            ProductionConfig.log("☁️ CloudKit subscriptions configured", category: "CLOUDKIT", level: .info)
        } catch {
            ProductionConfig.log("❌ CloudKit subscription failed: \(error)", category: "CLOUDKIT", level: .error)
        }
    }
    
    func syncPortfolio(_ portfolio: Portfolio) async {
        do {
            let record = CKRecord(recordType: "Portfolio")
            record["totalValue"] = portfolio.totalValue
            record["lastUpdated"] = Date()
            record["holdings"] = try JSONEncoder().encode(portfolio.holdings)
            
            try await database.save(record)
            
            ProductionConfig.log("☁️ Portfolio synced to CloudKit", category: "CLOUDKIT", level: .info)
        } catch {
            ProductionConfig.log("❌ Portfolio sync failed: \(error)", category: "CLOUDKIT", level: .error)
        }
    }
}

// MARK: - Watch Connectivity Manager

class WatchConnectivityManager: NSObject, WCSessionDelegate {
    weak var delegate: AppleEcosystemManager?
    
    func session(_ session: WCSession, activationDidCompleteWith activationState: WCSessionActivationState, error: Error?) {
        DispatchQueue.main.async {
            self.delegate?.isWatchConnected = (activationState == .activated)
            
            if let error = error {
                ProductionConfig.log("❌ Watch activation failed: \(error)", category: "WATCH", level: .error)
            } else {
                ProductionConfig.log("⌚ Watch connectivity activated", category: "WATCH", level: .info)
            }
        }
    }
    
    func sessionDidBecomeInactive(_ session: WCSession) {
        DispatchQueue.main.async {
            self.delegate?.isWatchConnected = false
        }
    }
    
    func sessionDidDeactivate(_ session: WCSession) {
        DispatchQueue.main.async {
            self.delegate?.isWatchConnected = false
        }
    }
    
    func sendPortfolioUpdate(_ portfolio: Portfolio) {
        guard WCSession.default.isReachable else { return }
        
        let portfolioData: [String: Any] = [
            "totalValue": portfolio.totalValue,
            "todayChange": portfolio.todayChange,
            "todayChangePercent": portfolio.todayChangePercent,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        WCSession.default.sendMessage(portfolioData, replyHandler: nil) { error in
            ProductionConfig.log("❌ Watch message failed: \(error)", category: "WATCH", level: .error)
        }
    }
}

// MARK: - Handoff Manager

class HandoffManager {
    private var currentActivity: NSUserActivity?
    
    func updateActivity(_ activity: NSUserActivity) {
        currentActivity?.invalidate()
        currentActivity = activity
        
        activity.becomeCurrent()
        
        ProductionConfig.log("🔄 Handoff activity updated: \(activity.activityType)", category: "HANDOFF", level: .debug)
    }
    
    func createPortfolioActivity(portfolioValue: Double) -> NSUserActivity {
        let activity = NSUserActivity("com.vibeFinance.portfolio")
        activity.title = "Portfolio - $\(String(format: "%.2f", portfolioValue))"
        activity.userInfo = ["portfolioValue": portfolioValue]
        activity.isEligibleForHandoff = true
        activity.isEligibleForSearch = true
        
        return activity
    }
    
    func createChatActivity(with advisor: String) -> NSUserActivity {
        let activity = NSUserActivity("com.vibeFinance.chat")
        activity.title = "Chat with \(advisor)"
        activity.userInfo = ["advisor": advisor]
        activity.isEligibleForHandoff = true
        
        return activity
    }
}

// MARK: - Widget Manager

class WidgetManager {
    func configureWidgets() {
        // Configure widget timeline updates
        WidgetCenter.shared.reloadAllTimelines()
        
        ProductionConfig.log("📱 Widgets configured and refreshed", category: "WIDGETS", level: .info)
    }
    
    func refreshAllWidgets() {
        WidgetCenter.shared.reloadAllTimelines()
    }
    
    func refreshPortfolioWidget() {
        WidgetCenter.shared.reloadTimelines(ofKind: "PortfolioWidget")
    }
    
    func refreshMarketWidget() {
        WidgetCenter.shared.reloadTimelines(ofKind: "MarketWidget")
    }
}

// MARK: - Live Activity Manager

@available(iOS 16.1, *)
class LiveActivityManager {
    private var currentActivity: Activity<MarketActivityAttributes>?
    
    func configure() {
        // Configure Live Activities
        ProductionConfig.log("🔴 Live Activities configured", category: "LIVE_ACTIVITY", level: .info)
    }
    
    func startMarketActivity() {
        guard ActivityAuthorizationInfo().areActivitiesEnabled else {
            ProductionConfig.log("❌ Live Activities not authorized", category: "LIVE_ACTIVITY", level: .warning)
            return
        }
        
        let attributes = MarketActivityAttributes()
        let initialState = MarketActivityAttributes.ContentState(
            spyPrice: 450.25,
            spyChange: 0.75,
            nasdaqPrice: 14125.80,
            nasdaqChange: 1.25,
            lastUpdated: Date()
        )
        
        do {
            currentActivity = try Activity.request(
                attributes: attributes,
                content: .init(state: initialState, staleDate: Date().addingTimeInterval(300))
            )
            
            ProductionConfig.log("🔴 Market Live Activity started", category: "LIVE_ACTIVITY", level: .info)
        } catch {
            ProductionConfig.log("❌ Live Activity start failed: \(error)", category: "LIVE_ACTIVITY", level: .error)
        }
    }
    
    func updateMarketActivity(spyPrice: Double, spyChange: Double, nasdaqPrice: Double, nasdaqChange: Double) {
        guard let activity = currentActivity else { return }
        
        let updatedState = MarketActivityAttributes.ContentState(
            spyPrice: spyPrice,
            spyChange: spyChange,
            nasdaqPrice: nasdaqPrice,
            nasdaqChange: nasdaqChange,
            lastUpdated: Date()
        )
        
        Task {
            await activity.update(.init(state: updatedState, staleDate: Date().addingTimeInterval(300)))
        }
    }
    
    func stopMarketActivity() {
        guard let activity = currentActivity else { return }
        
        Task {
            await activity.end(nil, dismissalPolicy: .immediate)
            currentActivity = nil
            
            ProductionConfig.log("🔴 Market Live Activity stopped", category: "LIVE_ACTIVITY", level: .info)
        }
    }
}

// MARK: - Focus Mode Manager

class FocusModeManager {
    static let shared = FocusModeManager()
    
    private init() {}
    
    func configure() {
        // Configure Focus Mode integration
        setupFocusFilters()
        
        ProductionConfig.log("🎯 Focus Mode integration configured", category: "FOCUS", level: .info)
    }
    
    private func setupFocusFilters() {
        // Setup Focus Mode filters for financial notifications
        // This would integrate with iOS Focus Mode APIs when available
    }
    
    func shouldShowNotification(type: NotificationType) -> Bool {
        // Check current Focus Mode and determine if notification should be shown
        // This is a placeholder for actual Focus Mode API integration
        return true
    }
}

// MARK: - Apple Ecosystem Extensions

extension AppleEcosystemManager: WatchConnectivityManagerDelegate {
    func watchConnectivityDidUpdate(_ isConnected: Bool) {
        isWatchConnected = isConnected
    }
}

protocol WatchConnectivityManagerDelegate: AnyObject {
    func watchConnectivityDidUpdate(_ isConnected: Bool)
}

// MARK: - Data Models

struct Portfolio {
    let totalValue: Double
    let todayChange: Double
    let todayChangePercent: Double
    let holdings: [Holding]
}

struct Holding {
    let symbol: String
    let shares: Double
    let currentPrice: Double
    let marketValue: Double
}

@available(iOS 16.1, *)
struct MarketActivityAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        let spyPrice: Double
        let spyChange: Double
        let nasdaqPrice: Double
        let nasdaqChange: Double
        let lastUpdated: Date
    }
}

enum NotificationType {
    case portfolioUpdate
    case marketAlert
    case questReminder
    case squadMessage
}

// MARK: - Apple Ecosystem View Modifier

struct AppleEcosystemModifier: ViewModifier {
    @StateObject private var ecosystemManager = AppleEcosystemManager.shared
    
    func body(content: Content) -> some View {
        content
            .onAppear {
                // Update widgets when view appears
                ecosystemManager.refreshWidgets()
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.didBecomeActiveNotification)) { _ in
                // Refresh ecosystem integration when app becomes active
                ecosystemManager.refreshWidgets()
            }
    }
}

extension View {
    func appleEcosystemIntegrated() -> some View {
        self.modifier(AppleEcosystemModifier())
    }
}
