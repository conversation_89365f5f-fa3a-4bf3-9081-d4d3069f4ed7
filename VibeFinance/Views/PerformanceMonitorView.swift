//
//  PerformanceMonitorView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct PerformanceMonitorView: View {
    @EnvironmentObject var performanceManager: PerformanceManager
    @State private var selectedTab: PerformanceTab = .overview
    @State private var isMonitoring = false
    @State private var showingExportSheet = false
    @State private var exportData: Data?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Tab Selector
                performanceTabSelector
                
                // Content
                TabView(selection: $selectedTab) {
                    PerformanceOverviewTab()
                        .tag(PerformanceTab.overview)
                    
                    CachePerformanceTab()
                        .tag(PerformanceTab.cache)
                    
                    NetworkPerformanceTab()
                        .tag(PerformanceTab.network)
                    
                    MemoryPerformanceTab()
                        .tag(PerformanceTab.memory)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Performance Monitor")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(isMonitoring ? "Stop" : "Start") {
                        toggleMonitoring()
                    }
                    .foregroundColor(isMonitoring ? .red : .green)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Export Data") {
                            exportPerformanceData()
                        }
                        
                        Button("Clear Statistics") {
                            clearStatistics()
                        }
                        
                        Button("Enable Debug Logging") {
                            enableDebugLogging()
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                    }
                }
            }
            .sheet(isPresented: $showingExportSheet) {
                if let data = exportData {
                    ActivityViewController(activityItems: [data])
                }
            }
            .onAppear {
                isMonitoring = performanceManager.isMonitoring
            }
        }
    }
    
    // MARK: - Tab Selector
    private var performanceTabSelector: some View {
        HStack(spacing: 0) {
            ForEach(PerformanceTab.allCases, id: \.self) { tab in
                Button(action: {
                    selectedTab = tab
                }) {
                    VStack(spacing: 4) {
                        Image(systemName: tab.icon)
                            .font(.title3)
                        
                        Text(tab.title)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(selectedTab == tab ? .purple : .secondary)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(
                        Rectangle()
                            .fill(selectedTab == tab ? Color.purple.opacity(0.1) : Color.clear)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .background(Color(.systemGray6))
    }
    
    // MARK: - Actions
    private func toggleMonitoring() {
        if isMonitoring {
            performanceManager.stopPerformanceMonitoring()
        } else {
            performanceManager.startPerformanceMonitoring()
        }
        isMonitoring.toggle()
    }
    
    private func exportPerformanceData() {
        exportData = performanceManager.exportPerformanceData()
        showingExportSheet = true
    }
    
    private func clearStatistics() {
        // Clear all performance statistics
        CacheManager.shared.getStatistics().reset()
        NetworkOptimizer.shared.getStatistics().reset()
        ImageCacheManager.shared.getStatistics().reset()
    }
    
    private func enableDebugLogging() {
        performanceManager.enablePerformanceDebugging()
    }
}

// MARK: - Performance Overview Tab
struct PerformanceOverviewTab: View {
    @EnvironmentObject var performanceManager: PerformanceManager
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Performance Grade
                performanceGradeCard
                
                // Key Metrics
                keyMetricsGrid
                
                // System Status
                systemStatusCard
                
                // Quick Actions
                quickActionsCard
            }
            .padding()
        }
    }
    
    private var performanceGradeCard: some View {
        VStack(spacing: 16) {
            Text("Performance Grade")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(performanceManager.performanceMetrics.performanceGrade)
                .font(.system(size: 60, weight: .bold, design: .rounded))
                .foregroundColor(gradeColor)
            
            Text(performanceStatusText)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    private var keyMetricsGrid: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
            MetricCard(
                title: "Memory Usage",
                value: "\(Int(performanceManager.performanceMetrics.memoryUsage * 100))%",
                color: performanceManager.performanceMetrics.memoryUsage > 0.8 ? .red : .green,
                icon: "memorychip"
            )
            
            MetricCard(
                title: "CPU Usage",
                value: "\(Int(performanceManager.performanceMetrics.cpuUsage * 100))%",
                color: performanceManager.performanceMetrics.cpuUsage > 0.8 ? .red : .green,
                icon: "cpu"
            )
            
            MetricCard(
                title: "Cache Hit Rate",
                value: "\(Int(performanceManager.performanceMetrics.cacheHitRate * 100))%",
                color: performanceManager.performanceMetrics.cacheHitRate > 0.7 ? .green : .orange,
                icon: "externaldrive"
            )
            
            MetricCard(
                title: "Network Latency",
                value: "\(Int(performanceManager.performanceMetrics.networkLatency * 1000))ms",
                color: performanceManager.performanceMetrics.networkLatency < 1.0 ? .green : .orange,
                icon: "network"
            )
        }
    }
    
    private var systemStatusCard: some View {
        VStack(spacing: 12) {
            Text("System Status")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                StatusRow(
                    title: "Battery Level",
                    value: "\(Int(performanceManager.performanceMetrics.batteryLevel * 100))%",
                    color: performanceManager.performanceMetrics.batteryLevel > 0.2 ? .green : .red
                )
                
                StatusRow(
                    title: "Thermal State",
                    value: performanceManager.performanceMetrics.thermalState.capitalized,
                    color: performanceManager.performanceMetrics.thermalState == "nominal" ? .green : .orange
                )
                
                StatusRow(
                    title: "Uptime",
                    value: formatUptime(performanceManager.performanceMetrics.uptime),
                    color: .blue
                )
                
                StatusRow(
                    title: "Frame Rate",
                    value: "\(Int(performanceManager.performanceMetrics.frameRate)) FPS",
                    color: performanceManager.performanceMetrics.frameRate >= 55 ? .green : .orange
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    private var quickActionsCard: some View {
        VStack(spacing: 12) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                ActionButton(
                    title: "Optimize for Low Memory",
                    icon: "memorychip",
                    color: .orange
                ) {
                    performanceManager.optimizeForLowMemory()
                }
                
                ActionButton(
                    title: "Optimize for Battery",
                    icon: "battery.100",
                    color: .green
                ) {
                    performanceManager.optimizeForBattery()
                }
                
                ActionButton(
                    title: "Optimize for Network",
                    icon: "network",
                    color: .blue
                ) {
                    performanceManager.optimizeForNetwork()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    private var gradeColor: Color {
        switch performanceManager.performanceMetrics.performanceGrade {
        case "A+", "A": return .green
        case "B": return .blue
        case "C": return .orange
        case "D", "F": return .red
        default: return .gray
        }
    }
    
    private var performanceStatusText: String {
        if performanceManager.performanceMetrics.isPerformanceGood {
            return "Your app is running smoothly with optimal performance"
        } else {
            return "Performance could be improved. Consider optimization"
        }
    }
    
    private func formatUptime(_ uptime: TimeInterval) -> String {
        let hours = Int(uptime) / 3600
        let minutes = (Int(uptime) % 3600) / 60
        return "\(hours)h \(minutes)m"
    }
}

// MARK: - Cache Performance Tab
struct CachePerformanceTab: View {
    @State private var cacheStats = CacheManager.shared.getStatistics()
    @State private var imageCacheStats = ImageCacheManager.shared.getStatistics()
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Data Cache Section
                dataCacheSection
                
                // Image Cache Section
                imageCacheSection
                
                // Cache Actions
                cacheActionsSection
            }
            .padding()
        }
        .onAppear {
            updateCacheStats()
        }
    }
    
    private var dataCacheSection: some View {
        VStack(spacing: 16) {
            Text("Data Cache")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                CacheMetricCard(
                    title: "Hit Rate",
                    value: "\(Int(CacheManager.shared.getHitRate() * 100))%",
                    color: CacheManager.shared.getHitRate() > 0.7 ? .green : .orange
                )
                
                CacheMetricCard(
                    title: "Memory Hits",
                    value: "\(cacheStats.memoryHits)",
                    color: .blue
                )
                
                CacheMetricCard(
                    title: "Disk Hits",
                    value: "\(cacheStats.diskHits)",
                    color: .purple
                )
                
                CacheMetricCard(
                    title: "Misses",
                    value: "\(cacheStats.misses)",
                    color: .red
                )
            }
        }
    }
    
    private var imageCacheSection: some View {
        VStack(spacing: 16) {
            Text("Image Cache")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                CacheMetricCard(
                    title: "Hit Rate",
                    value: "\(Int(ImageCacheManager.shared.getHitRate() * 100))%",
                    color: ImageCacheManager.shared.getHitRate() > 0.7 ? .green : .orange
                )
                
                CacheMetricCard(
                    title: "Downloads",
                    value: "\(imageCacheStats.downloads)",
                    color: .orange
                )
                
                CacheMetricCard(
                    title: "Memory Size",
                    value: "\(imageCacheStats.memoryCacheSize / 1024 / 1024)MB",
                    color: .blue
                )
                
                CacheMetricCard(
                    title: "Disk Size",
                    value: "\(imageCacheStats.diskCacheSize / 1024 / 1024)MB",
                    color: .purple
                )
            }
        }
    }
    
    private var cacheActionsSection: some View {
        VStack(spacing: 12) {
            Text("Cache Actions")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                ActionButton(
                    title: "Clear Memory Cache",
                    icon: "memorychip",
                    color: .orange
                ) {
                    CacheManager.shared.clearExpiredCache()
                    ImageCacheManager.shared.clearMemoryCache()
                    updateCacheStats()
                }
                
                ActionButton(
                    title: "Clear All Cache",
                    icon: "trash",
                    color: .red
                ) {
                    CacheManager.shared.removeAll()
                    Task {
                        await ImageCacheManager.shared.clearAllCache()
                        updateCacheStats()
                    }
                }
                
                ActionButton(
                    title: "Optimize Cache",
                    icon: "speedometer",
                    color: .green
                ) {
                    CacheManager.shared.optimizeCacheSizes()
                    updateCacheStats()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    private func updateCacheStats() {
        cacheStats = CacheManager.shared.getStatistics()
        imageCacheStats = ImageCacheManager.shared.getStatistics()
    }
}

// MARK: - Network Performance Tab
struct NetworkPerformanceTab: View {
    @State private var networkStats = NetworkOptimizer.shared.getStatistics()
    @EnvironmentObject var networkOptimizer: NetworkOptimizer
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Network Status
                networkStatusSection
                
                // Network Metrics
                networkMetricsSection
                
                // Network Actions
                networkActionsSection
            }
            .padding()
        }
        .onAppear {
            updateNetworkStats()
        }
    }
    
    private var networkStatusSection: some View {
        VStack(spacing: 16) {
            Text("Network Status")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            HStack(spacing: 20) {
                VStack(spacing: 8) {
                    Text("Connection")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(networkOptimizer.networkStatus.description)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
                
                VStack(spacing: 8) {
                    Text("Quality")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(networkOptimizer.connectionQuality.description)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(qualityColor)
                }
                
                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    private var networkMetricsSection: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
            MetricCard(
                title: "Success Rate",
                value: "\(Int(NetworkOptimizer.shared.getSuccessRate() * 100))%",
                color: NetworkOptimizer.shared.getSuccessRate() > 0.9 ? .green : .orange,
                icon: "checkmark.circle"
            )
            
            MetricCard(
                title: "Total Requests",
                value: "\(networkStats.totalRequests)",
                color: .blue,
                icon: "arrow.up.arrow.down"
            )
            
            MetricCard(
                title: "Active Requests",
                value: "\(networkStats.activeRequests)",
                color: .purple,
                icon: "clock"
            )
            
            MetricCard(
                title: "Average Latency",
                value: "\(Int(networkStats.averageLatency * 1000))ms",
                color: networkStats.averageLatency < 1.0 ? .green : .orange,
                icon: "speedometer"
            )
        }
    }
    
    private var networkActionsSection: some View {
        VStack(spacing: 12) {
            Text("Network Actions")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                ActionButton(
                    title: "Enable Battery Saving",
                    icon: "battery.100",
                    color: .green
                ) {
                    NetworkOptimizer.shared.enableBatterySavingMode()
                }
                
                ActionButton(
                    title: "Optimize for Network",
                    icon: "network",
                    color: .blue
                ) {
                    NetworkOptimizer.shared.enableNetworkOptimization()
                }
                
                ActionButton(
                    title: "Reset Statistics",
                    icon: "arrow.clockwise",
                    color: .orange
                ) {
                    NetworkOptimizer.shared.getStatistics().reset()
                    updateNetworkStats()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    private var qualityColor: Color {
        switch networkOptimizer.connectionQuality {
        case .good: return .green
        case .fair: return .orange
        case .poor: return .red
        }
    }
    
    private func updateNetworkStats() {
        networkStats = NetworkOptimizer.shared.getStatistics()
    }
}

// MARK: - Memory Performance Tab
struct MemoryPerformanceTab: View {
    @EnvironmentObject var performanceManager: PerformanceManager
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Memory Usage
                memoryUsageSection
                
                // Memory Actions
                memoryActionsSection
                
                // Memory Warnings
                if performanceManager.memoryWarningActive {
                    memoryWarningSection
                }
            }
            .padding()
        }
    }
    
    private var memoryUsageSection: some View {
        VStack(spacing: 16) {
            Text("Memory Usage")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // Memory usage visualization would go here
            Text("Memory monitoring implementation")
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    private var memoryActionsSection: some View {
        VStack(spacing: 12) {
            Text("Memory Actions")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 8) {
                ActionButton(
                    title: "Free Memory",
                    icon: "memorychip",
                    color: .orange
                ) {
                    performanceManager.optimizeForLowMemory()
                }
                
                ActionButton(
                    title: "Clear Caches",
                    icon: "trash",
                    color: .red
                ) {
                    CacheManager.shared.clearLowPriorityCache()
                    ImageCacheManager.shared.clearMemoryCache()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    private var memoryWarningSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.orange)
                Text("Memory Warning Active")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            Text("The system is running low on memory. Consider closing other apps or clearing caches.")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.orange.opacity(0.1))
        )
    }
}

// MARK: - Supporting Components

struct MetricCard: View {
    let title: String
    let value: String
    let color: Color
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct CacheMetricCard: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
        )
    }
}

struct StatusRow: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
    }
}

struct ActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Performance Tab Enum
enum PerformanceTab: String, CaseIterable {
    case overview = "Overview"
    case cache = "Cache"
    case network = "Network"
    case memory = "Memory"
    
    var title: String {
        return rawValue
    }
    
    var icon: String {
        switch self {
        case .overview: return "speedometer"
        case .cache: return "externaldrive"
        case .network: return "network"
        case .memory: return "memorychip"
        }
    }
}

// MARK: - Extensions
extension NetworkStatus {
    var description: String {
        switch self {
        case .wifi: return "WiFi"
        case .cellular: return "Cellular"
        case .offline: return "Offline"
        case .unknown: return "Unknown"
        }
    }
}

extension ConnectionQuality {
    var description: String {
        switch self {
        case .good: return "Good"
        case .fair: return "Fair"
        case .poor: return "Poor"
        }
    }
}

// MARK: - Activity View Controller
struct ActivityViewController: UIViewControllerRepresentable {
    let activityItems: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        return UIActivityViewController(activityItems: activityItems, applicationActivities: nil)
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

#Preview {
    PerformanceMonitorView()
        .environmentObject(PerformanceManager.shared)
        .environmentObject(NetworkOptimizer.shared)
}
