//
//  CreateSquadView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct CreateSquadView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var squadManager: SquadManager
    @EnvironmentObject var userManager: UserManager
    
    @State private var squadName = ""
    @State private var squadDescription = ""
    @State private var selectedEmoji = "💰"
    @State private var isPublic = true
    @State private var maxMembers = 50
    @State private var investmentFocus: InvestmentFocus = .balanced
    @State private var riskLevel: RiskLevel = .moderate
    @State private var minimumInvestment = 10.0
    @State private var currentStep = 1
    @State private var isCreating = false
    
    private let totalSteps = 4
    private let emojis = ["💰", "🚀", "📈", "💎", "🌟", "🔥", "⚡", "🎯", "🌱", "🏆"]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Progress Header
                CreateSquadProgressHeader(
                    currentStep: currentStep,
                    totalSteps: totalSteps
                )
                
                // Step Content
                ScrollView {
                    VStack(spacing: 24) {
                        switch currentStep {
                        case 1:
                            BasicInfoStep(
                                squadName: $squadName,
                                squadDescription: $squadDescription,
                                selectedEmoji: $selectedEmoji,
                                emojis: emojis
                            )
                        case 2:
                            PrivacySettingsStep(
                                isPublic: $isPublic,
                                maxMembers: $maxMembers
                            )
                        case 3:
                            InvestmentSettingsStep(
                                investmentFocus: $investmentFocus,
                                riskLevel: $riskLevel,
                                minimumInvestment: $minimumInvestment
                            )
                        case 4:
                            ReviewStep(
                                squadName: squadName,
                                squadDescription: squadDescription,
                                selectedEmoji: selectedEmoji,
                                isPublic: isPublic,
                                maxMembers: maxMembers,
                                investmentFocus: investmentFocus,
                                riskLevel: riskLevel,
                                minimumInvestment: minimumInvestment
                            )
                        default:
                            EmptyView()
                        }
                    }
                    .padding()
                }
                
                // Navigation Buttons
                HStack(spacing: 16) {
                    if currentStep > 1 {
                        Button("Previous") {
                            withAnimation {
                                currentStep -= 1
                            }
                        }
                        .buttonStyle(SecondaryButtonStyle())
                    }
                    
                    Button(currentStep == totalSteps ? "Create Squad" : "Next") {
                        if currentStep == totalSteps {
                            createSquad()
                        } else {
                            withAnimation {
                                currentStep += 1
                            }
                        }
                    }
                    .buttonStyle(PrimaryButtonStyle())
                    .disabled(!canProceed || isCreating)
                }
                .padding()
                .background(Color(.systemBackground))
            }
            .navigationTitle("Create Squad")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden()
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var canProceed: Bool {
        switch currentStep {
        case 1:
            return !squadName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
                   !squadDescription.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        case 2, 3, 4:
            return true
        default:
            return false
        }
    }
    
    private func createSquad() {
        guard let user = userManager.user else { return }
        
        isCreating = true
        
        let squad = Squad(
            name: squadName,
            description: squadDescription,
            emoji: selectedEmoji,
            creatorID: user.id,
            isPublic: isPublic,
            maxMembers: maxMembers
        )
        
        Task {
            await squadManager.createSquad(squad)
            await MainActor.run {
                self.isCreating = false
                self.dismiss()
            }
        }
    }
}

// MARK: - Progress Header
struct CreateSquadProgressHeader: View {
    let currentStep: Int
    let totalSteps: Int
    
    var progress: Double {
        Double(currentStep) / Double(totalSteps)
    }
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Step \(currentStep) of \(totalSteps)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text("\(Int(progress * 100))%")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                .scaleEffect(y: 2)
        }
        .padding()
        .background(Color(.systemGray6))
    }
}

// MARK: - Step 1: Basic Info
struct BasicInfoStep: View {
    @Binding var squadName: String
    @Binding var squadDescription: String
    @Binding var selectedEmoji: String
    let emojis: [String]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Squad Details")
                    .font(.title2)
                    .fontWeight(.bold)
                Text("Give your squad a name and description that reflects your investment goals")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // Emoji Selection
            VStack(alignment: .leading, spacing: 12) {
                Text("Choose an emoji")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 5), spacing: 12) {
                    ForEach(emojis, id: \.self) { emoji in
                        Button(action: {
                            selectedEmoji = emoji
                        }) {
                            Text(emoji)
                                .font(.title)
                                .frame(width: 50, height: 50)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(selectedEmoji == emoji ? Color.purple.opacity(0.2) : Color(.systemGray6))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(selectedEmoji == emoji ? Color.purple : Color.clear, lineWidth: 2)
                                        )
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
            
            // Squad Name
            VStack(alignment: .leading, spacing: 8) {
                Text("Squad name")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                TextField("Enter squad name", text: $squadName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .font(.body)
            }
            
            // Squad Description
            VStack(alignment: .leading, spacing: 8) {
                Text("Description")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                TextEditor(text: $squadDescription)
                    .frame(minHeight: 100)
                    .padding(8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
                    .overlay(
                        Text(squadDescription.isEmpty ? "Describe your squad's investment strategy and goals..." : "")
                            .foregroundColor(.gray)
                            .allowsHitTesting(false)
                            .padding(.leading, 12)
                            .padding(.top, 16),
                        alignment: .topLeading
                    )
            }
        }
    }
}

// MARK: - Step 2: Privacy Settings
struct PrivacySettingsStep: View {
    @Binding var isPublic: Bool
    @Binding var maxMembers: Int
    
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Privacy & Size")
                    .font(.title2)
                    .fontWeight(.bold)
                Text("Configure who can join your squad and how many members you want")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // Privacy Setting
            VStack(alignment: .leading, spacing: 16) {
                Text("Squad Privacy")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                VStack(spacing: 12) {
                    PrivacyOptionCard(
                        title: "Public Squad",
                        description: "Anyone can discover and join your squad",
                        icon: "globe",
                        isSelected: isPublic
                    ) {
                        isPublic = true
                    }
                    
                    PrivacyOptionCard(
                        title: "Private Squad",
                        description: "Only people with an invite link can join",
                        icon: "lock",
                        isSelected: !isPublic
                    ) {
                        isPublic = false
                    }
                }
            }
            
            // Max Members
            VStack(alignment: .leading, spacing: 12) {
                Text("Maximum Members")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("\(maxMembers) members")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        Spacer()
                    }
                    
                    Slider(value: Binding(
                        get: { Double(maxMembers) },
                        set: { maxMembers = Int($0) }
                    ), in: 5...500, step: 5)
                    .accentColor(.purple)
                    
                    HStack {
                        Text("5")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Spacer()
                        Text("500")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
}

// MARK: - Privacy Option Card
struct PrivacyOptionCard: View {
    let title: String
    let description: String
    let icon: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(isSelected ? .white : .purple)
                    .frame(width: 40, height: 40)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(isSelected ? Color.purple : Color.purple.opacity(0.1))
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .font(.title3)
                    .foregroundColor(isSelected ? .purple : .gray)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Step 3: Investment Settings
struct InvestmentSettingsStep: View {
    @Binding var investmentFocus: InvestmentFocus
    @Binding var riskLevel: RiskLevel
    @Binding var minimumInvestment: Double
    
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Investment Strategy")
                    .font(.title2)
                    .fontWeight(.bold)
                Text("Define your squad's investment approach and requirements")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // Investment Focus
            VStack(alignment: .leading, spacing: 12) {
                Text("Investment Focus")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                    ForEach(InvestmentFocus.allCases, id: \.self) { focus in
                        InvestmentFocusCard(
                            focus: focus,
                            isSelected: investmentFocus == focus
                        ) {
                            investmentFocus = focus
                        }
                    }
                }
            }
            
            // Risk Level
            VStack(alignment: .leading, spacing: 12) {
                Text("Risk Level")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                VStack(spacing: 8) {
                    ForEach(RiskLevel.allCases, id: \.self) { risk in
                        RiskLevelRow(
                            riskLevel: risk,
                            isSelected: riskLevel == risk
                        ) {
                            riskLevel = risk
                        }
                    }
                }
            }
            
            // Minimum Investment
            VStack(alignment: .leading, spacing: 12) {
                Text("Minimum Investment")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("$\(Int(minimumInvestment))")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        Spacer()
                    }
                    
                    Slider(value: $minimumInvestment, in: 1...100, step: 1)
                        .accentColor(.purple)
                    
                    HStack {
                        Text("$1")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Spacer()
                        Text("$100")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        }
    }
}

// MARK: - Investment Focus Card
struct InvestmentFocusCard: View {
    let focus: InvestmentFocus
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(focus.emoji)
                    .font(.title)
                
                Text(focus.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.purple.opacity(0.2) : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.purple : Color.clear, lineWidth: 2)
                    )
            )
            .foregroundColor(isSelected ? .purple : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Risk Level Row
struct RiskLevelRow: View {
    let riskLevel: RiskLevel
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: riskLevel.icon)
                    .foregroundColor(riskLevel.color)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(riskLevel.displayName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Text(riskLevel.description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .purple : .gray)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.purple.opacity(0.1) : Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Step 4: Review
struct ReviewStep: View {
    let squadName: String
    let squadDescription: String
    let selectedEmoji: String
    let isPublic: Bool
    let maxMembers: Int
    let investmentFocus: InvestmentFocus
    let riskLevel: RiskLevel
    let minimumInvestment: Double
    
    var body: some View {
        VStack(alignment: .leading, spacing: 24) {
            VStack(alignment: .leading, spacing: 8) {
                Text("Review & Create")
                    .font(.title2)
                    .fontWeight(.bold)
                Text("Review your squad settings before creating")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            // Squad Preview
            VStack(alignment: .leading, spacing: 16) {
                HStack(spacing: 16) {
                    Text(selectedEmoji)
                        .font(.system(size: 40))
                        .frame(width: 60, height: 60)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.purple.opacity(0.1))
                        )
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(squadName)
                            .font(.title3)
                            .fontWeight(.bold)
                        Text(squadDescription)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                }
                
                Divider()
                
                // Settings Summary
                VStack(spacing: 12) {
                    ReviewRow(title: "Privacy", value: isPublic ? "Public" : "Private")
                    ReviewRow(title: "Max Members", value: "\(maxMembers)")
                    ReviewRow(title: "Investment Focus", value: investmentFocus.displayName)
                    ReviewRow(title: "Risk Level", value: riskLevel.displayName)
                    ReviewRow(title: "Min Investment", value: "$\(Int(minimumInvestment))")
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemGray6))
            )
        }
    }
}

// MARK: - Review Row
struct ReviewRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

// MARK: - Supporting Types
enum InvestmentFocus: String, CaseIterable {
    case growth = "growth"
    case value = "value"
    case dividend = "dividend"
    case tech = "tech"
    case esg = "esg"
    case balanced = "balanced"
    
    var displayName: String {
        switch self {
        case .growth: return "Growth"
        case .value: return "Value"
        case .dividend: return "Dividend"
        case .tech: return "Technology"
        case .esg: return "ESG/Sustainable"
        case .balanced: return "Balanced"
        }
    }
    
    var emoji: String {
        switch self {
        case .growth: return "🚀"
        case .value: return "💎"
        case .dividend: return "💰"
        case .tech: return "💻"
        case .esg: return "🌱"
        case .balanced: return "⚖️"
        }
    }
}



#Preview {
    CreateSquadView()
        .environmentObject(SquadManager())
        .environmentObject(UserManager())
}
