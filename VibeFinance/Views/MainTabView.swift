//
//  MainTabView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct MainTabView: View {
    @EnvironmentObject var userManager: UserManager
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    @EnvironmentObject var realTradingManager: RealTradingManager
    @EnvironmentObject var analyticsManager: AnalyticsManager
    @EnvironmentObject var paymentManager: PaymentManager
    @State private var selectedTab = 0

    var body: some View {
        TabView(selection: $selectedTab) {
            // Feed Tab
            FeedView()
                .tabItem {
                    Image(systemName: selectedTab == 0 ? "house.fill" : "house")
                    Text("Feed")
                }
                .tag(0)

            // Quests Tab
            QuestsView()
                .tabItem {
                    Image(systemName: selectedTab == 1 ? "target" : "target")
                    Text("Quests")
                }
                .tag(1)

            // Squads Tab (Pro only)
            if userManager.canAccessFeature(.squads) {
                SquadsView()
                    .tabItem {
                        Image(systemName: selectedTab == 2 ? "person.3.fill" : "person.3")
                        Text("Squads")
                    }
                    .tag(2)
            }

            // Trading Tab (Pro users get Real Trading, others get Simulator)
            if userManager.canAccessFeature(.realInvestments) {
                RealTradingView()
                    .tabItem {
                        Image(systemName: selectedTab == 3 ? "chart.line.uptrend.xyaxis.fill" : "chart.line.uptrend.xyaxis")
                        Text("Real Trading")
                    }
                    .tag(3)
            } else if userManager.canAccessFeature(.simulator) {
                SimulatorView()
                    .tabItem {
                        Image(systemName: selectedTab == 3 ? "gamecontroller.fill" : "gamecontroller")
                        Text("Simulator")
                    }
                    .tag(3)
            }

            // Analytics Tab (Pro only)
            if userManager.canAccessFeature(.advancedAnalytics) {
                AdvancedAnalyticsView()
                    .tabItem {
                        Image(systemName: selectedTab == 4 ? "chart.bar.xaxis.ascending" : "chart.bar.xaxis")
                        Text("Analytics")
                    }
                    .tag(4)
            }

            // AI Chat Tab
            SimpleAIChatView()
                .tabItem {
                    Image(systemName: selectedTab == (userManager.canAccessFeature(.advancedAnalytics) ? 5 : 4) ? "brain.head.profile.fill" : "brain.head.profile")
                    Text("AI Assistant")
                }
                .tag(userManager.canAccessFeature(.advancedAnalytics) ? 5 : 4)

            // Testing Tab (temporary for debugging)
            TestingView()
                .tabItem {
                    Image(systemName: selectedTab == 5 ? "wrench.and.screwdriver.fill" : "wrench.and.screwdriver")
                    Text("Testing")
                }
                .tag(5)

            // Profile Tab
            ProfileView()
                .tabItem {
                    Image(systemName: selectedTab == 6 ? "person.fill" : "person")
                    Text("Profile")
                }
                .tag(6)

            TestingView()
                .tabItem {
                    Image(systemName: selectedTab == 7 ? "testtube.2" : "testtube.2")
                    Text("Testing")
                }
                .tag(7)
        }
        .accentColor(.purple)
        .onAppear {
            setupTabBarAppearance()
        }
    }

    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemBackground

        // Selected item color
        appearance.selectionIndicatorTintColor = UIColor.systemPurple

        // Normal item color
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor.systemGray
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor.systemGray
        ]

        // Selected item color
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor.systemPurple
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor.systemPurple
        ]

        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

// MARK: - Real Feed View Implementation

struct FeedView: View {
    @EnvironmentObject var feedManager: FeedManager
    @EnvironmentObject var userManager: UserManager
    @State private var showingFilters = false
    @State private var selectedFilter: FeedFilter = .all
    @State private var showingSubscriptionView = false
    @State private var showingPerformanceMonitor = false

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    // Feed Header with Quick Actions
                    FeedHeaderView(
                        showingFilters: $showingFilters,
                        selectedFilter: $selectedFilter
                    )

                    // Feed Items
                    if feedManager.isLoading && feedManager.feedItems.isEmpty {
                        FeedLoadingView()
                    } else {
                        ForEach(filteredFeedItems) { feedItem in
                            FeedItemCard(feedItem: feedItem) { action in
                                handleFeedAction(action, for: feedItem)
                            }
                        }

                        // Load More Button
                        if feedManager.hasMoreItems {
                            LoadMoreButton {
                                Task {
                                    await feedManager.loadMoreFeedItems()
                                }
                            }
                        }
                    }
                }
                .padding(.horizontal)
            }
            .navigationTitle("Your Feed")
            .navigationBarTitleDisplayMode(.large)
            .refreshable {
                await refreshFeed()
            }
            .onAppear {
                Task {
                    await loadInitialFeed()
                }
            }
            .sheet(isPresented: $showingFilters) {
                FeedFiltersView(selectedFilter: $selectedFilter)
            }
            .sheet(isPresented: $showingSubscriptionView) {
                EnhancedSubscriptionView()
            }
            .sheet(isPresented: $showingPerformanceMonitor) {
                PerformanceMonitorView()
            }
        }
    }

    private var filteredFeedItems: [FeedItem] {
        switch selectedFilter {
        case .all:
            return feedManager.feedItems
        case .stocks:
            return feedManager.feedItems.filter { $0.content.category == .stocks }
        case .crypto:
            return feedManager.feedItems.filter { $0.content.category == .crypto }
        case .news:
            return feedManager.feedItems.filter { $0.content.category == .news }
        case .education:
            return feedManager.feedItems.filter { $0.content.category == .education }
        case .bookmarked:
            return feedManager.feedItems.filter { $0.isBookmarked }
        }
    }

    private func loadInitialFeed() async {
        guard let user = userManager.user else { return }
        await feedManager.generateDailyFeed(for: user)
    }

    private func refreshFeed() async {
        guard let user = userManager.user else { return }
        await feedManager.refreshFeed(for: user)
    }

    private func handleFeedAction(_ action: FeedAction, for feedItem: FeedItem) {
        Task {
            switch action {
            case .like:
                await feedManager.addReaction(.like, to: feedItem)
            case .bookmark:
                await feedManager.toggleBookmark(for: feedItem)
            case .share:
                // Handle sharing
                break
            case .invest:
                // Navigate to investment flow
                break
            }
        }
    }
}

struct QuestsView: View {
    @State private var dailyQuests: [Quest] = [
        Quest(
            title: "Market Analysis Master",
            description: "Analyze 3 trending stocks and make AI-powered predictions",
            category: .stocks,
            difficulty: .intermediate,
            xpReward: 100,
            estimatedTime: 15,
            tasks: [
                QuestTask(title: "Research Apple Stock", description: "Analyze AAPL fundamentals", type: .research, xpReward: 30, order: 1),
                QuestTask(title: "Predict Price Movement", description: "Make a prediction for next week", type: .multipleChoice, xpReward: 40, order: 2),
                QuestTask(title: "Compare with AI", description: "See how your prediction matches AI analysis", type: .simulation, xpReward: 30, order: 3)
            ],
            isDaily: true,
            expiresAt: Calendar.current.date(byAdding: .day, value: 1, to: Date())
        ),
        Quest(
            title: "Portfolio Diversification",
            description: "Add different asset classes to reduce risk",
            category: .investing,
            difficulty: .advanced,
            xpReward: 200,
            estimatedTime: 25,
            tasks: [
                QuestTask(title: "Learn About ETFs", description: "Watch educational video on ETFs", type: .video, xpReward: 50, order: 1),
                QuestTask(title: "Choose Asset Classes", description: "Select 3 different asset classes", type: .multipleChoice, xpReward: 75, order: 2),
                QuestTask(title: "Make Investment", description: "Invest in your chosen assets", type: .simulation, xpReward: 75, order: 3)
            ],
            isDaily: true,
            expiresAt: Calendar.current.date(byAdding: .day, value: 1, to: Date())
        ),
        Quest(
            title: "Risk Assessment Mastery",
            description: "Complete comprehensive risk tolerance evaluation",
            category: .basics,
            difficulty: .beginner,
            xpReward: 50,
            estimatedTime: 10,
            tasks: [
                QuestTask(title: "Risk Quiz", description: "Answer 10 questions about risk tolerance", type: .multipleChoice, xpReward: 25, order: 1),
                QuestTask(title: "Review Results", description: "Understand your risk profile", type: .reading, xpReward: 25, order: 2)
            ],
            isDaily: true,
            expiresAt: Calendar.current.date(byAdding: .day, value: 1, to: Date())
        )
    ]

    @State private var weeklyChallenge = WeeklyChallenge(
        title: "Beat the Market",
        description: "Outperform S&P 500 this week with your picks",
        reward: 5000,
        xpReward: 1000,
        participants: 1247,
        timeRemaining: "3d 14h",
        currentRank: 23,
        performance: 8.7
    )

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    WeeklyChallengeCard(challenge: weeklyChallenge)
                    dailyQuestsSection
                    achievementsSection
                }
                .padding()
            }
            .navigationTitle("Quests")
            .refreshable {
                await refreshQuests()
            }
        }
    }

    private var dailyQuestsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("🎯 Daily Quests")
                    .font(.title2)
                    .fontWeight(.bold)
                Spacer()
                Text("0/\(dailyQuests.count)")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            ForEach(dailyQuests) { quest in
                QuestCard(quest: quest) {
                    startQuest(quest)
                }
            }
        }
    }

    private var achievementsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("🏆 Recent Achievements")
                .font(.title2)
                .fontWeight(.bold)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                AchievementBadge(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "First Profit",
                    description: "Made your first profitable trade",
                    isUnlocked: true
                )
                AchievementBadge(
                    icon: "person.3.fill",
                    title: "Squad Leader",
                    description: "Created your first investment squad",
                    isUnlocked: true
                )
                AchievementBadge(
                    icon: "star.fill",
                    title: "Week Warrior",
                    description: "Complete 7 daily quests in a row",
                    isUnlocked: false
                )
                AchievementBadge(
                    icon: "crown.fill",
                    title: "Market Master",
                    description: "Beat market for 30 days straight",
                    isUnlocked: false
                )
            }
        }
        .padding(.top)
    }

    private func startQuest(_ quest: Quest) {
        // Navigate to quest detail view
        let questDetailView = QuestDetailView(quest: quest)
            .environmentObject(QuestManager())
            .environmentObject(UserManager())

        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            let hostingController = UIHostingController(rootView: questDetailView)
            hostingController.modalPresentationStyle = .fullScreen
            window.rootViewController?.present(hostingController, animated: true)
        }
    }

    private func refreshQuests() async {
        // Simulate API call to refresh quests
        try? await Task.sleep(nanoseconds: 1_000_000_000)
        // In real app, this would fetch new quests from the server
    }
}

struct SquadsView: View {
    @State private var squads: [Squad] = [
        Squad(
            name: "Tech Titans 💻",
            description: "Investing in the future of technology",
            emoji: "💻",
            creatorID: UUID(),
            isPublic: true,
            maxMembers: 500
        ),
        Squad(
            name: "Green Energy Squad 🌱",
            description: "Sustainable investing for the planet",
            emoji: "🌱",
            creatorID: UUID(),
            isPublic: true,
            maxMembers: 300
        ),
        Squad(
            name: "Dividend Dynasty 💰",
            description: "Building wealth through dividend income",
            emoji: "💰",
            creatorID: UUID(),
            isPublic: true,
            maxMembers: 200
        )
    ]

    @State private var showingCreateSquad = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header Stats
                    VStack(spacing: 16) {
                        HStack {
                            VStack(alignment: .leading) {
                                Text("Your Squads")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                Text("Collaborative investing made simple")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            Spacer()
                            Button(action: { showingCreateSquad = true }) {
                                Image(systemName: "plus.circle.fill")
                                    .font(.title)
                                    .foregroundColor(.purple)
                            }
                        }

                        // Quick Stats
                        HStack(spacing: 20) {
                            StatCard(title: "Total Value", value: "$4.2M", icon: "dollarsign.circle.fill", color: .green)
                            StatCard(title: "Avg Return", value: "+8.4%", icon: "chart.line.uptrend.xyaxis", color: .blue)
                            StatCard(title: "Active Squads", value: "3", icon: "person.3.fill", color: .purple)
                        }
                    }

                    // Featured Squads
                    VStack(alignment: .leading, spacing: 12) {
                        Text("🔥 Trending Squads")
                            .font(.title2)
                            .fontWeight(.bold)

                        ForEach(squads) { squad in
                            SquadCard(squad: squad) {
                                joinSquad(squad)
                            }
                        }
                    }

                    // AI Recommendations
                    VStack(alignment: .leading, spacing: 12) {
                        Text("🤖 AI Recommended for You")
                            .font(.title2)
                            .fontWeight(.bold)

                        AIRecommendationSquadCard(
                            title: "Crypto Pioneers ₿",
                            description: "Based on your risk profile and interest in emerging tech",
                            expectedReturn: 15.2,
                            memberCount: 89
                        )
                    }

                    // Performance Leaderboard
                    VStack(alignment: .leading, spacing: 12) {
                        Text("🏆 Top Performing Squads")
                            .font(.title2)
                            .fontWeight(.bold)

                        ForEach(Array(squads.enumerated()), id: \.element.id) { index, squad in
                            SquadLeaderboardRow(
                                rank: index + 1,
                                squad: squad
                            )
                        }
                    }
                }
                .padding()
            }
            .navigationTitle("Investment Squads")
            .sheet(isPresented: $showingCreateSquad) {
                CreateSquadView()
            }
        }
    }

    private func joinSquad(_ squad: Squad) {
        // Handle squad joining logic
        print("Joining squad: \(squad.name)")
    }
}

struct SimulatorView: View {
    @EnvironmentObject var simulatorManager: SimulatorManager
    @EnvironmentObject var userManager: UserManager
    @State private var selectedTab = 0
    @State private var showingStockSearch = false
    @State private var showingTradeSheet = false
    @State private var selectedStock: StockPrice?

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Portfolio Header
                SimulatorPortfolioHeader(
                    virtualBalance: simulatorManager.virtualBalance,
                    totalValue: simulatorManager.totalPortfolioValue,
                    dailyChange: simulatorManager.dailyChange,
                    dailyChangePercent: simulatorManager.dailyChangePercent
                )

                // Tab Selector
                SimulatorTabSelector(selectedTab: $selectedTab)

                // Content
                TabView(selection: $selectedTab) {
                    // Portfolio Tab
                    SimulatorPortfolioView()
                        .tag(0)

                    // Market Tab
                    SimulatorMarketView(
                        onStockSelected: { stock in
                            selectedStock = stock
                            showingTradeSheet = true
                        }
                    )
                    .tag(1)

                    // History Tab
                    SimulatorHistoryView()
                        .tag(2)

                    // Leaderboard Tab
                    SimulatorLeaderboardView()
                        .tag(3)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("Investment Simulator")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingStockSearch = true
                    }) {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.purple)
                    }
                }
            }
            .sheet(isPresented: $showingStockSearch) {
                SimulatorStockSearchView { stock in
                    selectedStock = stock
                    showingTradeSheet = true
                }
            }
            .sheet(isPresented: $showingTradeSheet) {
                if let stock = selectedStock {
                    SimulatorTradeView(stock: stock)
                }
            }
            .onAppear {
                Task {
                    await simulatorManager.loadPortfolio()
                }
            }
        }
    }
}

struct ChatView: View {
    @State private var messages: [ChatMessage] = [
        ChatMessage(
            id: UUID(),
            content: "Hello! I'm your AI financial advisor. I can help you with investment strategies, market analysis, portfolio optimization, and financial planning. What would you like to discuss today?",
            isFromUser: false,
            timestamp: Date().addingTimeInterval(-300)
        )
    ]
    @State private var newMessage = ""
    @State private var isTyping = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Quick Action Buttons
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        QuickChatButton(icon: "chart.line.uptrend.xyaxis", title: "Market Analysis", action: {
                            sendQuickMessage("Give me a market analysis for today")
                        })
                        QuickChatButton(icon: "dollarsign.circle", title: "Investment Ideas", action: {
                            sendQuickMessage("What are some good investment opportunities right now?")
                        })
                        QuickChatButton(icon: "person.crop.circle.badge.questionmark", title: "Portfolio Review", action: {
                            sendQuickMessage("Can you review my portfolio and suggest improvements?")
                        })
                        QuickChatButton(icon: "brain.head.profile", title: "AI Insights", action: {
                            sendQuickMessage("What are your AI insights for my financial goals?")
                        })
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical, 8)
                .background(Color(.systemGray6))

                // Messages List
                ScrollViewReader { proxy in
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(messages) { message in
                                ChatMessageView(message: message)
                            }

                            if isTyping {
                                TypingIndicatorView()
                            }
                        }
                        .padding()
                    }
                    .onChange(of: messages.count) { _ in
                        withAnimation {
                            proxy.scrollTo(messages.last?.id, anchor: .bottom)
                        }
                    }
                }

                // Message Input
                HStack(spacing: 12) {
                    TextField("Ask me anything about finance...", text: $newMessage, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(1...4)

                    Button(action: sendMessage) {
                        Image(systemName: "paperplane.fill")
                            .foregroundColor(.white)
                            .frame(width: 36, height: 36)
                            .background(
                                Circle()
                                    .fill(newMessage.isEmpty ? Color.gray : Color.purple)
                            )
                    }
                    .disabled(newMessage.isEmpty)
                }
                .padding()
                .background(Color(.systemBackground))
            }
            .navigationTitle("AI Advisor")
            .navigationBarTitleDisplayMode(.inline)
        }
    }

    private func sendMessage() {
        guard !newMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }

        let userMessage = ChatMessage(
            id: UUID(),
            content: newMessage,
            isFromUser: true,
            timestamp: Date()
        )

        messages.append(userMessage)
        let messageToSend = newMessage
        newMessage = ""

        // Simulate AI response
        generateAIResponse(to: messageToSend)
    }

    private func sendQuickMessage(_ message: String) {
        let userMessage = ChatMessage(
            id: UUID(),
            content: message,
            isFromUser: true,
            timestamp: Date()
        )

        messages.append(userMessage)
        generateAIResponse(to: message)
    }

    private func generateAIResponse(to message: String) {
        isTyping = true

        Task {
            let aiService = AIServiceManager.shared
            let response = await aiService.getChatResponse(message: message)

            await MainActor.run {
                self.isTyping = false

                let aiMessage = ChatMessage(
                    id: UUID(),
                    content: response,
                    isFromUser: false,
                    timestamp: Date()
                )

                self.messages.append(aiMessage)
            }
        }
    }

    private func generateSmartResponse(for message: String) -> String {
        let lowercased = message.lowercased()

        if lowercased.contains("market") || lowercased.contains("analysis") {
            return "📈 Based on current market conditions, I'm seeing strong momentum in tech stocks and renewable energy sectors. The S&P 500 is showing bullish patterns, but I recommend maintaining a diversified portfolio. Would you like me to analyze specific stocks or sectors?"
        } else if lowercased.contains("investment") || lowercased.contains("invest") {
            return "💡 For your investment strategy, I recommend a balanced approach: 60% stocks (mix of growth and value), 30% bonds, and 10% alternative investments like REITs. Given your risk profile, consider dollar-cost averaging into index funds. What's your investment timeline?"
        } else if lowercased.contains("portfolio") {
            return "📊 Your portfolio is performing well with a 12.3% YTD return! I notice you're overweight in tech (45% vs recommended 25%). Consider rebalancing into healthcare and international markets. Your risk-adjusted returns could improve by 2-3% annually with better diversification."
        } else if lowercased.contains("crypto") || lowercased.contains("bitcoin") {
            return "₿ Cryptocurrency can be a valuable portfolio addition, but limit exposure to 5-10% of total assets. Bitcoin and Ethereum remain the most established options. Consider DCA strategy and only invest what you can afford to lose. The volatility is high but long-term prospects remain positive."
        } else {
            return "🤖 I understand you're looking for financial guidance. I can help with investment strategies, portfolio analysis, market insights, risk assessment, and financial planning. Could you be more specific about what aspect of your finances you'd like to discuss?"
        }
    }
}

struct ProfileView: View {
    @EnvironmentObject var authManager: AuthManager
    @EnvironmentObject var userManager: UserManager

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Profile Header
                VStack(spacing: 12) {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [.purple, .pink],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 80, height: 80)
                        .overlay(
                            Text(userManager.user?.username.prefix(1).uppercased() ?? "U")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                        )

                    Text(userManager.user?.username ?? "User")
                        .font(.title2)
                        .fontWeight(.semibold)

                    Text(userManager.user?.email ?? "")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 20)

                // Stats
                if let stats = userManager.getUserStats() {
                    HStack(spacing: 30) {
                        VStack {
                            Text("\(stats.level)")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.purple)
                            Text("Level")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        VStack {
                            Text("\(stats.xp)")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.orange)
                            Text("XP")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        VStack {
                            Text("\(stats.daysActive)")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.green)
                            Text("Days")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.vertical, 20)
                }

                // Subscription Status
                Button(action: {
                    showingSubscriptionView = true
                }) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Subscription")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.primary)

                            Text(userManager.user?.subscriptionStatus.displayName ?? "Free")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        Text(userManager.user?.subscriptionStatus.displayName ?? "Free")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.purple)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.purple.opacity(0.1))
                            )

                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
                }
                .buttonStyle(PlainButtonStyle())
                .padding(.horizontal, 20)

                // Performance Monitor (Development only)
                if DevelopmentConfig.isDevelopmentMode && DevelopmentConfig.showPerformanceMetrics {
                    Button(action: {
                        showingPerformanceMonitor = true
                    }) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("Performance Monitor")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.primary)

                                Text("Debug & Optimization")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Image(systemName: "speedometer")
                                .font(.title3)
                                .foregroundColor(.orange)

                            Image(systemName: "chevron.right")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                    .padding(.horizontal, 20)
                }

                Spacer()

                // Sign Out Button
                Button(action: {
                    Task {
                        await authManager.signOut()
                    }
                }) {
                    Text("Sign Out")
                        .font(.headline)
                        .foregroundColor(.red)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.red, lineWidth: 1)
                        )
                }
                .padding(.horizontal, 20)
                .padding(.bottom, 30)
            }
            .navigationTitle("Profile")
        }
    }
}



#Preview {
    MainTabView()
        .environmentObject(AuthManager())
        .environmentObject(UserManager())
        .environmentObject(SubscriptionManager())
}
