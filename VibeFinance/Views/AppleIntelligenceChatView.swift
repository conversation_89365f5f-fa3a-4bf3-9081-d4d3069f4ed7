//
//  AppleIntelligenceChatView.swift
//  VibeFinance - Apple Intelligence Powered
//
//  Created by MAGESH DHANASEKARAN on 12/26/24.
//  Apple Design Award ready chat interface with Apple Intelligence
//

import SwiftUI
import Speech
import AVFoundation

/// Apple Intelligence powered chat interface
/// Implements Apple's design guidelines and latest iOS features
struct AppleIntelligenceChatView: View {
    @StateObject private var aiManager = AppleIntelligenceManager.shared
    @StateObject private var speechManager = SpeechManager()
    @StateObject private var hapticManager = HapticManager()
    
    @State private var messages: [IntelligentMessage] = []
    @State private var inputText = ""
    @State private var isRecording = false
    @State private var showingWritingTools = false
    @State private var selectedMessage: IntelligentMessage?
    
    // Apple Intelligence features
    @State private var suggestedResponses: [String] = []
    @State private var smartSuggestions: [SmartSuggestion] = []
    @State private var contextualActions: [ContextualAction] = []
    
    // Accessibility
    @Environment(\.accessibilityReduceMotion) var reduceMotion
    @Environment(\.dynamicTypeSize) var dynamicTypeSize
    @Environment(\.colorScheme) var colorScheme
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Smart Suggestions Bar
                if !smartSuggestions.isEmpty {
                    SmartSuggestionsBar(suggestions: smartSuggestions) { suggestion in
                        handleSmartSuggestion(suggestion)
                    }
                    .transition(.move(edge: .top).combined(with: .opacity))
                }
                
                // Messages List
                ScrollViewReader { proxy in
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            ForEach(messages) { message in
                                IntelligentMessageView(
                                    message: message,
                                    onAction: { action in
                                        handleMessageAction(action, for: message)
                                    }
                                )
                                .id(message.id)
                                .transition(.asymmetric(
                                    insertion: .move(edge: .bottom).combined(with: .opacity),
                                    removal: .move(edge: .top).combined(with: .opacity)
                                ))
                            }
                        }
                        .padding()
                    }
                    .onChange(of: messages.count) { _ in
                        withAnimation(.easeInOut(duration: 0.5)) {
                            proxy.scrollTo(messages.last?.id, anchor: .bottom)
                        }
                    }
                }
                
                // Input Interface
                AppleIntelligenceInputView(
                    text: $inputText,
                    isRecording: $isRecording,
                    suggestedResponses: suggestedResponses,
                    onSend: sendMessage,
                    onVoiceInput: toggleVoiceInput,
                    onWritingTools: { showingWritingTools = true }
                )
            }
            .navigationTitle("AI Assistant")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Writing Tools", systemImage: "pencil.circle") {
                            showingWritingTools = true
                        }
                        
                        Button("Voice Settings", systemImage: "speaker.wave.2") {
                            // Voice settings
                        }
                        
                        Button("Clear Chat", systemImage: "trash", role: .destructive) {
                            clearChat()
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .font(.title3)
                    }
                }
            }
            .sheet(isPresented: $showingWritingTools) {
                WritingToolsView(text: $inputText)
            }
            .onAppear {
                setupInitialState()
            }
            .sensoryFeedback(.impact(flexibility: .soft), trigger: messages.count)
        }
    }
    
    // MARK: - Actions
    
    private func sendMessage() {
        guard !inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let userMessage = IntelligentMessage(
            content: inputText,
            type: .text,
            sender: .user,
            timestamp: Date()
        )
        
        withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
            messages.append(userMessage)
        }
        
        let messageToSend = inputText
        inputText = ""
        
        // Haptic feedback
        hapticManager.playMessageSent()
        
        // Generate AI response
        Task {
            await generateAIResponse(for: messageToSend)
        }
    }
    
    private func generateAIResponse(for message: String) async {
        // Show typing indicator
        let typingMessage = IntelligentMessage(
            content: "",
            type: .typing,
            sender: .assistant,
            timestamp: Date()
        )
        
        await MainActor.run {
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                messages.append(typingMessage)
            }
        }
        
        do {
            // Create conversation context
            let context = ConversationContext(
                recentMessages: Array(messages.suffix(5)),
                userPreferences: UserPreferences(),
                currentSession: UUID()
            )
            
            // Generate response using Apple Intelligence
            let response = try await aiManager.generateFinancialResponse(
                message: message,
                context: context
            )
            
            await MainActor.run {
                // Remove typing indicator
                if let typingIndex = messages.firstIndex(where: { $0.type == .typing }) {
                    messages.remove(at: typingIndex)
                }
                
                // Add AI response
                let aiMessage = IntelligentMessage(
                    content: response.text,
                    type: .text,
                    sender: .assistant,
                    timestamp: Date(),
                    confidence: response.confidence,
                    sources: response.sources,
                    actionItems: response.actionItems,
                    followUpSuggestions: response.followUpSuggestions
                )
                
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    messages.append(aiMessage)
                }
                
                // Update suggestions
                suggestedResponses = response.followUpSuggestions
                updateSmartSuggestions(based: response)
                
                // Haptic feedback
                hapticManager.playMessageReceived()
                
                // Speak response if voice mode is enabled
                if speechManager.isVoiceModeEnabled {
                    speechManager.speak(response.text)
                }
            }
            
        } catch {
            await MainActor.run {
                // Remove typing indicator
                if let typingIndex = messages.firstIndex(where: { $0.type == .typing }) {
                    messages.remove(at: typingIndex)
                }
                
                // Show error message
                let errorMessage = IntelligentMessage(
                    content: "I'm having trouble processing your request right now. Please try again.",
                    type: .error,
                    sender: .assistant,
                    timestamp: Date()
                )
                
                withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                    messages.append(errorMessage)
                }
                
                hapticManager.playError()
            }
        }
    }
    
    private func toggleVoiceInput() {
        if isRecording {
            speechManager.stopRecording()
        } else {
            speechManager.startRecording { transcript in
                inputText = transcript
            }
        }
        isRecording.toggle()
        hapticManager.playVoiceToggle()
    }
    
    private func handleSmartSuggestion(_ suggestion: SmartSuggestion) {
        switch suggestion.type {
        case .quickReply:
            inputText = suggestion.text
            sendMessage()
        case .action:
            performSuggestionAction(suggestion)
        case .followUp:
            inputText = suggestion.text
        }
        
        hapticManager.playSelection()
    }
    
    private func handleMessageAction(_ action: MessageAction, for message: IntelligentMessage) {
        switch action {
        case .copy:
            UIPasteboard.general.string = message.content
            hapticManager.playSuccess()
        case .share:
            // Implement sharing
            break
        case .regenerate:
            // Regenerate AI response
            Task {
                await regenerateResponse(for: message)
            }
        case .feedback(let rating):
            // Submit feedback
            submitFeedback(rating, for: message)
        }
    }
    
    private func setupInitialState() {
        // Load conversation history
        loadConversationHistory()
        
        // Setup initial smart suggestions
        smartSuggestions = [
            SmartSuggestion(
                type: .quickReply,
                text: "How's my portfolio performing?",
                icon: "chart.pie.fill"
            ),
            SmartSuggestion(
                type: .quickReply,
                text: "What should I invest in?",
                icon: "lightbulb.fill"
            ),
            SmartSuggestion(
                type: .quickReply,
                text: "Analyze market trends",
                icon: "chart.line.uptrend.xyaxis"
            )
        ]
    }
    
    private func updateSmartSuggestions(based response: FinancialResponse) {
        // Generate contextual suggestions based on AI response
        var newSuggestions: [SmartSuggestion] = []
        
        // Add follow-up questions
        for suggestion in response.followUpSuggestions.prefix(2) {
            newSuggestions.append(SmartSuggestion(
                type: .followUp,
                text: suggestion,
                icon: "questionmark.circle.fill"
            ))
        }
        
        // Add action items as suggestions
        for actionItem in response.actionItems.prefix(1) {
            newSuggestions.append(SmartSuggestion(
                type: .action,
                text: actionItem.title,
                icon: "checkmark.circle.fill",
                actionItem: actionItem
            ))
        }
        
        smartSuggestions = newSuggestions
    }
    
    private func clearChat() {
        withAnimation(.easeInOut(duration: 0.5)) {
            messages.removeAll()
            suggestedResponses.removeAll()
            smartSuggestions.removeAll()
        }
        hapticManager.playSuccess()
    }
    
    // MARK: - Helper Methods
    
    private func loadConversationHistory() {
        // Load from persistent storage
        // For demo, add a welcome message
        let welcomeMessage = IntelligentMessage(
            content: "Hello! I'm your AI financial assistant powered by Apple Intelligence. I can help you with portfolio analysis, investment advice, and financial planning. How can I assist you today?",
            type: .text,
            sender: .assistant,
            timestamp: Date()
        )
        
        messages = [welcomeMessage]
    }
    
    private func performSuggestionAction(_ suggestion: SmartSuggestion) {
        guard let actionItem = suggestion.actionItem else { return }
        
        // Perform the suggested action
        switch actionItem.type {
        case .viewPortfolio:
            // Navigate to portfolio
            break
        case .createGoal:
            // Navigate to goal creation
            break
        case .learnMore:
            // Open educational content
            break
        }
    }
    
    private func regenerateResponse(for message: IntelligentMessage) async {
        // Find the user message that prompted this response
        guard let messageIndex = messages.firstIndex(of: message),
              messageIndex > 0 else { return }
        
        let userMessage = messages[messageIndex - 1]
        
        // Remove the current AI response
        await MainActor.run {
            messages.remove(at: messageIndex)
        }
        
        // Generate new response
        await generateAIResponse(for: userMessage.content)
    }
    
    private func submitFeedback(_ rating: FeedbackRating, for message: IntelligentMessage) {
        // Submit feedback to improve AI responses
        Task {
            await FeedbackManager.shared.submitFeedback(
                messageId: message.id,
                rating: rating,
                content: message.content
            )
        }
        hapticManager.playSuccess()
    }
}

// MARK: - Supporting Views

struct SmartSuggestionsBar: View {
    let suggestions: [SmartSuggestion]
    let onSuggestionTapped: (SmartSuggestion) -> Void
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(suggestions) { suggestion in
                    SmartSuggestionChip(suggestion: suggestion) {
                        onSuggestionTapped(suggestion)
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
}

struct SmartSuggestionChip: View {
    let suggestion: SmartSuggestion
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 6) {
                Image(systemName: suggestion.icon)
                    .font(.caption)
                
                Text(suggestion.text)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemBackground))
            .foregroundColor(.primary)
            .cornerRadius(16)
            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Supporting Types

struct IntelligentMessage: Identifiable, Equatable {
    let id = UUID()
    let content: String
    let type: MessageType
    let sender: MessageSender
    let timestamp: Date
    let confidence: Double?
    let sources: [String]
    let actionItems: [ActionItem]
    let followUpSuggestions: [String]
    
    init(
        content: String,
        type: MessageType,
        sender: MessageSender,
        timestamp: Date,
        confidence: Double? = nil,
        sources: [String] = [],
        actionItems: [ActionItem] = [],
        followUpSuggestions: [String] = []
    ) {
        self.content = content
        self.type = type
        self.sender = sender
        self.timestamp = timestamp
        self.confidence = confidence
        self.sources = sources
        self.actionItems = actionItems
        self.followUpSuggestions = followUpSuggestions
    }
    
    static func == (lhs: IntelligentMessage, rhs: IntelligentMessage) -> Bool {
        lhs.id == rhs.id
    }
}

enum MessageType {
    case text
    case typing
    case error
    case system
}

enum MessageSender {
    case user
    case assistant
    case system
}

struct SmartSuggestion: Identifiable {
    let id = UUID()
    let type: SuggestionType
    let text: String
    let icon: String
    let actionItem: ActionItem?
    
    init(type: SuggestionType, text: String, icon: String, actionItem: ActionItem? = nil) {
        self.type = type
        self.text = text
        self.icon = icon
        self.actionItem = actionItem
    }
}

enum SuggestionType {
    case quickReply
    case action
    case followUp
}

enum MessageAction {
    case copy
    case share
    case regenerate
    case feedback(FeedbackRating)
}

enum FeedbackRating {
    case helpful
    case notHelpful
    case inaccurate
}

struct ConversationContext {
    let recentMessages: [IntelligentMessage]
    let userPreferences: UserPreferences
    let currentSession: UUID
}

// MARK: - Managers

@MainActor
class SpeechManager: ObservableObject {
    @Published var isVoiceModeEnabled = false
    @Published var isRecording = false
    
    private let speechRecognizer = SFSpeechRecognizer()
    private let audioEngine = AVAudioEngine()
    private let speechSynthesizer = AVSpeechSynthesizer()
    
    func startRecording(completion: @escaping (String) -> Void) {
        // Implement speech recognition
        isRecording = true
    }
    
    func stopRecording() {
        // Stop speech recognition
        isRecording = false
    }
    
    func speak(_ text: String) {
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = AVSpeechSynthesisVoice(language: "en-US")
        utterance.rate = 0.5
        speechSynthesizer.speak(utterance)
    }
}

@MainActor
class HapticManager: ObservableObject {
    func playMessageSent() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    func playMessageReceived() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    func playVoiceToggle() {
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
    }
    
    func playSelection() {
        let selectionFeedback = UISelectionFeedbackGenerator()
        selectionFeedback.selectionChanged()
    }
    
    func playSuccess() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.success)
    }
    
    func playError() {
        let notificationFeedback = UINotificationFeedbackGenerator()
        notificationFeedback.notificationOccurred(.error)
    }
}

class FeedbackManager {
    static let shared = FeedbackManager()

    func submitFeedback(messageId: UUID, rating: FeedbackRating, content: String) async {
        // Submit feedback for AI improvement
    }
}

// MARK: - Message View Component

struct IntelligentMessageView: View {
    let message: IntelligentMessage
    let onAction: (MessageAction) -> Void

    @State private var showingActions = false
    @Environment(\.dynamicTypeSize) var dynamicTypeSize

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            if message.sender == .user {
                Spacer()
            }

            VStack(alignment: message.sender == .user ? .trailing : .leading, spacing: 8) {
                // Message Content
                messageContent

                // Message Metadata
                if message.sender == .assistant {
                    messageMetadata
                }

                // Action Items
                if !message.actionItems.isEmpty {
                    actionItemsView
                }
            }
            .contextMenu {
                contextMenuItems
            }

            if message.sender == .assistant {
                Spacer()
            }
        }
        .padding(.horizontal)
    }

    @ViewBuilder
    private var messageContent: some View {
        Group {
            switch message.type {
            case .text:
                textMessageView
            case .typing:
                typingIndicatorView
            case .error:
                errorMessageView
            case .system:
                systemMessageView
            }
        }
    }

    private var textMessageView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(message.content)
                .font(.body)
                .foregroundColor(message.sender == .user ? .white : .primary)
                .multilineTextAlignment(.leading)

            // Confidence indicator for AI messages
            if message.sender == .assistant, let confidence = message.confidence {
                ConfidenceIndicator(confidence: confidence)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(messageBackground)
        .cornerRadius(20)
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(message.sender == .assistant ? Color(.systemGray4) : Color.clear, lineWidth: 1)
        )
    }

    private var typingIndicatorView: some View {
        HStack(spacing: 4) {
            ForEach(0..<3) { index in
                Circle()
                    .fill(Color(.systemGray3))
                    .frame(width: 8, height: 8)
                    .scaleEffect(typingScale)
                    .animation(
                        .easeInOut(duration: 0.6)
                        .repeatForever()
                        .delay(Double(index) * 0.2),
                        value: typingScale
                    )
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemGray6))
        .cornerRadius(20)
        .onAppear {
            typingScale = 1.2
        }
    }

    @State private var typingScale: CGFloat = 1.0

    private var errorMessageView: some View {
        HStack(spacing: 8) {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.orange)

            Text(message.content)
                .font(.body)
                .foregroundColor(.primary)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color.orange.opacity(0.1))
        .cornerRadius(20)
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
        )
    }

    private var systemMessageView: some View {
        Text(message.content)
            .font(.caption)
            .foregroundColor(.secondary)
            .multilineTextAlignment(.center)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(16)
    }

    private var messageBackground: some View {
        Group {
            if message.sender == .user {
                LinearGradient(
                    colors: [.purple, .blue],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            } else {
                Color(.systemGray6)
            }
        }
    }

    private var messageMetadata: some View {
        HStack(spacing: 12) {
            // Timestamp
            Text(message.timestamp, style: .time)
                .font(.caption2)
                .foregroundColor(.secondary)

            // Sources indicator
            if !message.sources.isEmpty {
                Button(action: {
                    // Show sources
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "link")
                        Text("\(message.sources.count)")
                    }
                    .font(.caption2)
                    .foregroundColor(.blue)
                }
            }

            Spacer()

            // Action buttons
            HStack(spacing: 8) {
                Button(action: { onAction(.copy) }) {
                    Image(systemName: "doc.on.doc")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                if message.sender == .assistant {
                    Button(action: { onAction(.regenerate) }) {
                        Image(systemName: "arrow.clockwise")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Menu {
                    Button("Helpful", systemImage: "hand.thumbsup") {
                        onAction(.feedback(.helpful))
                    }
                    Button("Not Helpful", systemImage: "hand.thumbsdown") {
                        onAction(.feedback(.notHelpful))
                    }
                    Button("Inaccurate", systemImage: "exclamationmark.triangle") {
                        onAction(.feedback(.inaccurate))
                    }
                } label: {
                    Image(systemName: "ellipsis")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }

    private var actionItemsView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Suggested Actions")
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.secondary)

            ForEach(message.actionItems.prefix(3), id: \.id) { actionItem in
                ActionItemChip(actionItem: actionItem) {
                    // Handle action item tap
                }
            }
        }
        .padding(.top, 4)
    }

    @ViewBuilder
    private var contextMenuItems: some View {
        Button("Copy", systemImage: "doc.on.doc") {
            onAction(.copy)
        }

        Button("Share", systemImage: "square.and.arrow.up") {
            onAction(.share)
        }

        if message.sender == .assistant {
            Button("Regenerate", systemImage: "arrow.clockwise") {
                onAction(.regenerate)
            }
        }

        Divider()

        Button("Helpful", systemImage: "hand.thumbsup") {
            onAction(.feedback(.helpful))
        }

        Button("Not Helpful", systemImage: "hand.thumbsdown") {
            onAction(.feedback(.notHelpful))
        }
    }
}

// MARK: - Input View Component

struct AppleIntelligenceInputView: View {
    @Binding var text: String
    @Binding var isRecording: Bool
    let suggestedResponses: [String]
    let onSend: () -> Void
    let onVoiceInput: () -> Void
    let onWritingTools: () -> Void

    @FocusState private var isTextFieldFocused: Bool
    @State private var showingSuggestions = false

    var body: some View {
        VStack(spacing: 0) {
            // Suggested Responses
            if !suggestedResponses.isEmpty && showingSuggestions {
                suggestedResponsesView
            }

            // Input Bar
            HStack(spacing: 12) {
                // Text Input
                HStack(spacing: 8) {
                    TextField("Ask me anything about finance...", text: $text, axis: .vertical)
                        .textFieldStyle(.plain)
                        .focused($isTextFieldFocused)
                        .lineLimit(1...4)
                        .onSubmit {
                            if !text.isEmpty {
                                onSend()
                            }
                        }

                    // Writing Tools Button
                    Button(action: onWritingTools) {
                        Image(systemName: "pencil.circle")
                            .font(.title3)
                            .foregroundColor(.blue)
                    }
                    .opacity(text.isEmpty ? 0.5 : 1.0)
                    .disabled(text.isEmpty)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemGray6))
                .cornerRadius(24)

                // Voice/Send Button
                Button(action: text.isEmpty ? onVoiceInput : onSend) {
                    Image(systemName: text.isEmpty ? (isRecording ? "stop.circle.fill" : "mic.circle.fill") : "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                        .background(
                            Circle()
                                .fill(text.isEmpty ? (isRecording ? .red : .blue) : .purple)
                                .frame(width: 36, height: 36)
                        )
                }
                .scaleEffect(isRecording ? 1.1 : 1.0)
                .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isRecording)
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
            .background(Color(.systemBackground))
        }
        .onChange(of: isTextFieldFocused) { focused in
            withAnimation(.easeInOut(duration: 0.3)) {
                showingSuggestions = focused && !suggestedResponses.isEmpty
            }
        }
    }

    private var suggestedResponsesView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(suggestedResponses.prefix(5), id: \.self) { suggestion in
                    Button(suggestion) {
                        text = suggestion
                        onSend()
                    }
                    .font(.caption)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color(.systemGray5))
                    .foregroundColor(.primary)
                    .cornerRadius(12)
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .transition(.move(edge: .bottom).combined(with: .opacity))
    }
}

// MARK: - Supporting Components

struct ConfidenceIndicator: View {
    let confidence: Double

    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: "checkmark.seal.fill")
                .font(.caption2)
                .foregroundColor(confidenceColor)

            Text("Confidence: \(Int(confidence * 100))%")
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }

    private var confidenceColor: Color {
        if confidence > 0.8 {
            return .green
        } else if confidence > 0.6 {
            return .orange
        } else {
            return .red
        }
    }
}

struct ActionItemChip: View {
    let actionItem: ActionItem
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 6) {
                Image(systemName: actionItem.icon)
                    .font(.caption)

                Text(actionItem.title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(Color.blue.opacity(0.1))
            .foregroundColor(.blue)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct WritingToolsView: View {
    @Binding var text: String
    @Environment(\.dismiss) var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Writing Tools")
                    .font(.title2)
                    .fontWeight(.bold)

                VStack(spacing: 12) {
                    WritingToolButton(title: "Proofread", icon: "checkmark.circle") {
                        // Implement proofreading
                    }

                    WritingToolButton(title: "Rewrite", icon: "arrow.clockwise") {
                        // Implement rewriting
                    }

                    WritingToolButton(title: "Make Friendly", icon: "heart") {
                        // Implement tone adjustment
                    }

                    WritingToolButton(title: "Make Professional", icon: "briefcase") {
                        // Implement tone adjustment
                    }

                    WritingToolButton(title: "Summarize", icon: "doc.text") {
                        // Implement summarization
                    }
                }

                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct WritingToolButton: View {
    let title: String
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.blue)

                Text(title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}
