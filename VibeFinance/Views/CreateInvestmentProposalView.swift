//
//  CreateInvestmentProposalView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct CreateInvestmentProposalView: View {
    let squad: Squad
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var squadManager: SquadManager
    
    @State private var proposalTitle = ""
    @State private var proposalDescription = ""
    @State private var selectedSymbol = ""
    @State private var investmentAmount = 100.0
    @State private var searchText = ""
    @State private var searchResults: [StockSearchResult] = []
    @State private var isSearching = false
    @State private var showingStockSearch = false
    
    private let stockService = StockServiceManager.shared
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 8) {
                        Text("💡")
                            .font(.system(size: 50))
                        Text("Create Investment Proposal")
                            .font(.title2)
                            .fontWeight(.bold)
                        Text("Propose an investment for your squad to vote on")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 20)
                    
                    // Proposal Form
                    VStack(alignment: .leading, spacing: 20) {
                        // Title
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Proposal Title")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            TextField("Enter a catchy title", text: $proposalTitle)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }
                        
                        // Stock Selection
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Investment Target")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            if selectedSymbol.isEmpty {
                                Button(action: {
                                    showingStockSearch = true
                                }) {
                                    HStack {
                                        Image(systemName: "magnifyingglass")
                                        Text("Search for stocks, ETFs, or crypto")
                                        Spacer()
                                        Image(systemName: "chevron.right")
                                    }
                                    .foregroundColor(.secondary)
                                    .padding()
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                    )
                                }
                                .buttonStyle(PlainButtonStyle())
                            } else {
                                SelectedStockCard(symbol: selectedSymbol) {
                                    selectedSymbol = ""
                                }
                            }
                        }
                        
                        // Investment Amount
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Investment Amount")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            VStack(alignment: .leading, spacing: 12) {
                                HStack {
                                    Text("$\(Int(investmentAmount))")
                                        .font(.title3)
                                        .fontWeight(.semibold)
                                    Spacer()
                                    Text("per member")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                Slider(value: $investmentAmount, in: 1...500, step: 1)
                                    .accentColor(.purple)
                                
                                HStack {
                                    Text("$1")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text("$500")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                // Total calculation
                                HStack {
                                    Text("Total squad investment:")
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)
                                    Spacer()
                                    Text("$\(Int(investmentAmount * Double(squad.memberCount)))")
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.purple)
                                }
                                .padding(.top, 8)
                            }
                        }
                        
                        // Description
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Investment Rationale")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            TextEditor(text: $proposalDescription)
                                .frame(minHeight: 120)
                                .padding(8)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                )
                                .overlay(
                                    Text(proposalDescription.isEmpty ? "Explain why this is a good investment opportunity..." : "")
                                        .foregroundColor(.gray)
                                        .allowsHitTesting(false)
                                        .padding(.leading, 12)
                                        .padding(.top, 16),
                                    alignment: .topLeading
                                )
                        }
                        
                        // AI Analysis
                        if !selectedSymbol.isEmpty {
                            AIAnalysisCard(symbol: selectedSymbol)
                        }
                    }
                    .padding(.horizontal)
                }
            }
            .navigationTitle("New Proposal")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden()
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") {
                        createProposal()
                    }
                    .disabled(!canCreateProposal)
                }
            }
            .sheet(isPresented: $showingStockSearch) {
                StockSearchView(selectedSymbol: $selectedSymbol)
            }
        }
    }
    
    private var canCreateProposal: Bool {
        !proposalTitle.isEmpty &&
        !selectedSymbol.isEmpty &&
        !proposalDescription.isEmpty
    }
    
    private func createProposal() {
        let proposal = InvestmentProposal(
            squadID: squad.id,
            creatorID: UUID(), // Get from user manager
            title: proposalTitle,
            description: proposalDescription,
            symbol: selectedSymbol,
            amount: investmentAmount
        )
        
        Task {
            await squadManager.createInvestmentProposal(proposal)
            await MainActor.run {
                dismiss()
            }
        }
    }
}

// MARK: - Selected Stock Card
struct SelectedStockCard: View {
    let symbol: String
    let onRemove: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // Stock Icon
            Circle()
                .fill(Color.purple.opacity(0.1))
                .frame(width: 40, height: 40)
                .overlay(
                    Text(symbol.prefix(2))
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                )
            
            VStack(alignment: .leading, spacing: 2) {
                Text(symbol)
                    .font(.headline)
                    .fontWeight(.semibold)
                Text(getCompanyName(for: symbol))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Button(action: onRemove) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.gray)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
    
    private func getCompanyName(for symbol: String) -> String {
        switch symbol {
        case "AAPL": return "Apple Inc."
        case "TSLA": return "Tesla Inc."
        case "NVDA": return "NVIDIA Corp."
        case "GOOGL": return "Alphabet Inc."
        case "MSFT": return "Microsoft Corp."
        default: return "Company Name"
        }
    }
}

// MARK: - AI Analysis Card
struct AIAnalysisCard: View {
    let symbol: String
    @State private var analysis: String = ""
    @State private var isLoading = true
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.purple)
                Text("AI Analysis")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            if isLoading {
                HStack {
                    ProgressView()
                        .scaleEffect(0.8)
                    Text("Analyzing \(symbol)...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            } else {
                Text(analysis)
                    .font(.body)
                    .lineSpacing(4)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.purple.opacity(0.1))
        )
        .onAppear {
            generateAnalysis()
        }
    }
    
    private func generateAnalysis() {
        // Simulate AI analysis
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            analysis = """
            \(symbol) shows strong fundamentals with consistent revenue growth. The stock has outperformed the market by 15% this year. 
            
            Key strengths:
            • Strong market position
            • Growing revenue streams
            • Solid balance sheet
            
            Recommendation: BUY with moderate risk level.
            """
            isLoading = false
        }
    }
}

// MARK: - Stock Search View
struct StockSearchView: View {
    @Binding var selectedSymbol: String
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var searchResults: [StockSearchResult] = []
    
    private let popularStocks = [
        StockSearchResult(symbol: "AAPL", name: "Apple Inc.", type: "Stock"),
        StockSearchResult(symbol: "TSLA", name: "Tesla Inc.", type: "Stock"),
        StockSearchResult(symbol: "NVDA", name: "NVIDIA Corp.", type: "Stock"),
        StockSearchResult(symbol: "SPY", name: "SPDR S&P 500 ETF", type: "ETF"),
        StockSearchResult(symbol: "QQQ", name: "Invesco QQQ Trust", type: "ETF"),
        StockSearchResult(symbol: "BTC", name: "Bitcoin", type: "Crypto")
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search Bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                    
                    TextField("Search stocks, ETFs, crypto...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                }
                .padding()
                .background(Color(.systemGray6))
                
                // Results
                List {
                    if searchText.isEmpty {
                        Section("Popular Investments") {
                            ForEach(popularStocks, id: \.symbol) { stock in
                                StockSearchRow(stock: stock) {
                                    selectedSymbol = stock.symbol
                                    dismiss()
                                }
                            }
                        }
                    } else {
                        Section("Search Results") {
                            ForEach(filteredResults, id: \.symbol) { stock in
                                StockSearchRow(stock: stock) {
                                    selectedSymbol = stock.symbol
                                    dismiss()
                                }
                            }
                        }
                    }
                }
                .listStyle(PlainListStyle())
            }
            .navigationTitle("Select Investment")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var filteredResults: [StockSearchResult] {
        if searchText.isEmpty {
            return popularStocks
        } else {
            return popularStocks.filter {
                $0.symbol.localizedCaseInsensitiveContains(searchText) ||
                $0.name.localizedCaseInsensitiveContains(searchText)
            }
        }
    }
}

// MARK: - Stock Search Row
struct StockSearchRow: View {
    let stock: StockSearchResult
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 12) {
                Circle()
                    .fill(typeColor.opacity(0.2))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Text(stock.symbol.prefix(2))
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(typeColor)
                    )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(stock.symbol)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                    
                    Text(stock.name)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Text(stock.type)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(typeColor)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(typeColor.opacity(0.1))
                    )
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var typeColor: Color {
        switch stock.type {
        case "Stock": return .blue
        case "ETF": return .green
        case "Crypto": return .orange
        default: return .gray
        }
    }
}

// MARK: - Supporting Types
struct StockSearchResult {
    let symbol: String
    let name: String
    let type: String
}

#Preview {
    CreateInvestmentProposalView(
        squad: Squad(
            name: "Tech Titans",
            description: "Technology investments",
            emoji: "💻",
            creatorID: UUID(),
            isPublic: true,
            maxMembers: 50
        )
    )
    .environmentObject(SquadManager())
}
