//
//  BuffettChatView.swift
//  VibeFinance - <PERSON> Inspired AI Adviser
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

// MARK: - Conversation Context

enum ConversationContext: String, CaseIterable {
    case general = "General"
    case investing = "Investing"
    case portfolio = "Portfolio"
    case market = "Market Analysis"
    case risk = "Risk Management"

    var icon: String {
        switch self {
        case .general: return "brain.head.profile"
        case .investing: return "chart.line.uptrend.xyaxis"
        case .portfolio: return "chart.pie"
        case .market: return "chart.bar"
        case .risk: return "shield.checkered"
        }
    }

    var color: Color {
        switch self {
        case .general: return VibeFinanceDesignSystem.Colors.accentGold
        case .investing: return .green
        case .portfolio: return .blue
        case .market: return .purple
        case .risk: return .orange
        }
    }
}

struct BuffettChatView: View {
    @State private var messages: [BuffettChatMessage] = []
    @State private var newMessage = ""
    @State private var isTyping = false
    @State private var showingQuickActions = true
    @State private var conversationContext: ConversationContext = .general



    var body: some View {
        NavigationView {
            ZStack {
                // Unified Warren Buffett inspired gradient background
                VibeFinanceDesignSystem.Colors.primaryGradient
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // Compact AI Adviser Header (60% smaller)
                    CompactBuffettAdviserHeader(
                        context: conversationContext,
                        isTyping: isTyping
                    )
                    .padding(.horizontal, 16)
                    .padding(.top, 4)

                    // Contextual Quick Actions (collapsible)
                    if showingQuickActions {
                        ContextualQuickActionsView(
                            context: conversationContext
                        ) { action in
                            sendQuickMessage(action.message)
                            updateConversationContext(for: action.message)
                        }
                        .padding(.horizontal, 16)
                        .padding(.top, 8)
                        .transition(.move(edge: .top).combined(with: .opacity))
                    }

                    // Enhanced Messages List with Context
                    ScrollViewReader { proxy in
                        ScrollView {
                            LazyVStack(spacing: 8) {
                                // Conversation starter if no messages
                                if messages.isEmpty {
                                    ConversationStarterView(
                                        onStarterTap: { starter in
                                            sendQuickMessage(starter)
                                        }
                                    )
                                    .padding(.top, 20)
                                }

                                ForEach(messages) { message in
                                    EnhancedChatMessageView(
                                        message: message,
                                        showContext: true
                                    )
                                }

                                if isTyping {
                                    EnhancedTypingIndicatorView()
                                }
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                        }
                        .onChange(of: messages.count) { _, _ in
                            withAnimation(.easeInOut(duration: 0.3)) {
                                proxy.scrollTo(messages.last?.id, anchor: .bottom)
                            }
                        }
                        .onTapGesture {
                            // Hide quick actions when scrolling
                            if showingQuickActions {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    showingQuickActions = false
                                }
                            }
                        }
                    }

                    // Enhanced Message Input with Smart Suggestions
                    EnhancedMessageInputView(
                        messageText: $newMessage,
                        context: conversationContext,
                        onSend: sendMessage,
                        onQuickActionsToggle: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                showingQuickActions.toggle()
                            }
                        }
                    )
                }
            }
            .navigationTitle("AI Adviser")
            .navigationBarTitleDisplayMode(.inline)
            .preferredColorScheme(.dark)
            .onAppear {
                loadMockChatData()
            }
        }
    }
    
    private func loadMockChatData() {
        messages = BuffettChatMessage.mockMessages
    }
    
    private func sendMessage() {
        guard !newMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let userMessage = BuffettChatMessage(
            content: newMessage,
            isFromUser: true,
            timestamp: Date(),
            buffettWisdom: nil
        )
        
        messages.append(userMessage)
        let messageToSend = newMessage
        newMessage = ""
        
        generateBuffettResponse(to: messageToSend)
    }
    
    private func sendQuickMessage(_ message: String) {
        let userMessage = BuffettChatMessage(
            content: message,
            isFromUser: true,
            timestamp: Date(),
            buffettWisdom: nil
        )

        messages.append(userMessage)
        updateConversationContext(for: message)
        generateBuffettResponse(to: message)
    }

    private func updateConversationContext(for message: String) {
        let lowercased = message.lowercased()

        if lowercased.contains("invest") || lowercased.contains("stock") || lowercased.contains("buy") {
            conversationContext = .investing
        } else if lowercased.contains("portfolio") || lowercased.contains("allocation") {
            conversationContext = .portfolio
        } else if lowercased.contains("market") || lowercased.contains("analysis") {
            conversationContext = .market
        } else if lowercased.contains("risk") || lowercased.contains("safety") {
            conversationContext = .risk
        } else {
            conversationContext = .general
        }
    }
    
    private func generateBuffettResponse(to message: String) {
        isTyping = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            isTyping = false
            
            let response = generateWarrenResponse(for: message)
            let aiMessage = BuffettChatMessage(
                content: response.content,
                isFromUser: false,
                timestamp: Date(),
                buffettWisdom: response.wisdom
            )
            
            messages.append(aiMessage)
        }
    }
    
    private func generateWarrenResponse(for message: String) -> (content: String, wisdom: String?) {
        let lowercased = message.lowercased()
        
        if lowercased.contains("invest") || lowercased.contains("stock") {
            return (
                "When investing, remember my golden rule: Rule No. 1 - Never lose money. Rule No. 2 - Never forget rule No. 1. Focus on companies with strong fundamentals, competitive advantages, and excellent management. Look for businesses you understand and can hold for decades.",
                "Price is what you pay. Value is what you get."
            )
        } else if lowercased.contains("market") || lowercased.contains("analysis") {
            return (
                "The stock market is a voting machine in the short run, but a weighing machine in the long run. Don't try to time the market - time in the market beats timing the market. Focus on buying wonderful companies at fair prices and hold them for the long term.",
                "The stock market is designed to transfer money from the Active to the Patient."
            )
        } else if lowercased.contains("portfolio") {
            return (
                "A good portfolio should be concentrated in your best ideas, not diversified across mediocre ones. I'd rather own pieces of wonderful businesses than a little bit of everything. Focus on quality over quantity.",
                "Wide diversification is only required when investors do not understand what they are doing."
            )
        } else if lowercased.contains("risk") {
            return (
                "Risk comes from not knowing what you're doing. The biggest risk is not taking any risk at all. But be smart about it - invest in businesses you understand, with strong competitive moats and excellent management.",
                "Risk comes from not knowing what you're doing."
            )
        } else {
            return (
                "Remember, investing is not about beating others at their game. It's about controlling yourself at your own game. Stay patient, stay disciplined, and focus on the long term. The best investment you can make is in yourself - keep learning!",
                "Someone's sitting in the shade today because someone planted a tree a long time ago."
            )
        }
    }
}

// MARK: - Compact Buffett Adviser Header (60% smaller)
struct CompactBuffettAdviserHeader: View {
    let context: ConversationContext
    let isTyping: Bool

    var body: some View {
        HStack(spacing: 8) {
            // Compact Warren's Avatar
            ZStack {
                Circle()
                    .fill(context.color.opacity(0.3))
                    .frame(width: 32, height: 32)

                Image(systemName: context.icon)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(context.color)
            }

            VStack(alignment: .leading, spacing: 1) {
                Text("Warren AI")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                HStack(spacing: 4) {
                    if isTyping {
                        HStack(spacing: 2) {
                            ForEach(0..<3) { index in
                                Circle()
                                    .fill(VibeFinanceDesignSystem.Colors.accentGold)
                                    .frame(width: 3, height: 3)
                                    .scaleEffect(isTyping ? 1.0 : 0.5)
                                    .animation(
                                        .easeInOut(duration: 0.6)
                                        .repeatForever(autoreverses: true)
                                        .delay(Double(index) * 0.2),
                                        value: isTyping
                                    )
                            }
                        }

                        Text("Thinking...")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                    } else {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 6, height: 6)

                        Text(context.rawValue)
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
            }

            Spacer()

            // Context indicator
            Text(context.icon)
                .font(.caption)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.1))
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
    }
}

// MARK: - Buffett Quick Actions
struct BuffettQuickActionsView: View {
    let onAction: (BuffettQuickAction) -> Void
    
    private let quickActions = BuffettQuickAction.allActions
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(quickActions) { action in
                    Button(action: { onAction(action) }) {
                        VStack(spacing: 6) {
                            ZStack {
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            colors: [
                                                Color.white.opacity(0.2),
                                                Color.white.opacity(0.1)
                                            ],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .frame(width: 44, height: 44)
                                
                                Text(action.emoji)
                                    .font(.title2)
                            }
                            
                            Text(action.title)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.white.opacity(0.9))
                                .multilineTextAlignment(.center)
                        }
                        .frame(width: 70)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 16)
        }
    }
}

// MARK: - Buffett Chat Message View
struct BuffettChatMessageView: View {
    let message: BuffettChatMessage
    
    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(message.content)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .foregroundColor(.white)
                        .cornerRadius(18)
                        .frame(maxWidth: UIScreen.main.bounds.width * 0.75, alignment: .trailing)
                    
                    Text(formatTime(message.timestamp))
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.6))
                }
            } else {
                VStack(alignment: .leading, spacing: 8) {
                    HStack(alignment: .top, spacing: 8) {
                        // Warren's mini avatar
                        Circle()
                            .fill(Color.yellow)
                            .frame(width: 24, height: 24)
                            .overlay(
                                Text("🧠")
                                    .font(.caption)
                            )
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text(message.content)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 18)
                                        .fill(
                                            LinearGradient(
                                                colors: [
                                                    Color.white.opacity(0.2),
                                                    Color.white.opacity(0.1)
                                                ],
                                                startPoint: .topLeading,
                                                endPoint: .bottomTrailing
                                            )
                                        )
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                                .foregroundColor(.white)
                                .frame(maxWidth: UIScreen.main.bounds.width * 0.75, alignment: .leading)
                            
                            // Warren's wisdom quote
                            if let wisdom = message.buffettWisdom {
                                HStack(spacing: 8) {
                                    Image(systemName: "lightbulb.fill")
                                        .font(.caption)
                                        .foregroundColor(.yellow)
                                    
                                    Text("\"\(wisdom)\" — Warren Buffett")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(.yellow.opacity(0.9))
                                        .italic()
                                }
                                .padding(.horizontal, 12)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.yellow.opacity(0.1))
                                        .stroke(Color.yellow.opacity(0.3), lineWidth: 1)
                                )
                            }
                        }
                        
                        Spacer()
                    }
                    
                    Text(formatTime(message.timestamp))
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.6))
                        .padding(.leading, 32)
                }
            }
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Buffett Typing Indicator
struct BuffettTypingIndicatorView: View {
    @State private var animationOffset: CGFloat = 0

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Circle()
                .fill(Color.yellow)
                .frame(width: 24, height: 24)
                .overlay(
                    Text("🧠")
                        .font(.caption)
                )

            HStack(spacing: 4) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(Color.white.opacity(0.6))
                        .frame(width: 8, height: 8)
                        .scaleEffect(animationOffset == CGFloat(index) ? 1.2 : 0.8)
                        .animation(
                            Animation.easeInOut(duration: 0.6)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                            value: animationOffset
                        )
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 18)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.2),
                                Color.white.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
            )

            Spacer()
        }
        .onAppear {
            animationOffset = 0
            withAnimation {
                animationOffset = 2
            }
        }
    }
}

// MARK: - Buffett Message Input
struct BuffettMessageInputView: View {
    @Binding var messageText: String
    let onSend: () -> Void

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            HStack(spacing: 12) {
                TextField("Ask Warren for investment wisdom...", text: $messageText, axis: .vertical)
                    .textFieldStyle(PlainTextFieldStyle())
                    .foregroundColor(.white)
                    .lineLimit(1...4)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)

                Button(action: onSend) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(messageText.isEmpty ? .white.opacity(0.4) : .yellow)
                }
                .disabled(messageText.isEmpty)
                .padding(.trailing, 8)
            }
        }
        .frame(minHeight: 44)
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
    }
}

// MARK: - Data Models
struct BuffettChatMessage: Identifiable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp: Date
    let buffettWisdom: String?

    static let mockMessages: [BuffettChatMessage] = [
        BuffettChatMessage(
            content: "Hello! I'm Warren's AI Adviser, trained on 60+ years of investment wisdom from the Oracle of Omaha himself. I can help you understand value investing, analyze companies, build portfolios, and develop the patience needed for long-term wealth building. What would you like to learn about investing today?",
            isFromUser: false,
            timestamp: Date().addingTimeInterval(-3600),
            buffettWisdom: "The best investment you can make is in yourself."
        ),
        BuffettChatMessage(
            content: "I'm new to investing. Where should I start?",
            isFromUser: true,
            timestamp: Date().addingTimeInterval(-3500),
            buffettWisdom: nil
        ),
        BuffettChatMessage(
            content: "Excellent question! Start by investing in yourself - read everything you can about businesses and investing. I recommend starting with index funds while you learn. Focus on companies you understand, with strong competitive advantages (what I call 'economic moats'), excellent management, and reasonable prices. Remember, time is your greatest ally in investing.",
            isFromUser: false,
            timestamp: Date().addingTimeInterval(-3400),
            buffettWisdom: "Time is the friend of the wonderful company, the enemy of the mediocre."
        )
    ]
}

struct BuffettQuickAction: Identifiable {
    let id = UUID()
    let emoji: String
    let title: String
    let message: String

    static let allActions: [BuffettQuickAction] = [
        BuffettQuickAction(
            emoji: "📊",
            title: "Market Analysis",
            message: "What's your take on the current market conditions?"
        ),
        BuffettQuickAction(
            emoji: "💎",
            title: "Value Stocks",
            message: "Help me find undervalued companies with strong fundamentals"
        ),
        BuffettQuickAction(
            emoji: "🏰",
            title: "Economic Moats",
            message: "Explain economic moats and how to identify them"
        ),
        BuffettQuickAction(
            emoji: "📚",
            title: "Learning",
            message: "What books and resources do you recommend for learning investing?"
        ),
        BuffettQuickAction(
            emoji: "⏰",
            title: "Long-term",
            message: "How do I develop patience for long-term investing?"
        )
    ]
}

// MARK: - Enhanced Components

struct ContextualQuickActionsView: View {
    let context: ConversationContext
    let onActionTap: (QuickAction) -> Void

    private var contextualActions: [QuickAction] {
        switch context {
        case .general:
            return [
                QuickAction(title: "Start Investing", message: "How do I start investing?", icon: "chart.line.uptrend.xyaxis"),
                QuickAction(title: "Portfolio Tips", message: "How should I build my portfolio?", icon: "chart.pie"),
                QuickAction(title: "Market Analysis", message: "What's happening in the market?", icon: "chart.bar"),
                QuickAction(title: "Risk Management", message: "How do I manage investment risk?", icon: "shield.checkered")
            ]
        case .investing:
            return [
                QuickAction(title: "Value Stocks", message: "How do I find undervalued stocks?", icon: "magnifyingglass"),
                QuickAction(title: "Company Analysis", message: "What makes a good company?", icon: "building.2"),
                QuickAction(title: "When to Buy", message: "When is the best time to buy?", icon: "clock"),
                QuickAction(title: "Long-term Strategy", message: "How long should I hold stocks?", icon: "calendar")
            ]
        case .portfolio:
            return [
                QuickAction(title: "Diversification", message: "How diversified should my portfolio be?", icon: "chart.pie"),
                QuickAction(title: "Rebalancing", message: "When should I rebalance?", icon: "arrow.triangle.2.circlepath"),
                QuickAction(title: "Asset Allocation", message: "How should I allocate my assets?", icon: "percent"),
                QuickAction(title: "Performance Review", message: "How do I review my portfolio performance?", icon: "chart.xyaxis.line")
            ]
        case .market:
            return [
                QuickAction(title: "Market Trends", message: "What are the current market trends?", icon: "chart.line.uptrend.xyaxis"),
                QuickAction(title: "Economic Indicators", message: "Which economic indicators matter?", icon: "chart.bar"),
                QuickAction(title: "Market Timing", message: "Should I try to time the market?", icon: "timer"),
                QuickAction(title: "Bear Markets", message: "How do I handle bear markets?", icon: "chart.line.downtrend.xyaxis")
            ]
        case .risk:
            return [
                QuickAction(title: "Risk Assessment", message: "How do I assess investment risk?", icon: "checkmark.shield"),
                QuickAction(title: "Risk Tolerance", message: "What's my risk tolerance?", icon: "heart.text.square"),
                QuickAction(title: "Risk Mitigation", message: "How do I reduce investment risk?", icon: "shield.lefthalf.filled"),
                QuickAction(title: "Emergency Fund", message: "How much should I keep in emergency funds?", icon: "banknote")
            ]
        }
    }

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(contextualActions, id: \.title) { action in
                    Button(action: { onActionTap(action) }) {
                        HStack(spacing: 6) {
                            Image(systemName: action.icon)
                                .font(.caption)

                            Text(action.title)
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.white.opacity(0.15))
                                .background(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .foregroundColor(.white)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 16)
        }
    }
}

struct QuickAction {
    let title: String
    let message: String
    let icon: String
}

struct ConversationStarterView: View {
    let onStarterTap: (String) -> Void

    private let starters = [
        "How do I start investing with $1000?",
        "What makes Warren Buffett successful?",
        "Explain value investing in simple terms",
        "How do I analyze a company's stock?"
    ]

    var body: some View {
        VStack(spacing: 16) {
            VStack(spacing: 8) {
                Text("🧠")
                    .font(.system(size: 40))

                Text("Ask Warren Anything")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("Get investment wisdom from 60+ years of experience")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }

            VStack(spacing: 12) {
                ForEach(starters, id: \.self) { starter in
                    Button(action: { onStarterTap(starter) }) {
                        HStack {
                            Text(starter)
                                .font(.subheadline)
                                .foregroundColor(.white)
                                .multilineTextAlignment(.leading)

                            Spacer()

                            Image(systemName: "arrow.right.circle")
                                .font(.title3)
                                .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                        }
                        .padding(16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.white.opacity(0.1))
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(20)
    }
}

struct EnhancedChatMessageView: View {
    let message: BuffettChatMessage
    let showContext: Bool

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            if !message.isFromUser {
                // Warren's avatar
                Circle()
                    .fill(VibeFinanceDesignSystem.Colors.accentGold.opacity(0.3))
                    .frame(width: 32, height: 32)
                    .overlay(
                        Text("🧠")
                            .font(.caption)
                    )
            }

            VStack(alignment: message.isFromUser ? .trailing : .leading, spacing: 8) {
                // Message bubble
                Text(message.content)
                    .font(.body)
                    .foregroundColor(message.isFromUser ? .black : .white)
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                message.isFromUser ?
                                VibeFinanceDesignSystem.Colors.accentGold :
                                Color.white.opacity(0.15)
                            )
                    )

                // Warren's wisdom quote
                if let wisdom = message.buffettWisdom {
                    Text("💡 \"\(wisdom)\"")
                        .font(.caption)
                        .italic()
                        .foregroundColor(.white.opacity(0.8))
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.white.opacity(0.1))
                        )
                }

                // Timestamp
                Text(message.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.5))
            }

            if message.isFromUser {
                Spacer()
            }
        }
        .frame(maxWidth: .infinity, alignment: message.isFromUser ? .trailing : .leading)
    }
}

struct EnhancedTypingIndicatorView: View {
    @State private var animationOffset: CGFloat = 0

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // Warren's avatar
            Circle()
                .fill(VibeFinanceDesignSystem.Colors.accentGold.opacity(0.3))
                .frame(width: 32, height: 32)
                .overlay(
                    Text("🧠")
                        .font(.caption)
                )

            HStack(spacing: 4) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(Color.white.opacity(0.6))
                        .frame(width: 8, height: 8)
                        .offset(y: animationOffset)
                        .animation(
                            .easeInOut(duration: 0.6)
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.2),
                            value: animationOffset
                        )
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.15))
            )

            Spacer()
        }
        .onAppear {
            animationOffset = -4
        }
    }
}

struct EnhancedMessageInputView: View {
    @Binding var messageText: String
    let context: ConversationContext
    let onSend: () -> Void
    let onQuickActionsToggle: () -> Void

    @State private var isRecording = false

    var body: some View {
        VStack(spacing: 0) {
            // Input area
            HStack(spacing: 12) {
                // Quick actions toggle
                Button(action: onQuickActionsToggle) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                        .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                }

                // Text input
                HStack(spacing: 8) {
                    TextField("Ask Warren anything...", text: $messageText, axis: .vertical)
                        .font(.body)
                        .foregroundColor(.white)
                        .lineLimit(1...4)

                    if !messageText.isEmpty {
                        Button(action: onSend) {
                            Image(systemName: "arrow.up.circle.fill")
                                .font(.title2)
                                .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                        }
                    } else {
                        Button(action: { isRecording.toggle() }) {
                            Image(systemName: isRecording ? "stop.circle.fill" : "mic.circle.fill")
                                .font(.title2)
                                .foregroundColor(isRecording ? .red : VibeFinanceDesignSystem.Colors.accentGold)
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 24)
                        .fill(Color.white.opacity(0.15))
                        .background(
                            RoundedRectangle(cornerRadius: 24)
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                Rectangle()
                    .fill(VibeFinanceDesignSystem.Colors.primaryBlue.opacity(0.9))
                    .blur(radius: 20)
            )
        }
    }
}

#Preview {
    BuffettChatView()
        .preferredColorScheme(.dark)
}
