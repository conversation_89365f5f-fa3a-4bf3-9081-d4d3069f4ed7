//
//  BuffettChatView.swift
//  VibeFinance - <PERSON> Inspired AI Adviser
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

struct BuffettChatView: View {
    @State private var messages: [BuffettChatMessage] = []
    @State private var newMessage = ""
    @State private var isTyping = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Warren Buffett inspired gradient background
                LinearGradient(
                    colors: [
                        Color(red: 0.1, green: 0.2, blue: 0.4),
                        Color(red: 0.2, green: 0.3, blue: 0.5),
                        Color(red: 0.1, green: 0.25, blue: 0.45)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Warren's AI Adviser Header
                    BuffettAdviserHeader()
                        .padding(.horizontal, 16)
                        .padding(.top, 8)
                    
                    // Quick Actions
                    BuffettQuickActionsView { action in
                        sendQuickMessage(action.message)
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 8)
                    
                    // Messages List
                    ScrollViewReader { proxy in
                        ScrollView {
                            LazyVStack(spacing: 12) {
                                ForEach(messages) { message in
                                    BuffettChatMessageView(message: message)
                                }
                                
                                if isTyping {
                                    BuffettTypingIndicatorView()
                                }
                            }
                            .padding()
                        }
                        .onChange(of: messages.count) { _ in
                            withAnimation {
                                proxy.scrollTo(messages.last?.id, anchor: .bottom)
                            }
                        }
                    }
                    
                    // Message Input
                    BuffettMessageInputView(
                        messageText: $newMessage,
                        onSend: sendMessage
                    )
                }
            }
            .navigationTitle("Warren's AI Adviser")
            .navigationBarTitleDisplayMode(.large)
            .preferredColorScheme(.dark)
            .onAppear {
                loadMockChatData()
            }
        }
    }
    
    private func loadMockChatData() {
        messages = BuffettChatMessage.mockMessages
    }
    
    private func sendMessage() {
        guard !newMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let userMessage = BuffettChatMessage(
            content: newMessage,
            isFromUser: true,
            timestamp: Date(),
            buffettWisdom: nil
        )
        
        messages.append(userMessage)
        let messageToSend = newMessage
        newMessage = ""
        
        generateBuffettResponse(to: messageToSend)
    }
    
    private func sendQuickMessage(_ message: String) {
        let userMessage = BuffettChatMessage(
            content: message,
            isFromUser: true,
            timestamp: Date(),
            buffettWisdom: nil
        )
        
        messages.append(userMessage)
        generateBuffettResponse(to: message)
    }
    
    private func generateBuffettResponse(to message: String) {
        isTyping = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            isTyping = false
            
            let response = generateWarrenResponse(for: message)
            let aiMessage = BuffettChatMessage(
                content: response.content,
                isFromUser: false,
                timestamp: Date(),
                buffettWisdom: response.wisdom
            )
            
            messages.append(aiMessage)
        }
    }
    
    private func generateWarrenResponse(for message: String) -> (content: String, wisdom: String?) {
        let lowercased = message.lowercased()
        
        if lowercased.contains("invest") || lowercased.contains("stock") {
            return (
                "When investing, remember my golden rule: Rule No. 1 - Never lose money. Rule No. 2 - Never forget rule No. 1. Focus on companies with strong fundamentals, competitive advantages, and excellent management. Look for businesses you understand and can hold for decades.",
                "Price is what you pay. Value is what you get."
            )
        } else if lowercased.contains("market") || lowercased.contains("analysis") {
            return (
                "The stock market is a voting machine in the short run, but a weighing machine in the long run. Don't try to time the market - time in the market beats timing the market. Focus on buying wonderful companies at fair prices and hold them for the long term.",
                "The stock market is designed to transfer money from the Active to the Patient."
            )
        } else if lowercased.contains("portfolio") {
            return (
                "A good portfolio should be concentrated in your best ideas, not diversified across mediocre ones. I'd rather own pieces of wonderful businesses than a little bit of everything. Focus on quality over quantity.",
                "Wide diversification is only required when investors do not understand what they are doing."
            )
        } else if lowercased.contains("risk") {
            return (
                "Risk comes from not knowing what you're doing. The biggest risk is not taking any risk at all. But be smart about it - invest in businesses you understand, with strong competitive moats and excellent management.",
                "Risk comes from not knowing what you're doing."
            )
        } else {
            return (
                "Remember, investing is not about beating others at their game. It's about controlling yourself at your own game. Stay patient, stay disciplined, and focus on the long term. The best investment you can make is in yourself - keep learning!",
                "Someone's sitting in the shade today because someone planted a tree a long time ago."
            )
        }
    }
}

// MARK: - Buffett Adviser Header
struct BuffettAdviserHeader: View {
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.yellow.opacity(0.3),
                            Color.orange.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.yellow.opacity(0.5), lineWidth: 1)
                )
                .blur(radius: 0.5)
            
            HStack(spacing: 12) {
                // Warren's Avatar
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [.yellow, .orange],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 50, height: 50)
                    
                    Text("🧠")
                        .font(.title)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Warren's AI Adviser")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("Powered by 60+ years of investment wisdom")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 2) {
                    Circle()
                        .fill(Color.green)
                        .frame(width: 8, height: 8)
                    
                    Text("Online")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            .padding(12)
        }
    }
}

// MARK: - Buffett Quick Actions
struct BuffettQuickActionsView: View {
    let onAction: (BuffettQuickAction) -> Void
    
    private let quickActions = BuffettQuickAction.allActions
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(quickActions) { action in
                    Button(action: { onAction(action) }) {
                        VStack(spacing: 6) {
                            ZStack {
                                Circle()
                                    .fill(
                                        LinearGradient(
                                            colors: [
                                                Color.white.opacity(0.2),
                                                Color.white.opacity(0.1)
                                            ],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .frame(width: 44, height: 44)
                                
                                Text(action.emoji)
                                    .font(.title2)
                            }
                            
                            Text(action.title)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.white.opacity(0.9))
                                .multilineTextAlignment(.center)
                        }
                        .frame(width: 70)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 16)
        }
    }
}

// MARK: - Buffett Chat Message View
struct BuffettChatMessageView: View {
    let message: BuffettChatMessage
    
    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(message.content)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .foregroundColor(.white)
                        .cornerRadius(18)
                        .frame(maxWidth: UIScreen.main.bounds.width * 0.75, alignment: .trailing)
                    
                    Text(formatTime(message.timestamp))
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.6))
                }
            } else {
                VStack(alignment: .leading, spacing: 8) {
                    HStack(alignment: .top, spacing: 8) {
                        // Warren's mini avatar
                        Circle()
                            .fill(Color.yellow)
                            .frame(width: 24, height: 24)
                            .overlay(
                                Text("🧠")
                                    .font(.caption)
                            )
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text(message.content)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 18)
                                        .fill(
                                            LinearGradient(
                                                colors: [
                                                    Color.white.opacity(0.2),
                                                    Color.white.opacity(0.1)
                                                ],
                                                startPoint: .topLeading,
                                                endPoint: .bottomTrailing
                                            )
                                        )
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                                .foregroundColor(.white)
                                .frame(maxWidth: UIScreen.main.bounds.width * 0.75, alignment: .leading)
                            
                            // Warren's wisdom quote
                            if let wisdom = message.buffettWisdom {
                                HStack(spacing: 8) {
                                    Image(systemName: "lightbulb.fill")
                                        .font(.caption)
                                        .foregroundColor(.yellow)
                                    
                                    Text("\"\(wisdom)\" — Warren Buffett")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(.yellow.opacity(0.9))
                                        .italic()
                                }
                                .padding(.horizontal, 12)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.yellow.opacity(0.1))
                                        .stroke(Color.yellow.opacity(0.3), lineWidth: 1)
                                )
                            }
                        }
                        
                        Spacer()
                    }
                    
                    Text(formatTime(message.timestamp))
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.6))
                        .padding(.leading, 32)
                }
            }
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Buffett Typing Indicator
struct BuffettTypingIndicatorView: View {
    @State private var animationOffset: CGFloat = 0

    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            Circle()
                .fill(Color.yellow)
                .frame(width: 24, height: 24)
                .overlay(
                    Text("🧠")
                        .font(.caption)
                )

            HStack(spacing: 4) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(Color.white.opacity(0.6))
                        .frame(width: 8, height: 8)
                        .scaleEffect(animationOffset == CGFloat(index) ? 1.2 : 0.8)
                        .animation(
                            Animation.easeInOut(duration: 0.6)
                                .repeatForever()
                                .delay(Double(index) * 0.2),
                            value: animationOffset
                        )
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 18)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(0.2),
                                Color.white.opacity(0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
            )

            Spacer()
        }
        .onAppear {
            animationOffset = 0
            withAnimation {
                animationOffset = 2
            }
        }
    }
}

// MARK: - Buffett Message Input
struct BuffettMessageInputView: View {
    @Binding var messageText: String
    let onSend: () -> Void

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            HStack(spacing: 12) {
                TextField("Ask Warren for investment wisdom...", text: $messageText, axis: .vertical)
                    .textFieldStyle(PlainTextFieldStyle())
                    .foregroundColor(.white)
                    .lineLimit(1...4)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)

                Button(action: onSend) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(messageText.isEmpty ? .white.opacity(0.4) : .yellow)
                }
                .disabled(messageText.isEmpty)
                .padding(.trailing, 8)
            }
        }
        .frame(minHeight: 44)
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
    }
}

// MARK: - Data Models
struct BuffettChatMessage: Identifiable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp: Date
    let buffettWisdom: String?

    static let mockMessages: [BuffettChatMessage] = [
        BuffettChatMessage(
            content: "Hello! I'm Warren's AI Adviser, trained on 60+ years of investment wisdom from the Oracle of Omaha himself. I can help you understand value investing, analyze companies, build portfolios, and develop the patience needed for long-term wealth building. What would you like to learn about investing today?",
            isFromUser: false,
            timestamp: Date().addingTimeInterval(-3600),
            buffettWisdom: "The best investment you can make is in yourself."
        ),
        BuffettChatMessage(
            content: "I'm new to investing. Where should I start?",
            isFromUser: true,
            timestamp: Date().addingTimeInterval(-3500),
            buffettWisdom: nil
        ),
        BuffettChatMessage(
            content: "Excellent question! Start by investing in yourself - read everything you can about businesses and investing. I recommend starting with index funds while you learn. Focus on companies you understand, with strong competitive advantages (what I call 'economic moats'), excellent management, and reasonable prices. Remember, time is your greatest ally in investing.",
            isFromUser: false,
            timestamp: Date().addingTimeInterval(-3400),
            buffettWisdom: "Time is the friend of the wonderful company, the enemy of the mediocre."
        )
    ]
}

struct BuffettQuickAction: Identifiable {
    let id = UUID()
    let emoji: String
    let title: String
    let message: String

    static let allActions: [BuffettQuickAction] = [
        BuffettQuickAction(
            emoji: "📊",
            title: "Market Analysis",
            message: "What's your take on the current market conditions?"
        ),
        BuffettQuickAction(
            emoji: "💎",
            title: "Value Stocks",
            message: "Help me find undervalued companies with strong fundamentals"
        ),
        BuffettQuickAction(
            emoji: "🏰",
            title: "Economic Moats",
            message: "Explain economic moats and how to identify them"
        ),
        BuffettQuickAction(
            emoji: "📚",
            title: "Learning",
            message: "What books and resources do you recommend for learning investing?"
        ),
        BuffettQuickAction(
            emoji: "⏰",
            title: "Long-term",
            message: "How do I develop patience for long-term investing?"
        )
    ]
}

#Preview {
    BuffettChatView()
        .preferredColorScheme(.dark)
}
