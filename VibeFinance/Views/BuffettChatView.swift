//
//  BuffettChatView.swift
//  VibeFinance - <PERSON> Inspired AI Adviser
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

// MARK: - Financial Agent Models

struct FinancialAgent: Identifiable {
    let id = UUID()
    let name: String
    let title: String
    let expertise: String
    let icon: String
    let color: Color
    let description: String
    let specialties: [String]

    static let allAgents: [FinancialAgent] = [
        FinancialAgent(
            name: "<PERSON>",
            title: "Value Investing Guru",
            expertise: "Long-term Value Investing",
            icon: "chart.pie.fill",
            color: .yellow,
            description: "The Oracle of Omaha specializes in finding undervalued companies with strong fundamentals.",
            specialties: ["Value Investing", "Company Analysis", "Long-term Strategy"]
        ),
        FinancialAgent(
            name: "<PERSON>",
            title: "Risk Management Expert",
            expertise: "Portfolio Diversification",
            icon: "shield.checkered",
            color: .blue,
            description: "Founder of Bridgewater Associates, expert in risk management and economic principles.",
            specialties: ["Risk Management", "Diversification", "Economic Analysis"]
        ),
        FinancialAgent(
            name: "<PERSON>",
            title: "Growth Stock Specialist",
            expertise: "Growth Investing",
            icon: "chart.line.uptrend.xyaxis",
            color: .green,
            description: "Former Fidelity manager known for finding growth stocks and consumer-focused investments.",
            specialties: ["Growth Stocks", "Consumer Analysis", "Market Research"]
        ),
        FinancialAgent(
            name: "<PERSON>",
            title: "Security Analysis Master",
            expertise: "Fundamental Analysis",
            icon: "magnifyingglass.circle.fill",
            color: .purple,
            description: "Father of value investing and author of 'The Intelligent Investor'.",
            specialties: ["Security Analysis", "Financial Statements", "Margin of Safety"]
        ),
        FinancialAgent(
            name: "John Bogle",
            title: "Index Fund Pioneer",
            expertise: "Passive Investing",
            icon: "chart.bar.fill",
            color: .orange,
            description: "Founder of Vanguard and pioneer of low-cost index fund investing.",
            specialties: ["Index Funds", "Cost Management", "Long-term Planning"]
        ),
        FinancialAgent(
            name: "Cathie Wood",
            title: "Innovation Investor",
            expertise: "Disruptive Technology",
            icon: "bolt.circle.fill",
            color: .pink,
            description: "ARK Invest founder focused on disruptive innovation and technology investments.",
            specialties: ["Technology", "Innovation", "Disruptive Growth"]
        )
    ]
}

// MARK: - Conversation Context

enum ConversationContext: String, CaseIterable {
    case general = "General"
    case investing = "Investing"
    case portfolio = "Portfolio"
    case market = "Market Analysis"
    case risk = "Risk Management"

    var icon: String {
        switch self {
        case .general: return "brain.head.profile"
        case .investing: return "chart.line.uptrend.xyaxis"
        case .portfolio: return "chart.pie"
        case .market: return "chart.bar"
        case .risk: return "shield.checkered"
        }
    }

    var color: Color {
        switch self {
        case .general: return VibeFinanceDesignSystem.Colors.accentGold
        case .investing: return .green
        case .portfolio: return .blue
        case .market: return .purple
        case .risk: return .orange
        }
    }
}

struct BuffettChatView: View {
    @State private var selectedAgent: FinancialAgent?
    @State private var showingAgentChat = false

    var body: some View {
        NavigationView {
            ZStack {
                // Unified Warren Buffett inspired gradient background
                VibeFinanceDesignSystem.Colors.primaryGradient
                    .ignoresSafeArea()

                ScrollView {
                    LazyVStack(spacing: 16) {
                        // Header
                        VStack(spacing: 8) {
                            Text("🏦 AI Financial Advisors")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)

                            Text("Your Virtual Investment Bank Team")
                                .font(.subheadline)
                                .foregroundColor(.white.opacity(0.8))
                        }
                        .padding(.top, 20)

                        // Financial Agents Grid
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                            ForEach(FinancialAgent.allAgents) { agent in
                                FinancialAgentCard(agent: agent) {
                                    selectedAgent = agent
                                    showingAgentChat = true
                                }
                            }
                        }
                        .padding(.horizontal, 16)

                        // Quick Actions
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Quick Actions")
                                .font(.headline)
                                .foregroundColor(.white)
                                .padding(.horizontal, 16)

                            VStack(spacing: 8) {
                                QuickActionRow(
                                    icon: "chart.line.uptrend.xyaxis",
                                    title: "Portfolio Review",
                                    description: "Get comprehensive portfolio analysis",
                                    action: { /* Handle action */ }
                                )

                                QuickActionRow(
                                    icon: "magnifyingglass",
                                    title: "Stock Research",
                                    description: "Research any stock with AI insights",
                                    action: { /* Handle action */ }
                                )

                                QuickActionRow(
                                    icon: "lightbulb",
                                    title: "Investment Ideas",
                                    description: "Get personalized investment suggestions",
                                    action: { /* Handle action */ }
                                )
                            }
                            .padding(.horizontal, 16)
                        }
                        .padding(.top, 20)

                    // Enhanced Messages List with Context
                    ScrollViewReader { proxy in
                        ScrollView {
                            LazyVStack(spacing: 8) {
                                // Conversation starter if no messages
                                if messages.isEmpty {
                                    ConversationStarterView(
                                        onStarterTap: { starter in
                                            sendQuickMessage(starter)
                                        }
                                    )
                                    .padding(.top, 20)
                                }

                                ForEach(messages) { message in
                                    EnhancedChatMessageView(
                                        message: message,
                                        showContext: true
                                    )
                                }

                                if isTyping {
                                    EnhancedTypingIndicatorView()
                                }
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                        }
                        .onChange(of: messages.count) { _, _ in
                            withAnimation(.easeInOut(duration: 0.3)) {
                                proxy.scrollTo(messages.last?.id, anchor: .bottom)
                            }
                        }
                        .onTapGesture {
                            // Hide quick actions when scrolling
                            if showingQuickActions {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    showingQuickActions = false
                                }
                            }
                        }
                    }

                    // Enhanced Message Input with Smart Suggestions
                    EnhancedMessageInputView(
                        messageText: $newMessage,
                        context: conversationContext,
                        onSend: sendMessage,
                        onQuickActionsToggle: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                showingQuickActions.toggle()
                            }
                        }
                    )
                }
            }
            .navigationTitle("AI Advisors")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showingAgentChat) {
                if let agent = selectedAgent {
                    AgentChatView(agent: agent)
                }
            }
        }
    }
}

// MARK: - Supporting Components


// MARK: - Supporting Components

struct FinancialAgentCard: View {
    let agent: FinancialAgent
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Agent Icon
                ZStack {
                    Circle()
                        .fill(agent.color.opacity(0.2))
                        .frame(width: 60, height: 60)

                    Image(systemName: agent.icon)
                        .font(.title2)
                        .foregroundColor(agent.color)
                }

                // Agent Info
                VStack(spacing: 4) {
                    Text(agent.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .lineLimit(1)

                    Text(agent.title)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                        .lineLimit(2)
                        .multilineTextAlignment(.center)

                    Text(agent.expertise)
                        .font(.caption2)
                        .foregroundColor(agent.color)
                        .lineLimit(1)
                }
            }
            .padding(16)
            .frame(height: 140)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct QuickActionRow: View {
    let icon: String
    let title: String
    let description: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.yellow)
                    .frame(width: 30)

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.5))
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

// MARK: - Buffett Message Input
struct BuffettMessageInputView: View {
    @Binding var messageText: String
    let onSend: () -> Void

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            HStack(spacing: 12) {
                TextField("Ask Warren for investment wisdom...", text: $messageText, axis: .vertical)
                    .textFieldStyle(PlainTextFieldStyle())
                    .foregroundColor(.white)
                    .lineLimit(1...4)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)

                Button(action: onSend) {
                    Image(systemName: "arrow.up.circle.fill")
                        .font(.title2)
                        .foregroundColor(messageText.isEmpty ? .white.opacity(0.4) : .yellow)
                }
                .disabled(messageText.isEmpty)
                .padding(.trailing, 8)
            }
        }
        .frame(minHeight: 44)
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
    }
}

// MARK: - Data Models
struct BuffettChatMessage: Identifiable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp: Date
    let buffettWisdom: String?

    static let mockMessages: [BuffettChatMessage] = [
        BuffettChatMessage(
            content: "Hello! I'm Warren's AI Adviser, trained on 60+ years of investment wisdom from the Oracle of Omaha himself. I can help you understand value investing, analyze companies, build portfolios, and develop the patience needed for long-term wealth building. What would you like to learn about investing today?",
            isFromUser: false,
            timestamp: Date().addingTimeInterval(-3600),
            buffettWisdom: "The best investment you can make is in yourself."
        ),
        BuffettChatMessage(
            content: "I'm new to investing. Where should I start?",
            isFromUser: true,
            timestamp: Date().addingTimeInterval(-3500),
            buffettWisdom: nil
        ),
        BuffettChatMessage(
            content: "Excellent question! Start by investing in yourself - read everything you can about businesses and investing. I recommend starting with index funds while you learn. Focus on companies you understand, with strong competitive advantages (what I call 'economic moats'), excellent management, and reasonable prices. Remember, time is your greatest ally in investing.",
            isFromUser: false,
            timestamp: Date().addingTimeInterval(-3400),
            buffettWisdom: "Time is the friend of the wonderful company, the enemy of the mediocre."
        )
    ]
}

struct BuffettQuickAction: Identifiable {
    let id = UUID()
    let emoji: String
    let title: String
    let message: String

    static let allActions: [BuffettQuickAction] = [
        BuffettQuickAction(
            emoji: "📊",
            title: "Market Analysis",
            message: "What's your take on the current market conditions?"
        ),
        BuffettQuickAction(
            emoji: "💎",
            title: "Value Stocks",
            message: "Help me find undervalued companies with strong fundamentals"
        ),
        BuffettQuickAction(
            emoji: "🏰",
            title: "Economic Moats",
            message: "Explain economic moats and how to identify them"
        ),
        BuffettQuickAction(
            emoji: "📚",
            title: "Learning",
            message: "What books and resources do you recommend for learning investing?"
        ),
        BuffettQuickAction(
            emoji: "⏰",
            title: "Long-term",
            message: "How do I develop patience for long-term investing?"
        )
    ]
}

// MARK: - Enhanced Components

struct ContextualQuickActionsView: View {
    let context: ConversationContext
    let onActionTap: (QuickAction) -> Void

    private var contextualActions: [QuickAction] {
        switch context {
        case .general:
            return [
                QuickAction(title: "Start Investing", message: "How do I start investing?", icon: "chart.line.uptrend.xyaxis"),
                QuickAction(title: "Portfolio Tips", message: "How should I build my portfolio?", icon: "chart.pie"),
                QuickAction(title: "Market Analysis", message: "What's happening in the market?", icon: "chart.bar"),
                QuickAction(title: "Risk Management", message: "How do I manage investment risk?", icon: "shield.checkered")
            ]
        case .investing:
            return [
                QuickAction(title: "Value Stocks", message: "How do I find undervalued stocks?", icon: "magnifyingglass"),
                QuickAction(title: "Company Analysis", message: "What makes a good company?", icon: "building.2"),
                QuickAction(title: "When to Buy", message: "When is the best time to buy?", icon: "clock"),
                QuickAction(title: "Long-term Strategy", message: "How long should I hold stocks?", icon: "calendar")
            ]
        case .portfolio:
            return [
                QuickAction(title: "Diversification", message: "How diversified should my portfolio be?", icon: "chart.pie"),
                QuickAction(title: "Rebalancing", message: "When should I rebalance?", icon: "arrow.triangle.2.circlepath"),
                QuickAction(title: "Asset Allocation", message: "How should I allocate my assets?", icon: "percent"),
                QuickAction(title: "Performance Review", message: "How do I review my portfolio performance?", icon: "chart.xyaxis.line")
            ]
        case .market:
            return [
                QuickAction(title: "Market Trends", message: "What are the current market trends?", icon: "chart.line.uptrend.xyaxis"),
                QuickAction(title: "Economic Indicators", message: "Which economic indicators matter?", icon: "chart.bar"),
                QuickAction(title: "Market Timing", message: "Should I try to time the market?", icon: "timer"),
                QuickAction(title: "Bear Markets", message: "How do I handle bear markets?", icon: "chart.line.downtrend.xyaxis")
            ]
        case .risk:
            return [
                QuickAction(title: "Risk Assessment", message: "How do I assess investment risk?", icon: "checkmark.shield"),
                QuickAction(title: "Risk Tolerance", message: "What's my risk tolerance?", icon: "heart.text.square"),
                QuickAction(title: "Risk Mitigation", message: "How do I reduce investment risk?", icon: "shield.lefthalf.filled"),
                QuickAction(title: "Emergency Fund", message: "How much should I keep in emergency funds?", icon: "banknote")
            ]
        }
    }

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(contextualActions, id: \.title) { action in
                    Button(action: { onActionTap(action) }) {
                        HStack(spacing: 6) {
                            Image(systemName: action.icon)
                                .font(.caption)

                            Text(action.title)
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color.white.opacity(0.15))
                                .background(
                                    RoundedRectangle(cornerRadius: 16)
                                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .foregroundColor(.white)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal, 16)
        }
    }
}

struct QuickAction {
    let title: String
    let message: String
    let icon: String
}

struct ConversationStarterView: View {
    let onStarterTap: (String) -> Void

    private let starters = [
        "How do I start investing with $1000?",
        "What makes Warren Buffett successful?",
        "Explain value investing in simple terms",
        "How do I analyze a company's stock?"
    ]

    var body: some View {
        VStack(spacing: 16) {
            VStack(spacing: 8) {
                Text("🧠")
                    .font(.system(size: 40))

                Text("Ask Warren Anything")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("Get investment wisdom from 60+ years of experience")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }

            VStack(spacing: 12) {
                ForEach(starters, id: \.self) { starter in
                    Button(action: { onStarterTap(starter) }) {
                        HStack {
                            Text(starter)
                                .font(.subheadline)
                                .foregroundColor(.white)
                                .multilineTextAlignment(.leading)

                            Spacer()

                            Image(systemName: "arrow.right.circle")
                                .font(.title3)
                                .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                        }
                        .padding(16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.white.opacity(0.1))
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                )
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(20)
    }
}

struct EnhancedChatMessageView: View {
    let message: BuffettChatMessage
    let showContext: Bool

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            if !message.isFromUser {
                // Warren's avatar
                Circle()
                    .fill(VibeFinanceDesignSystem.Colors.accentGold.opacity(0.3))
                    .frame(width: 32, height: 32)
                    .overlay(
                        Text("🧠")
                            .font(.caption)
                    )
            }

            VStack(alignment: message.isFromUser ? .trailing : .leading, spacing: 8) {
                // Message bubble
                Text(message.content)
                    .font(.body)
                    .foregroundColor(message.isFromUser ? .black : .white)
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                message.isFromUser ?
                                VibeFinanceDesignSystem.Colors.accentGold :
                                Color.white.opacity(0.15)
                            )
                    )

                // Warren's wisdom quote
                if let wisdom = message.buffettWisdom {
                    Text("💡 \"\(wisdom)\"")
                        .font(.caption)
                        .italic()
                        .foregroundColor(.white.opacity(0.8))
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.white.opacity(0.1))
                        )
                }

                // Timestamp
                Text(message.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.5))
            }

            if message.isFromUser {
                Spacer()
            }
        }
        .frame(maxWidth: .infinity, alignment: message.isFromUser ? .trailing : .leading)
    }
}

struct EnhancedTypingIndicatorView: View {
    @State private var animationOffset: CGFloat = 0

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // Warren's avatar
            Circle()
                .fill(VibeFinanceDesignSystem.Colors.accentGold.opacity(0.3))
                .frame(width: 32, height: 32)
                .overlay(
                    Text("🧠")
                        .font(.caption)
                )

            HStack(spacing: 4) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(Color.white.opacity(0.6))
                        .frame(width: 8, height: 8)
                        .offset(y: animationOffset)
                        .animation(
                            .easeInOut(duration: 0.6)
                            .repeatForever(autoreverses: true)
                            .delay(Double(index) * 0.2),
                            value: animationOffset
                        )
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.15))
            )

            Spacer()
        }
        .onAppear {
            animationOffset = -4
        }
    }
}

struct EnhancedMessageInputView: View {
    @Binding var messageText: String
    let context: ConversationContext
    let onSend: () -> Void
    let onQuickActionsToggle: () -> Void

    @State private var isRecording = false

    var body: some View {
        VStack(spacing: 0) {
            // Input area
            HStack(spacing: 12) {
                // Quick actions toggle
                Button(action: onQuickActionsToggle) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                        .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                }

                // Text input
                HStack(spacing: 8) {
                    TextField("Ask Warren anything...", text: $messageText, axis: .vertical)
                        .font(.body)
                        .foregroundColor(.white)
                        .lineLimit(1...4)

                    if !messageText.isEmpty {
                        Button(action: onSend) {
                            Image(systemName: "arrow.up.circle.fill")
                                .font(.title2)
                                .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                        }
                    } else {
                        Button(action: { isRecording.toggle() }) {
                            Image(systemName: isRecording ? "stop.circle.fill" : "mic.circle.fill")
                                .font(.title2)
                                .foregroundColor(isRecording ? .red : VibeFinanceDesignSystem.Colors.accentGold)
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 24)
                        .fill(Color.white.opacity(0.15))
                        .background(
                            RoundedRectangle(cornerRadius: 24)
                                .stroke(Color.white.opacity(0.3), lineWidth: 1)
                        )
                )
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                Rectangle()
                    .fill(VibeFinanceDesignSystem.Colors.primaryBlue.opacity(0.9))
                    .blur(radius: 20)
            )
        }
    }
}

// MARK: - Supporting Components

struct FinancialAgentCard: View {
    let agent: FinancialAgent
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Agent Icon
                ZStack {
                    Circle()
                        .fill(agent.color.opacity(0.2))
                        .frame(width: 60, height: 60)

                    Image(systemName: agent.icon)
                        .font(.title2)
                        .foregroundColor(agent.color)
                }

                // Agent Info
                VStack(spacing: 4) {
                    Text(agent.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .lineLimit(1)

                    Text(agent.title)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                        .lineLimit(2)
                        .multilineTextAlignment(.center)

                    Text(agent.expertise)
                        .font(.caption2)
                        .foregroundColor(agent.color)
                        .lineLimit(1)
                }
            }
            .padding(16)
            .frame(height: 140)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct QuickActionRow: View {
    let icon: String
    let title: String
    let description: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.yellow)
                    .frame(width: 30)

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.5))
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AgentChatView: View {
    let agent: FinancialAgent
    @Environment(\.dismiss) private var dismiss
    @State private var messages: [String] = []
    @State private var newMessage = ""

    var body: some View {
        NavigationView {
            VStack {
                // Agent Header
                HStack(spacing: 12) {
                    Image(systemName: agent.icon)
                        .font(.title2)
                        .foregroundColor(agent.color)

                    VStack(alignment: .leading) {
                        Text(agent.name)
                            .font(.headline)
                            .fontWeight(.bold)
                        Text(agent.expertise)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }
                .padding()
                .background(Color(.systemGray6))

                // Chat Area
                ScrollView {
                    LazyVStack(spacing: 12) {
                        Text("Chat with \(agent.name) - Coming Soon!")
                            .foregroundColor(.secondary)
                            .padding()
                    }
                }

                // Message Input
                HStack {
                    TextField("Ask \(agent.name.components(separatedBy: " ").first ?? "")...", text: $newMessage)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    Button("Send") {
                        // Handle send
                    }
                    .disabled(newMessage.isEmpty)
                }
                .padding()
            }
            .navigationTitle(agent.name)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden()
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    BuffettChatView()
        .preferredColorScheme(.dark)
}
