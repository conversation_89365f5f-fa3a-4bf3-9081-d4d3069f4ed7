//
//  BuffettChatView.swift
//  VibeFinance - <PERSON> Inspired AI Adviser
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

// MARK: - Financial Agent Models

struct FinancialAgent: Identifiable {
    let id = UUID()
    let name: String
    let title: String
    let expertise: String
    let icon: String
    let color: Color
    let description: String
    let specialties: [String]

    static let allAgents: [FinancialAgent] = [
        FinancialAgent(
            name: "<PERSON>",
            title: "Value Investing Guru",
            expertise: "Long-term Value Investing",
            icon: "chart.pie.fill",
            color: .yellow,
            description: "The Oracle of Omaha specializes in finding undervalued companies with strong fundamentals.",
            specialties: ["Value Investing", "Company Analysis", "Long-term Strategy"]
        ),
        FinancialAgent(
            name: "<PERSON>",
            title: "Risk Management Expert",
            expertise: "Portfolio Diversification",
            icon: "shield.checkered",
            color: .blue,
            description: "Founder of Bridgewater Associates, expert in risk management and economic principles.",
            specialties: ["Risk Management", "Diversification", "Economic Analysis"]
        ),
        FinancialAgent(
            name: "<PERSON>",
            title: "Growth Stock Specialist",
            expertise: "Growth Investing",
            icon: "chart.line.uptrend.xyaxis",
            color: .green,
            description: "Former Fidelity manager known for finding growth stocks and consumer-focused investments.",
            specialties: ["Growth Stocks", "Consumer Analysis", "Market Research"]
        ),
        FinancialAgent(
            name: "<PERSON>",
            title: "Security Analysis Master",
            expertise: "Fundamental Analysis",
            icon: "magnifyingglass.circle.fill",
            color: .purple,
            description: "Father of value investing and author of 'The Intelligent Investor'.",
            specialties: ["Security Analysis", "Financial Statements", "Margin of Safety"]
        ),
        FinancialAgent(
            name: "John Bogle",
            title: "Index Fund Pioneer",
            expertise: "Passive Investing",
            icon: "chart.bar.fill",
            color: .orange,
            description: "Founder of Vanguard and pioneer of low-cost index fund investing.",
            specialties: ["Index Funds", "Cost Management", "Long-term Planning"]
        ),
        FinancialAgent(
            name: "Cathie Wood",
            title: "Innovation Investor",
            expertise: "Disruptive Technology",
            icon: "bolt.circle.fill",
            color: .pink,
            description: "ARK Invest founder focused on disruptive innovation and technology investments.",
            specialties: ["Technology", "Innovation", "Disruptive Growth"]
        )
    ]
}

// MARK: - Conversation Context

enum ConversationContext: String, CaseIterable {
    case general = "General"
    case investing = "Investing"
    case portfolio = "Portfolio"
    case market = "Market Analysis"
    case risk = "Risk Management"

    var icon: String {
        switch self {
        case .general: return "brain.head.profile"
        case .investing: return "chart.line.uptrend.xyaxis"
        case .portfolio: return "chart.pie"
        case .market: return "chart.bar"
        case .risk: return "shield.checkered"
        }
    }

    var color: Color {
        switch self {
        case .general: return VibeFinanceDesignSystem.Colors.accentGold
        case .investing: return .green
        case .portfolio: return .blue
        case .market: return .purple
        case .risk: return .orange
        }
    }
}

struct BuffettChatView: View {
    @State private var selectedAgent: FinancialAgent?
    @State private var showingAgentChat = false

    var body: some View {
        NavigationView {
            ZStack {
                // Unified Warren Buffett inspired gradient background
                VibeFinanceDesignSystem.Colors.primaryGradient
                    .ignoresSafeArea()

                ScrollView {
                    LazyVStack(spacing: 16) {
                        // Header
                        VStack(spacing: 8) {
                            Text("🏦 AI Financial Advisors")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.white)

                            Text("Your Virtual Investment Bank Team")
                                .font(.subheadline)
                                .foregroundColor(.white.opacity(0.8))
                        }
                        .padding(.top, 20)

                        // Financial Agents Grid
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                            ForEach(FinancialAgent.allAgents) { agent in
                                FinancialAgentCard(agent: agent) {
                                    selectedAgent = agent
                                    showingAgentChat = true
                                }
                            }
                        }
                        .padding(.horizontal, 16)

                        // Quick Actions
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Quick Actions")
                                .font(.headline)
                                .foregroundColor(.white)
                                .padding(.horizontal, 16)

                            VStack(spacing: 8) {
                                QuickActionRow(
                                    icon: "chart.line.uptrend.xyaxis",
                                    title: "Portfolio Review",
                                    description: "Get comprehensive portfolio analysis",
                                    action: { /* Handle action */ }
                                )

                                QuickActionRow(
                                    icon: "magnifyingglass",
                                    title: "Stock Research",
                                    description: "Research any stock with AI insights",
                                    action: { /* Handle action */ }
                                )

                                QuickActionRow(
                                    icon: "lightbulb",
                                    title: "Investment Ideas",
                                    description: "Get personalized investment suggestions",
                                    action: { /* Handle action */ }
                                )
                            }
                            .padding(.horizontal, 16)
                        }
                        .padding(.top, 20)
                    }
                    .padding(.bottom, 100)
                }
            }
            .navigationTitle("AI Advisors")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showingAgentChat) {
                if let agent = selectedAgent {
                    AgentChatView(agent: agent)
                }
            }
        }
    }
}

// MARK: - Supporting Components

struct FinancialAgentCard: View {
    let agent: FinancialAgent
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // Agent Icon
                ZStack {
                    Circle()
                        .fill(agent.color.opacity(0.2))
                        .frame(width: 60, height: 60)

                    Image(systemName: agent.icon)
                        .font(.title2)
                        .foregroundColor(agent.color)
                }

                // Agent Info
                VStack(spacing: 4) {
                    Text(agent.name)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .lineLimit(1)

                    Text(agent.title)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                        .lineLimit(2)
                        .multilineTextAlignment(.center)

                    Text(agent.expertise)
                        .font(.caption2)
                        .foregroundColor(agent.color)
                        .lineLimit(1)
                }
            }
            .padding(16)
            .frame(height: 140)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct QuickActionRow: View {
    let icon: String
    let title: String
    let description: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.yellow)
                    .frame(width: 30)

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.5))
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.white.opacity(0.1), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct AgentChatView: View {
    let agent: FinancialAgent
    @Environment(\.dismiss) private var dismiss
    @State private var messages: [String] = []
    @State private var newMessage = ""

    var body: some View {
        NavigationView {
            VStack {
                // Agent Header
                HStack(spacing: 12) {
                    Image(systemName: agent.icon)
                        .font(.title2)
                        .foregroundColor(agent.color)

                    VStack(alignment: .leading) {
                        Text(agent.name)
                            .font(.headline)
                            .fontWeight(.bold)
                        Text(agent.expertise)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()
                }
                .padding()
                .background(Color(.systemGray6))

                // Chat Area
                ScrollView {
                    LazyVStack(spacing: 12) {
                        Text("Chat with \(agent.name) - Coming Soon!")
                            .foregroundColor(.secondary)
                            .padding()
                    }
                }

                // Message Input
                HStack {
                    TextField("Ask \(agent.name.components(separatedBy: " ").first ?? "")...", text: $newMessage)
                        .textFieldStyle(RoundedBorderTextFieldStyle())

                    Button("Send") {
                        // Handle send
                    }
                    .disabled(newMessage.isEmpty)
                }
                .padding()
            }
            .navigationTitle(agent.name)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden()
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    BuffettChatView()
        .preferredColorScheme(.dark)
}
