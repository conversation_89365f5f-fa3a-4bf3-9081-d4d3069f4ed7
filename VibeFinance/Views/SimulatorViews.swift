//
//  SimulatorViews.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Simulator History View
struct SimulatorHistoryView: View {
    @EnvironmentObject var simulatorManager: SimulatorManager
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                if simulatorManager.transactions.isEmpty {
                    SimulatorEmptyHistoryView()
                } else {
                    ForEach(simulatorManager.transactions.sorted { $0.timestamp > $1.timestamp }) { transaction in
                        SimulatorTransactionCard(transaction: transaction)
                    }
                }
            }
            .padding()
        }
    }
}

// MARK: - Simulator Empty History View
struct SimulatorEmptyHistoryView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "clock")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Trading History")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Your trading history will appear here once you start buying and selling stocks.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
    }
}

// MARK: - Simulator Transaction Card
struct SimulatorTransactionCard: View {
    let transaction: SimulatorTransaction
    
    var body: some View {
        HStack(spacing: 12) {
            // Transaction Type Icon
            Circle()
                .fill(transaction.type == .buy ? Color.green.opacity(0.1) : Color.red.opacity(0.1))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: transaction.type == .buy ? "arrow.down.circle.fill" : "arrow.up.circle.fill")
                        .foregroundColor(transaction.type == .buy ? .green : .red)
                )
            
            // Transaction Details
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(transaction.type == .buy ? "Bought" : "Sold")
                        .font(.headline)
                        .fontWeight(.semibold)
                    Text(transaction.symbol)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                }
                
                Text("\(Int(transaction.shares)) shares at $\(transaction.price, specifier: "%.2f")")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text(transaction.timestamp, style: .relative)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Transaction Amount
            VStack(alignment: .trailing, spacing: 2) {
                Text("$\(transaction.totalAmount, specifier: "%.2f")")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(transaction.type == .buy ? .red : .green)
                
                Text(transaction.type == .buy ? "Spent" : "Received")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Simulator Leaderboard View
struct SimulatorLeaderboardView: View {
    @State private var leaderboardData: [LeaderboardEntry] = []
    @State private var isLoading = true
    @State private var selectedPeriod: LeaderboardPeriod = .allTime
    
    var body: some View {
        VStack(spacing: 0) {
            // Period Selector
            LeaderboardPeriodSelector(selectedPeriod: $selectedPeriod)
            
            // Leaderboard List
            ScrollView {
                LazyVStack(spacing: 12) {
                    if isLoading {
                        ForEach(0..<10, id: \.self) { _ in
                            LeaderboardEntrySkeleton()
                        }
                    } else {
                        ForEach(Array(leaderboardData.enumerated()), id: \.element.id) { index, entry in
                            LeaderboardEntryCard(
                                entry: entry,
                                rank: index + 1,
                                isCurrentUser: entry.isCurrentUser
                            )
                        }
                    }
                }
                .padding()
            }
        }
        .onAppear {
            loadLeaderboard()
        }
        .onChange(of: selectedPeriod) { _ in
            loadLeaderboard()
        }
    }
    
    private func loadLeaderboard() {
        isLoading = true
        
        // Simulate loading leaderboard data
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            leaderboardData = generateMockLeaderboard()
            isLoading = false
        }
    }
    
    private func generateMockLeaderboard() -> [LeaderboardEntry] {
        return [
            LeaderboardEntry(username: "InvestorPro", return: 45.2, portfolioValue: 14520.0, isCurrentUser: false),
            LeaderboardEntry(username: "StockMaster", return: 38.7, portfolioValue: 13870.0, isCurrentUser: false),
            LeaderboardEntry(username: "You", return: 25.3, portfolioValue: 12530.0, isCurrentUser: true),
            LeaderboardEntry(username: "TradingGuru", return: 22.1, portfolioValue: 12210.0, isCurrentUser: false),
            LeaderboardEntry(username: "MarketWiz", return: 18.9, portfolioValue: 11890.0, isCurrentUser: false),
            LeaderboardEntry(username: "BullRunner", return: 15.4, portfolioValue: 11540.0, isCurrentUser: false),
            LeaderboardEntry(username: "ValueSeeker", return: 12.8, portfolioValue: 11280.0, isCurrentUser: false),
            LeaderboardEntry(username: "GrowthHunter", return: 9.3, portfolioValue: 10930.0, isCurrentUser: false),
            LeaderboardEntry(username: "DividendKing", return: 7.1, portfolioValue: 10710.0, isCurrentUser: false),
            LeaderboardEntry(username: "RookieTrader", return: 3.2, portfolioValue: 10320.0, isCurrentUser: false)
        ]
    }
}

// MARK: - Leaderboard Period Selector
struct LeaderboardPeriodSelector: View {
    @Binding var selectedPeriod: LeaderboardPeriod
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(LeaderboardPeriod.allCases, id: \.self) { period in
                Button(action: {
                    selectedPeriod = period
                }) {
                    Text(period.displayName)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(selectedPeriod == period ? .white : .secondary)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(selectedPeriod == period ? Color.purple : Color.clear)
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
    }
}

// MARK: - Leaderboard Entry Card
struct LeaderboardEntryCard: View {
    let entry: LeaderboardEntry
    let rank: Int
    let isCurrentUser: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            // Rank
            Text("\(rank)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(rankColor)
                .frame(width: 30)
            
            // User Info
            HStack(spacing: 8) {
                Circle()
                    .fill(isCurrentUser ? Color.purple.opacity(0.2) : Color.gray.opacity(0.2))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Text(entry.username.prefix(2).uppercased())
                            .font(.caption)
                            .fontWeight(.bold)
                            .foregroundColor(isCurrentUser ? .purple : .gray)
                    )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(entry.username)
                        .font(.subheadline)
                        .fontWeight(isCurrentUser ? .bold : .medium)
                        .foregroundColor(isCurrentUser ? .purple : .primary)
                    
                    Text("$\(entry.portfolioValue, specifier: "%.0f")")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            // Return
            VStack(alignment: .trailing, spacing: 2) {
                Text("+\(entry.return, specifier: "%.1f")%")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.green)
                
                if rank <= 3 {
                    Image(systemName: rankIcon)
                        .font(.caption)
                        .foregroundColor(rankColor)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isCurrentUser ? Color.purple.opacity(0.1) : Color(.systemBackground))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isCurrentUser ? Color.purple : Color.clear, lineWidth: 2)
                )
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
    
    private var rankColor: Color {
        switch rank {
        case 1: return .gold
        case 2: return .gray
        case 3: return Color(red: 0.8, green: 0.5, blue: 0.2) // Bronze
        default: return .secondary
        }
    }
    
    private var rankIcon: String {
        switch rank {
        case 1: return "crown.fill"
        case 2: return "medal.fill"
        case 3: return "medal"
        default: return ""
        }
    }
}

// MARK: - Leaderboard Entry Skeleton
struct LeaderboardEntrySkeleton: View {
    @State private var isAnimating = false
    
    var body: some View {
        HStack(spacing: 12) {
            RoundedRectangle(cornerRadius: 4)
                .fill(Color(.systemGray5))
                .frame(width: 30, height: 20)
            
            Circle()
                .fill(Color(.systemGray5))
                .frame(width: 40, height: 40)
            
            VStack(alignment: .leading, spacing: 4) {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color(.systemGray5))
                    .frame(width: 80, height: 16)
                
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color(.systemGray5))
                    .frame(width: 60, height: 12)
            }
            
            Spacer()
            
            RoundedRectangle(cornerRadius: 4)
                .fill(Color(.systemGray5))
                .frame(width: 60, height: 20)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .opacity(isAnimating ? 0.5 : 1.0)
        .animation(
            Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true),
            value: isAnimating
        )
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - Simulator Stock Search View
struct SimulatorStockSearchView: View {
    let onStockSelected: (StockPrice) -> Void
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var searchResults: [StockPrice] = []
    @State private var isSearching = false
    
    private let stockService = StockServiceManager.shared
    private let popularStocks = ["AAPL", "TSLA", "NVDA", "GOOGL", "MSFT", "AMZN", "META", "NFLX"]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Search Bar
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                    
                    TextField("Search stocks...", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .onSubmit {
                            searchStocks()
                        }
                    
                    if !searchText.isEmpty {
                        Button("Clear") {
                            searchText = ""
                            searchResults = []
                        }
                        .font(.caption)
                        .foregroundColor(.purple)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                
                // Results
                ScrollView {
                    LazyVStack(spacing: 12) {
                        if searchText.isEmpty {
                            // Popular stocks
                            Section {
                                ForEach(popularStocks, id: \.self) { symbol in
                                    PopularStockRow(symbol: symbol) { stock in
                                        onStockSelected(stock)
                                        dismiss()
                                    }
                                }
                            } header: {
                                HStack {
                                    Text("Popular Stocks")
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                    Spacer()
                                }
                                .padding(.horizontal)
                                .padding(.top)
                            }
                        } else if isSearching {
                            ForEach(0..<5, id: \.self) { _ in
                                SimulatorMarketCardSkeleton()
                            }
                        } else {
                            ForEach(searchResults, id: \.symbol) { stock in
                                SimulatorMarketCard(stock: stock) {
                                    onStockSelected(stock)
                                    dismiss()
                                }
                            }
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle("Search Stocks")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private func searchStocks() {
        guard !searchText.isEmpty else { return }
        
        isSearching = true
        
        Task {
            // Simulate search delay
            try? await Task.sleep(nanoseconds: 500_000_000)
            
            // For demo, filter popular stocks
            let filtered = popularStocks.filter {
                $0.localizedCaseInsensitiveContains(searchText)
            }
            
            var results: [StockPrice] = []
            for symbol in filtered {
                if let stock = await stockService.getStockData(symbol: symbol) {
                    results.append(stock)
                }
            }
            
            await MainActor.run {
                self.searchResults = results
                self.isSearching = false
            }
        }
    }
}

// MARK: - Popular Stock Row
struct PopularStockRow: View {
    let symbol: String
    let onSelect: (StockPrice) -> Void
    @State private var stockData: StockPrice?
    
    var body: some View {
        if let stock = stockData {
            SimulatorMarketCard(stock: stock) {
                onSelect(stock)
            }
        } else {
            SimulatorMarketCardSkeleton()
                .onAppear {
                    loadStockData()
                }
        }
    }
    
    private func loadStockData() {
        Task {
            if let stock = await StockServiceManager.shared.getStockData(symbol: symbol) {
                await MainActor.run {
                    self.stockData = stock
                }
            }
        }
    }
}

// MARK: - Supporting Types
enum LeaderboardPeriod: String, CaseIterable {
    case daily = "daily"
    case weekly = "weekly"
    case monthly = "monthly"
    case allTime = "all_time"
    
    var displayName: String {
        switch self {
        case .daily: return "Today"
        case .weekly: return "This Week"
        case .monthly: return "This Month"
        case .allTime: return "All Time"
        }
    }
}

struct LeaderboardEntry: Identifiable {
    let id = UUID()
    let username: String
    let `return`: Double
    let portfolioValue: Double
    let isCurrentUser: Bool
}
