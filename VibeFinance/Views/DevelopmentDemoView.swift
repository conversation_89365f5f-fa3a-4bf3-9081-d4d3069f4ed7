//
//  DevelopmentDemoView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct DevelopmentDemoView: View {
    @EnvironmentObject var authManager: AuthManager
    @State private var showingMagicMCPInfo = false
    @State private var showingMockAuthInfo = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 16) {
                        Text("🚀")
                            .font(.system(size: 60))
                        
                        Text("Enhanced Development")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text("Supercharged development tools for VibeFinance")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 20)
                    
                    // Mock Authentication Section
                    DemoCard(
                        icon: "🔐",
                        title: "Mock Authentication",
                        subtitle: "Skip login during development",
                        description: "Instantly log in with test users, no network calls required",
                        isEnabled: authManager.useMockAuth,
                        action: {
                            showingMockAuthInfo = true
                        }
                    )
                    
                    // Magic MCP Section
                    DemoCard(
                        icon: "🎨",
                        title: "Magic MCP Integration",
                        subtitle: "AI-powered UI generation",
                        description: "Create SwiftUI components with natural language",
                        isEnabled: DevelopmentConfig.enableMagicMCP,
                        action: {
                            showingMagicMCPInfo = true
                        }
                    )
                    
                    // Development Status
                    if authManager.isDevelopmentMode {
                        VStack(spacing: 16) {
                            Text("Development Status")
                                .font(.headline)
                                .frame(maxWidth: .infinity, alignment: .leading)
                            
                            StatusRow(
                                title: "Development Mode",
                                value: authManager.isDevelopmentMode ? "Enabled" : "Disabled",
                                isPositive: authManager.isDevelopmentMode
                            )
                            
                            StatusRow(
                                title: "Mock Authentication",
                                value: authManager.useMockAuth ? "Active" : "Inactive",
                                isPositive: authManager.useMockAuth
                            )
                            
                            StatusRow(
                                title: "Current User",
                                value: authManager.currentUser?.username ?? "None",
                                isPositive: authManager.currentUser != nil
                            )
                            
                            StatusRow(
                                title: "Build Configuration",
                                value: DevelopmentConfig.buildConfiguration,
                                isPositive: DevelopmentConfig.isDevelopmentMode
                            )
                        }
                        .padding(20)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color(.systemGray6))
                        )
                    }
                    
                    // Quick Actions
                    VStack(spacing: 16) {
                        Text("Quick Actions")
                            .font(.headline)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        
                        if authManager.isDevelopmentMode {
                            QuickActionButton(
                                icon: "bolt.fill",
                                title: "Quick Dev Login",
                                subtitle: "Instant login with dev user",
                                color: .orange
                            ) {
                                authManager.quickDevLogin()
                            }
                            
                            QuickActionButton(
                                icon: "gearshape.fill",
                                title: "Developer Settings",
                                subtitle: "Configure development options",
                                color: .blue
                            ) {
                                // This would open developer settings
                                // Implementation depends on navigation structure
                            }
                            
                            QuickActionButton(
                                icon: "doc.text.fill",
                                title: "View Documentation",
                                subtitle: "Read setup guides and examples",
                                color: .green
                            ) {
                                // This would open documentation
                                // Could open in Safari or in-app browser
                            }
                        }
                    }
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, 20)
            }
            .navigationTitle("Development Tools")
            .navigationBarTitleDisplayMode(.inline)
        }
        .sheet(isPresented: $showingMockAuthInfo) {
            MockAuthInfoView()
        }
        .sheet(isPresented: $showingMagicMCPInfo) {
            MagicMCPInfoView()
        }
    }
}

struct DemoCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let description: String
    let isEnabled: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 16) {
                HStack {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text(icon)
                                .font(.title)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text(title)
                                    .font(.headline)
                                    .foregroundColor(.primary)
                                
                                Text(subtitle)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Circle()
                                .fill(isEnabled ? Color.green : Color.gray)
                                .frame(width: 12, height: 12)
                        }
                        
                        Text(description)
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.leading)
                    }
                    
                    Spacer()
                }
                
                HStack {
                    Spacer()
                    Text("Learn More")
                        .font(.caption)
                        .foregroundColor(.blue)
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.blue)
                }
            }
            .padding(20)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct StatusRow: View {
    let title: String
    let value: String
    let isPositive: Bool
    
    var body: some View {
        HStack {
            Text(title)
                .font(.body)
            
            Spacer()
            
            Text(value)
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(isPositive ? .green : .red)
        }
    }
}

struct QuickActionButton: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// Info views for the sheets
struct MockAuthInfoView: View {
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Mock Authentication allows you to bypass real authentication during development, making it faster to test features and iterate on your code.")
                    
                    Text("Features:")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("• Instant login without network calls")
                        Text("• Multiple test user profiles")
                        Text("• Persistent settings between launches")
                        Text("• Easy toggle on/off")
                    }
                    
                    Text("Available Mock Users:")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("• Dev User (<EMAIL>)")
                        Text("• Test User (<EMAIL>)")
                        Text("• Demo User (<EMAIL>)")
                        Text("• Investor Pro (<EMAIL>)")
                    }
                }
                .padding()
            }
            .navigationTitle("Mock Authentication")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct MagicMCPInfoView: View {
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Magic MCP (Model Context Protocol) enables AI-powered UI component generation directly in your IDE using natural language descriptions.")
                    
                    Text("How to Use:")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("1. Open Cursor Composer (Cmd+I)")
                        Text("2. Type '/ui' followed by your description")
                        Text("3. Magic generates SwiftUI components")
                        Text("4. Components are added to your project")
                    }
                    
                    Text("Example Prompts:")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("• /ui create a SwiftUI portfolio card")
                        Text("• /ui design a quest completion animation")
                        Text("• /ui build an investment simulator interface")
                    }
                    
                    Text("Setup Required:")
                        .font(.headline)
                    
                    Text("Run ./scripts/setup-magic-mcp.sh to configure Magic MCP with your API key.")
                }
                .padding()
            }
            .navigationTitle("Magic MCP")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

#if DEBUG
struct DevelopmentDemoView_Previews: PreviewProvider {
    static var previews: some View {
        DevelopmentDemoView()
            .environmentObject(AuthManager())
    }
}
#endif
