//
//  AppleIntelligencePortfolioView.swift
//  VibeFinance - Apple Intelligence Powered
//
//  Created by MAGESH DHANASEKARAN on 12/26/24.
//  Apple Intelligence powered portfolio analysis with iOS 18+ features
//

import SwiftUI
import Charts

/// Apple Intelligence powered portfolio analysis view
/// Implements Apple's latest design patterns and AI capabilities
struct AppleIntelligencePortfolioView: View {
    @StateObject private var aiManager = AppleIntelligenceManager.shared
    @StateObject private var portfolioManager = PortfolioManager.shared
    @StateObject private var hapticManager = HapticManager()
    
    @State private var portfolioAnalysis: PortfolioAnalysis?
    @State private var isAnalyzing = false
    @State private var showingAIInsights = false
    @State private var selectedTimeframe: TimeFrame = .oneMonth
    @State private var smartSuggestions: [SmartSuggestion] = []
    
    // Apple Intelligence features
    @State private var aiSummary: String = ""
    @State private var riskAssessment: RiskAssessment?
    @State private var optimizationSuggestions: [OptimizationSuggestion] = []
    
    // Accessibility
    @Environment(\.accessibilityReduceMotion) var reduceMotion
    @Environment(\.dynamicTypeSize) var dynamicTypeSize
    
    var body: some View {
        NavigationStack {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // AI-Powered Summary Card
                    aiSummaryCard
                    
                    // Portfolio Performance Chart
                    portfolioPerformanceChart
                    
                    // Holdings Breakdown
                    holdingsBreakdown
                    
                    // AI Risk Assessment
                    if let riskAssessment = riskAssessment {
                        aiRiskAssessmentCard(riskAssessment)
                    }
                    
                    // Optimization Suggestions
                    if !optimizationSuggestions.isEmpty {
                        optimizationSuggestionsCard
                    }
                    
                    // Smart Actions
                    smartActionsCard
                }
                .padding()
            }
            .navigationTitle("Portfolio")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Analyze with AI", systemImage: "brain") {
                            analyzePortfolioWithAI()
                        }
                        
                        Button("Export Analysis", systemImage: "square.and.arrow.up") {
                            exportAnalysis()
                        }
                        
                        Button("Settings", systemImage: "gear") {
                            // Portfolio settings
                        }
                    } label: {
                        Image(systemName: "ellipsis.circle")
                            .font(.title3)
                    }
                }
            }
            .refreshable {
                await refreshPortfolioData()
            }
            .onAppear {
                loadPortfolioData()
            }
            .sheet(isPresented: $showingAIInsights) {
                AIInsightsDetailView(analysis: portfolioAnalysis)
            }
        }
    }
    
    // MARK: - AI Summary Card
    
    private var aiSummaryCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                HStack(spacing: 8) {
                    Image(systemName: "brain")
                        .font(.title3)
                        .foregroundColor(.purple)
                    
                    Text("AI Analysis")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                if isAnalyzing {
                    ProgressView()
                        .scaleEffect(0.8)
                } else {
                    Button("View Details") {
                        showingAIInsights = true
                    }
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                }
            }
            
            // AI Summary
            if !aiSummary.isEmpty {
                Text(aiSummary)
                    .font(.body)
                    .foregroundColor(.primary)
                    .lineLimit(nil)
            } else if isAnalyzing {
                HStack(spacing: 8) {
                    ProgressView()
                        .scaleEffect(0.7)
                    Text("Analyzing your portfolio with Apple Intelligence...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            } else {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Get AI-powered insights about your portfolio")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Button("Analyze Now") {
                        analyzePortfolioWithAI()
                    }
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        LinearGradient(
                            colors: [.purple, .blue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(20)
                }
            }
            
            // Performance Metrics
            if let analysis = portfolioAnalysis {
                performanceMetricsView(analysis)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    private func performanceMetricsView(_ analysis: PortfolioAnalysis) -> some View {
        HStack(spacing: 20) {
            MetricView(
                title: "Risk Score",
                value: "\(analysis.riskScore)/10",
                color: riskScoreColor(analysis.riskScore),
                icon: "shield.fill"
            )
            
            MetricView(
                title: "Diversification",
                value: "\(analysis.diversificationScore)/10",
                color: diversificationColor(analysis.diversificationScore),
                icon: "chart.pie.fill"
            )
            
            MetricView(
                title: "Performance",
                value: "\(analysis.performanceGrade)",
                color: performanceColor(analysis.performanceGrade),
                icon: "chart.line.uptrend.xyaxis"
            )
        }
    }
    
    // MARK: - Portfolio Performance Chart
    
    private var portfolioPerformanceChart: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header with timeframe selector
            HStack {
                Text("Performance")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Picker("Timeframe", selection: $selectedTimeframe) {
                    Text("1M").tag(TimeFrame.oneMonth)
                    Text("3M").tag(TimeFrame.threeMonths)
                    Text("1Y").tag(TimeFrame.oneYear)
                    Text("All").tag(TimeFrame.all)
                }
                .pickerStyle(.segmented)
                .frame(width: 200)
            }
            
            // Chart
            Chart {
                ForEach(portfolioManager.performanceData) { dataPoint in
                    LineMark(
                        x: .value("Date", dataPoint.date),
                        y: .value("Value", dataPoint.value)
                    )
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.purple, .blue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .lineStyle(StrokeStyle(lineWidth: 3))
                    
                    AreaMark(
                        x: .value("Date", dataPoint.date),
                        y: .value("Value", dataPoint.value)
                    )
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.purple.opacity(0.3), .blue.opacity(0.1)],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                }
            }
            .frame(height: 200)
            .chartXAxis {
                AxisMarks(values: .automatic) { _ in
                    AxisGridLine()
                    AxisTick()
                    AxisValueLabel(format: .dateTime.month(.abbreviated))
                }
            }
            .chartYAxis {
                AxisMarks(position: .trailing) { _ in
                    AxisGridLine()
                    AxisTick()
                    AxisValueLabel(format: .currency(code: "USD"))
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Holdings Breakdown
    
    private var holdingsBreakdown: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Holdings")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(portfolioManager.holdings.prefix(5)) { holding in
                HoldingRowView(holding: holding)
            }
            
            if portfolioManager.holdings.count > 5 {
                Button("View All Holdings") {
                    // Navigate to full holdings view
                }
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.purple)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - AI Risk Assessment Card
    
    private func aiRiskAssessmentCard(_ assessment: RiskAssessment) -> some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "shield.checkered")
                    .font(.title3)
                    .foregroundColor(.orange)
                
                Text("Risk Assessment")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            VStack(alignment: .leading, spacing: 12) {
                RiskFactorView(
                    title: "Market Risk",
                    level: assessment.marketRisk,
                    description: "Exposure to market volatility"
                )
                
                RiskFactorView(
                    title: "Concentration Risk",
                    level: assessment.concentrationRisk,
                    description: "Portfolio concentration in specific assets"
                )
                
                RiskFactorView(
                    title: "Sector Risk",
                    level: assessment.sectorRisk,
                    description: "Exposure to specific industry sectors"
                )
            }
            
            Text(assessment.summary)
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.top, 8)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Optimization Suggestions Card
    
    private var optimizationSuggestionsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .font(.title3)
                    .foregroundColor(.yellow)
                
                Text("AI Suggestions")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            ForEach(optimizationSuggestions.prefix(3)) { suggestion in
                OptimizationSuggestionView(suggestion: suggestion) {
                    implementSuggestion(suggestion)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Smart Actions Card
    
    private var smartActionsCard: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                SmartActionButton(
                    title: "Rebalance",
                    icon: "scale.3d",
                    color: .blue
                ) {
                    // Rebalance portfolio
                }
                
                SmartActionButton(
                    title: "Add Funds",
                    icon: "plus.circle.fill",
                    color: .green
                ) {
                    // Add funds
                }
                
                SmartActionButton(
                    title: "Tax Optimize",
                    icon: "doc.text.fill",
                    color: .orange
                ) {
                    // Tax optimization
                }
                
                SmartActionButton(
                    title: "Set Goals",
                    icon: "target",
                    color: .purple
                ) {
                    // Set investment goals
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - Actions
    
    private func analyzePortfolioWithAI() {
        isAnalyzing = true
        hapticManager.playSelection()
        
        Task {
            do {
                let portfolio = await portfolioManager.getCurrentPortfolio()
                let context = await getUserContext()
                
                let analysis = try await aiManager.analyzePortfolio(portfolio, context: context)
                
                await MainActor.run {
                    self.portfolioAnalysis = analysis
                    self.aiSummary = analysis.summary
                    self.riskAssessment = analysis.riskAssessment
                    self.optimizationSuggestions = analysis.optimizationSuggestions
                    self.isAnalyzing = false
                    
                    hapticManager.playSuccess()
                }
                
            } catch {
                await MainActor.run {
                    self.isAnalyzing = false
                    hapticManager.playError()
                    // Show error message
                }
            }
        }
    }
    
    private func refreshPortfolioData() async {
        await portfolioManager.refreshData()
        if portfolioAnalysis != nil {
            analyzePortfolioWithAI()
        }
    }
    
    private func loadPortfolioData() {
        Task {
            await portfolioManager.loadPortfolioData()
        }
    }
    
    private func exportAnalysis() {
        // Implement analysis export
        hapticManager.playSuccess()
    }
    
    private func implementSuggestion(_ suggestion: OptimizationSuggestion) {
        // Implement the AI suggestion
        hapticManager.playSuccess()
    }
    
    private func getUserContext() async -> UserContext {
        // Get user context for AI analysis
        return UserContext.mockContext()
    }
    
    // MARK: - Helper Methods
    
    private func riskScoreColor(_ score: Int) -> Color {
        switch score {
        case 1...3: return .green
        case 4...6: return .orange
        case 7...10: return .red
        default: return .gray
        }
    }
    
    private func diversificationColor(_ score: Int) -> Color {
        switch score {
        case 8...10: return .green
        case 5...7: return .orange
        case 1...4: return .red
        default: return .gray
        }
    }
    
    private func performanceColor(_ grade: String) -> Color {
        switch grade {
        case "A+", "A": return .green
        case "B+", "B": return .blue
        case "C+", "C": return .orange
        default: return .red
        }
    }
}

// MARK: - Supporting Views

struct MetricView: View {
    let title: String
    let value: String
    let color: Color
    let icon: String
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(color)
            
            Text(value)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

struct HoldingRowView: View {
    let holding: Holding
    
    var body: some View {
        HStack {
            // Symbol and name
            VStack(alignment: .leading, spacing: 2) {
                Text(holding.symbol)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(holding.name)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            // Value and change
            VStack(alignment: .trailing, spacing: 2) {
                Text("$\(holding.currentValue, specifier: "%.2f")")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                HStack(spacing: 2) {
                    Image(systemName: holding.changePercent >= 0 ? "arrow.up" : "arrow.down")
                        .font(.caption2)
                    
                    Text("\(abs(holding.changePercent), specifier: "%.2f")%")
                        .font(.caption)
                }
                .foregroundColor(holding.changePercent >= 0 ? .green : .red)
            }
        }
        .padding(.vertical, 4)
    }
}

struct RiskFactorView: View {
    let title: String
    let level: RiskLevel
    let description: String
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Text(level.displayName)
                .font(.caption)
                .fontWeight(.semibold)
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(level.color)
                .cornerRadius(8)
        }
    }
}

struct OptimizationSuggestionView: View {
    let suggestion: OptimizationSuggestion
    let onImplement: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(suggestion.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(suggestion.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
            
            Button("Apply") {
                onImplement()
            }
            .font(.caption)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color.blue)
            .cornerRadius(12)
        }
        .padding(.vertical, 4)
    }
}

struct SmartActionButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(color.opacity(0.1))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Supporting Types

enum TimeFrame: CaseIterable {
    case oneMonth, threeMonths, oneYear, all
    
    var displayName: String {
        switch self {
        case .oneMonth: return "1M"
        case .threeMonths: return "3M"
        case .oneYear: return "1Y"
        case .all: return "All"
        }
    }
}

struct RiskAssessment {
    let marketRisk: RiskLevel
    let concentrationRisk: RiskLevel
    let sectorRisk: RiskLevel
    let summary: String
}

enum RiskLevel: CaseIterable {
    case low, medium, high
    
    var displayName: String {
        switch self {
        case .low: return "Low"
        case .medium: return "Medium"
        case .high: return "High"
        }
    }
    
    var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        }
    }
}

struct OptimizationSuggestion: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let impact: String
    let priority: Priority
    
    enum Priority {
        case high, medium, low
    }
}

struct Holding: Identifiable {
    let id = UUID()
    let symbol: String
    let name: String
    let currentValue: Double
    let changePercent: Double
}

// MARK: - AI Insights Detail View

struct AIInsightsDetailView: View {
    let analysis: PortfolioAnalysis?
    @Environment(\.dismiss) var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    if let analysis = analysis {
                        Text("Detailed AI Analysis")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text(analysis.detailedSummary)
                            .font(.body)
                        
                        // Additional detailed analysis content
                    } else {
                        Text("No analysis available")
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
            }
            .navigationTitle("AI Insights")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}
