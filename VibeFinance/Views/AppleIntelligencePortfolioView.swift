//
//  AppleIntelligencePortfolioView.swift
//  VibeFinance - Apple Intelligence Portfolio Analysis
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import SwiftUI
import Charts

// MARK: - Apple Intelligence Portfolio Analysis

struct AppleIntelligencePortfolioView: View {
    @State private var analysisResults: PortfolioAnalysisResults?
    @State private var isAnalyzing = false
    @State private var selectedTimeframe: AnalysisTimeframe = .oneMonth
    @State private var showingDetailedAnalysis = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Apple Intelligence Header
                    appleIntelligenceHeader
                    
                    // Analysis Controls
                    analysisControls
                    
                    if isAnalyzing {
                        analysisLoadingView
                    } else if let results = analysisResults {
                        // Analysis Results
                        analysisResultsView(results)
                    } else {
                        // Start Analysis
                        startAnalysisView
                    }
                }
                .padding()
            }
            .background(VibeFinanceDesignSystem.Colors.primaryGradient)
            .navigationTitle("AI Portfolio Analysis")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                startAnalysis()
            }
        }
    }
    
    private var appleIntelligenceHeader: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                // Apple Intelligence Icon
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(
                            LinearGradient(
                                colors: [.blue, .purple, .pink],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 50, height: 50)
                    
                    Image(systemName: "chart.pie.fill")
                        .font(.title2)
                        .foregroundColor(.white)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Apple Intelligence")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("Portfolio Analysis")
                        .font(.subheadline)
                        .foregroundColor(.white.opacity(0.8))
                    
                    HStack(spacing: 4) {
                        Image(systemName: "lock.shield.fill")
                            .font(.caption2)
                            .foregroundColor(.green)
                        
                        Text("On-device processing")
                            .font(.caption2)
                            .foregroundColor(.green)
                    }
                }
                
                Spacer()
            }
            
            // AI Capabilities
            HStack(spacing: 8) {
                ForEach(aiCapabilities, id: \.self) { capability in
                    Text(capability)
                        .font(.caption2)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            Capsule()
                                .fill(Color.white.opacity(0.2))
                        )
                        .foregroundColor(.white)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.3))
                .blur(radius: 10)
        )
    }
    
    private var analysisControls: some View {
        VStack(spacing: 12) {
            Text("Analysis Timeframe")
                .font(.headline)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            Picker("Timeframe", selection: $selectedTimeframe) {
                ForEach(AnalysisTimeframe.allCases, id: \.self) { timeframe in
                    Text(timeframe.displayName).tag(timeframe)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .onChange(of: selectedTimeframe) { _ in
                startAnalysis()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.1))
        )
    }
    
    private var analysisLoadingView: some View {
        VStack(spacing: 16) {
            // AI Processing Animation
            ZStack {
                Circle()
                    .stroke(Color.blue.opacity(0.3), lineWidth: 4)
                    .frame(width: 80, height: 80)
                
                Circle()
                    .trim(from: 0, to: 0.7)
                    .stroke(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        style: StrokeStyle(lineWidth: 4, lineCap: .round)
                    )
                    .frame(width: 80, height: 80)
                    .rotationEffect(.degrees(isAnalyzing ? 360 : 0))
                    .animation(.linear(duration: 2).repeatForever(autoreverses: false), value: isAnalyzing)
                
                Image(systemName: "brain.head.profile")
                    .font(.title2)
                    .foregroundColor(.blue)
            }
            
            VStack(spacing: 8) {
                Text("Apple Intelligence Analyzing...")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text("Processing your portfolio with on-device AI")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            
            // Analysis Steps
            VStack(alignment: .leading, spacing: 8) {
                ForEach(analysisSteps.indices, id: \.self) { index in
                    HStack(spacing: 12) {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .opacity(index < 3 ? 1 : 0.3)
                        
                        Text(analysisSteps[index])
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(index < 3 ? 1 : 0.6))
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.1))
            )
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.3))
        )
    }
    
    private var startAnalysisView: some View {
        VStack(spacing: 16) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 60))
                .foregroundColor(.blue)
            
            Text("AI Portfolio Analysis")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text("Let Apple Intelligence analyze your portfolio performance, risk profile, and provide personalized recommendations.")
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
            
            Button(action: startAnalysis) {
                HStack(spacing: 8) {
                    Image(systemName: "play.circle.fill")
                    Text("Start Analysis")
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .padding()
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.blue)
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.3))
        )
    }
    
    private func analysisResultsView(_ results: PortfolioAnalysisResults) -> some View {
        VStack(spacing: 20) {
            // Overall Score
            overallScoreView(results)
            
            // Performance Chart
            performanceChartView(results)
            
            // Risk Analysis
            riskAnalysisView(results)
            
            // AI Recommendations
            aiRecommendationsView(results)
            
            // Detailed Analysis Button
            Button(action: { showingDetailedAnalysis = true }) {
                HStack {
                    Text("View Detailed Analysis")
                        .fontWeight(.semibold)
                    
                    Spacer()
                    
                    Image(systemName: "arrow.right.circle.fill")
                }
                .foregroundColor(.blue)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.white)
                )
            }
        }
    }
    
    private func overallScoreView(_ results: PortfolioAnalysisResults) -> some View {
        VStack(spacing: 12) {
            Text("Portfolio Health Score")
                .font(.headline)
                .foregroundColor(.white)
            
            ZStack {
                Circle()
                    .stroke(Color.white.opacity(0.3), lineWidth: 8)
                    .frame(width: 120, height: 120)
                
                Circle()
                    .trim(from: 0, to: CGFloat(results.healthScore) / 100)
                    .stroke(
                        LinearGradient(
                            colors: scoreColors(results.healthScore),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        style: StrokeStyle(lineWidth: 8, lineCap: .round)
                    )
                    .frame(width: 120, height: 120)
                    .rotationEffect(.degrees(-90))
                
                VStack(spacing: 2) {
                    Text("\(results.healthScore)")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("/ 100")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                }
            }
            
            Text(scoreDescription(results.healthScore))
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.8))
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.3))
        )
    }
    
    private func performanceChartView(_ results: PortfolioAnalysisResults) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Performance vs Benchmark")
                .font(.headline)
                .foregroundColor(.white)
            
            Chart(results.performanceData) { data in
                LineMark(
                    x: .value("Date", data.date),
                    y: .value("Portfolio", data.portfolioValue)
                )
                .foregroundStyle(.blue)
                .lineStyle(StrokeStyle(lineWidth: 3))
                
                LineMark(
                    x: .value("Date", data.date),
                    y: .value("Benchmark", data.benchmarkValue)
                )
                .foregroundStyle(.gray)
                .lineStyle(StrokeStyle(lineWidth: 2, dash: [5]))
            }
            .frame(height: 200)
            .chartBackground { chartProxy in
                Rectangle()
                    .fill(Color.white.opacity(0.05))
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.3))
        )
    }
    
    private func riskAnalysisView(_ results: PortfolioAnalysisResults) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Risk Analysis")
                .font(.headline)
                .foregroundColor(.white)
            
            VStack(spacing: 8) {
                riskMetricRow("Beta", value: String(format: "%.2f", results.beta), description: "Market sensitivity")
                riskMetricRow("Sharpe Ratio", value: String(format: "%.2f", results.sharpeRatio), description: "Risk-adjusted return")
                riskMetricRow("Max Drawdown", value: "\(String(format: "%.1f", results.maxDrawdown))%", description: "Largest loss period")
                riskMetricRow("Volatility", value: "\(String(format: "%.1f", results.volatility))%", description: "Price fluctuation")
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.3))
        )
    }
    
    private func riskMetricRow(_ title: String, value: String, description: String) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.6))
            }
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.blue)
        }
    }
    
    private func aiRecommendationsView(_ results: PortfolioAnalysisResults) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: "brain.head.profile")
                    .foregroundColor(.blue)
                
                Text("AI Recommendations")
                    .font(.headline)
                    .foregroundColor(.white)
            }
            
            VStack(spacing: 12) {
                ForEach(results.recommendations, id: \.id) { recommendation in
                    AIRecommendationCard(recommendation: recommendation)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.3))
        )
    }
    
    private let aiCapabilities = ["Risk Assessment", "Performance Analysis", "Rebalancing", "Optimization"]
    
    private let analysisSteps = [
        "Analyzing portfolio composition",
        "Calculating risk metrics",
        "Comparing to benchmarks",
        "Generating recommendations"
    ]
    
    private func startAnalysis() {
        isAnalyzing = true
        
        // Simulate AI analysis
        Task {
            try? await Task.sleep(nanoseconds: 3_000_000_000) // 3 seconds
            
            await MainActor.run {
                analysisResults = generateMockAnalysis()
                isAnalyzing = false
            }
        }
    }
    
    private func generateMockAnalysis() -> PortfolioAnalysisResults {
        // Generate mock analysis results
        let performanceData = generateMockPerformanceData()
        
        return PortfolioAnalysisResults(
            healthScore: 78,
            beta: 1.15,
            sharpeRatio: 1.42,
            maxDrawdown: -8.5,
            volatility: 12.3,
            performanceData: performanceData,
            recommendations: generateMockRecommendations()
        )
    }
    
    private func generateMockPerformanceData() -> [PerformanceDataPoint] {
        let calendar = Calendar.current
        let endDate = Date()
        let startDate = calendar.date(byAdding: .month, value: -selectedTimeframe.months, to: endDate)!
        
        var data: [PerformanceDataPoint] = []
        var currentDate = startDate
        var portfolioValue = 100000.0
        var benchmarkValue = 100000.0
        
        while currentDate <= endDate {
            portfolioValue *= (1 + Double.random(in: -0.02...0.03))
            benchmarkValue *= (1 + Double.random(in: -0.015...0.025))
            
            data.append(PerformanceDataPoint(
                date: currentDate,
                portfolioValue: portfolioValue,
                benchmarkValue: benchmarkValue
            ))
            
            currentDate = calendar.date(byAdding: .day, value: 7, to: currentDate)!
        }
        
        return data
    }
    
    private func generateMockRecommendations() -> [AIRecommendation] {
        return [
            AIRecommendation(
                id: UUID(),
                title: "Reduce Tech Concentration",
                description: "Your portfolio is 35% tech stocks. Consider reducing to 25-30% for better diversification.",
                priority: .high,
                impact: "Reduce risk by 15%"
            ),
            AIRecommendation(
                id: UUID(),
                title: "Add International Exposure",
                description: "Increase international diversification to 20% of portfolio for global growth opportunities.",
                priority: .medium,
                impact: "Improve diversification"
            ),
            AIRecommendation(
                id: UUID(),
                title: "Consider Bond Allocation",
                description: "Add 10-15% bonds to reduce overall portfolio volatility and provide stability.",
                priority: .low,
                impact: "Lower volatility"
            )
        ]
    }
    
    private func scoreColors(_ score: Int) -> [Color] {
        if score >= 80 {
            return [.green, .mint]
        } else if score >= 60 {
            return [.yellow, .orange]
        } else {
            return [.red, .pink]
        }
    }
    
    private func scoreDescription(_ score: Int) -> String {
        if score >= 80 {
            return "Excellent portfolio health with strong diversification and risk management"
        } else if score >= 60 {
            return "Good portfolio with room for improvement in risk management"
        } else {
            return "Portfolio needs attention - consider rebalancing and risk reduction"
        }
    }
}

// MARK: - Supporting Views

// Using AIRecommendationCard from PortfolioComponents.swift

// MARK: - Data Models

struct PortfolioAnalysisResults {
    let healthScore: Int
    let beta: Double
    let sharpeRatio: Double
    let maxDrawdown: Double
    let volatility: Double
    let performanceData: [PerformanceDataPoint]
    let recommendations: [AIRecommendation]
}

// Using PerformanceDataPoint from AnalyticsModels.swift

struct AIRecommendation {
    let id: UUID
    let title: String
    let description: String
    let priority: RecommendationPriority
    let impact: String
}

enum RecommendationPriority {
    case high, medium, low
    
    var displayName: String {
        switch self {
        case .high: return "High"
        case .medium: return "Medium"
        case .low: return "Low"
        }
    }
    
    var color: Color {
        switch self {
        case .high: return .red
        case .medium: return .orange
        case .low: return .blue
        }
    }
}

enum AnalysisTimeframe: CaseIterable {
    case oneWeek, oneMonth, threeMonths, sixMonths, oneYear
    
    var displayName: String {
        switch self {
        case .oneWeek: return "1W"
        case .oneMonth: return "1M"
        case .threeMonths: return "3M"
        case .sixMonths: return "6M"
        case .oneYear: return "1Y"
        }
    }
    
    var months: Int {
        switch self {
        case .oneWeek: return 0
        case .oneMonth: return 1
        case .threeMonths: return 3
        case .sixMonths: return 6
        case .oneYear: return 12
        }
    }
}
