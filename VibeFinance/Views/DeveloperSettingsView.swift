//
//  DeveloperSettingsView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct DeveloperSettingsView: View {
    @EnvironmentObject var authManager: AuthManager
    @Environment(\.dismiss) var dismiss
    @State private var showingMockUserPicker = false
    
    var body: some View {
        NavigationView {
            List {
                Section("Authentication") {
                    HStack {
                        VStack(alignment: .leading) {
                            Text("Mock Authentication")
                                .font(.headline)
                            Text("Skip real authentication during development")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        Spacer()
                        Toggle("", isOn: $authManager.useMockAuth)
                            .onChange(of: authManager.useMockAuth) {
                                authManager.toggleMockAuth()
                            }
                    }
                    
                    if authManager.useMockAuth {
                        Button(action: {
                            showingMockUserPicker = true
                        }) {
                            HStack {
                                Image(systemName: "person.circle.fill")
                                    .foregroundColor(.blue)
                                Text("Quick Login")
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                                    .font(.caption)
                            }
                        }
                        .foregroundColor(.primary)
                        
                        Button(action: {
                            authManager.quickDevLogin()
                        }) {
                            HStack {
                                Image(systemName: "bolt.fill")
                                    .foregroundColor(.orange)
                                Text("Instant Login (Dev User)")
                                Spacer()
                            }
                        }
                        .foregroundColor(.primary)
                    }
                }
                
                Section("Current State") {
                    HStack {
                        Text("Development Mode")
                        Spacer()
                        Text(authManager.isDevelopmentMode ? "Enabled" : "Disabled")
                            .foregroundColor(authManager.isDevelopmentMode ? .green : .red)
                    }
                    
                    HStack {
                        Text("Authentication Status")
                        Spacer()
                        Text(authManager.isAuthenticated ? "Authenticated" : "Not Authenticated")
                            .foregroundColor(authManager.isAuthenticated ? .green : .red)
                    }
                    
                    if let user = authManager.currentUser {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Current User")
                                .font(.headline)
                            Text("Email: \(user.email)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("Username: \(user.username)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("ID: \(user.id.uuidString)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                
                Section("Actions") {
                    if authManager.isAuthenticated {
                        Button(action: {
                            Task {
                                await authManager.signOut()
                            }
                        }) {
                            HStack {
                                Image(systemName: "rectangle.portrait.and.arrow.right")
                                    .foregroundColor(.red)
                                Text("Sign Out")
                            }
                        }
                        .foregroundColor(.red)
                    }
                    
                    Button(action: {
                        // Clear all UserDefaults for the app
                        if let bundleID = Bundle.main.bundleIdentifier {
                            UserDefaults.standard.removePersistentDomain(forName: bundleID)
                        }
                        
                        // Reset auth manager state
                        authManager.useMockAuth = false
                        authManager.isAuthenticated = false
                        authManager.currentUser = nil
                        authManager.errorMessage = nil
                    }) {
                        HStack {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                            Text("Reset All Settings")
                        }
                    }
                    .foregroundColor(.red)
                }
            }
            .navigationTitle("Developer Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingMockUserPicker) {
            MockUserPickerView()
        }
    }
}

struct MockUserPickerView: View {
    @EnvironmentObject var authManager: AuthManager
    @Environment(\.dismiss) var dismiss
    
    private let mockUsers = [
        ("Dev User", "<EMAIL>", "DevUser"),
        ("Test User", "<EMAIL>", "TestUser"),
        ("Demo User", "<EMAIL>", "DemoUser")
    ]
    
    var body: some View {
        NavigationView {
            List {
                ForEach(Array(mockUsers.enumerated()), id: \.offset) { index, user in
                    Button(action: {
                        authManager.mockSignIn(userIndex: index)
                        dismiss()
                    }) {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(user.2)
                                .font(.headline)
                                .foregroundColor(.primary)
                            Text(user.1)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.vertical, 4)
                    }
                }
            }
            .navigationTitle("Select Mock User")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#if DEBUG
struct DeveloperSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        DeveloperSettingsView()
            .environmentObject(AuthManager())
    }
}
#endif
