//
//  DashboardView.swift
//  VibeFinance - <PERSON>ett Inspired Financial Education
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

struct DashboardView: View {
    @State private var selectedTab = 0
    @State private var showingBuffettQuote = true
    @StateObject private var performanceManager = PerformanceManager.shared
    @StateObject private var batteryOptimizer = BatteryOptimizer()

    private let tabs = ["Feed", "Quests", "Squads"]
    
    var body: some View {
        NavigationView {
            ZStack {
                // Unified Warren Buffett inspired gradient background
                VibeFinanceDesignSystem.Colors.primaryGradient
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Warren Buffett Daily Wisdom Card
                    if showingBuffettQuote {
                        BuffettWisdomCard(
                            quote: "Rule No. 1: Never lose money. Rule No. 2: Never forget rule No. 1.",
                            context: "Investment Wisdom"
                        )
                        .padding(.horizontal, 16)
                        .padding(.top, 8)
                    }
                    
                    // Unified Tab Selector with Warren Buffett styling
                    VibeTabSelector(
                        selectedTab: $selectedTab,
                        tabs: tabs
                    )
                    .padding(.horizontal, 16)
                    .padding(.top, showingBuffettQuote ? 8 : 16)
                    
                    // Content based on selected tab
                    TabView(selection: $selectedTab) {
                        // Feed Tab
                        BuffettInspiredFeedView()
                            .tag(0)
                            // .optimizedForPerformance(batteryOptimizer) // Custom modifier not implemented

                        // Quests Tab
                        BuffettQuestsView()
                            .tag(1)
                            // .optimizedForPerformance(batteryOptimizer) // Custom modifier not implemented

                        // Squads Tab
                        BuffettSquadsView()
                            .tag(2)
                            .optimizedForPerformance(batteryOptimizer)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                    .animation(
                        .easeInOut(duration: 0.3), // Simplified animation
                        value: selectedTab
                    )
                }
            }
            .navigationTitle("Wealth Builder")
            .navigationBarTitleDisplayMode(.large)
            .preferredColorScheme(.dark)
            .onAppear {
                // performanceManager.startMonitoring()
            }
            .onDisappear {
                // performanceManager.stopMonitoring()
            }
            .onChange(of: selectedTab) { _ in
                // Track tab changes for analytics
                // performanceManager.trackUserInteraction("tab_change")
            }
        }
    }
}

// Using BuffettWisdomCard from TradingConfirmationViews.swift
/*struct BuffettWisdomCard: View {
    let onDismiss: () -> Void
    
    private let buffettQuotes = [
        "Rule No. 1: Never lose money. Rule No. 2: Never forget rule No. 1.",
        "Time is the friend of the wonderful company, the enemy of the mediocre.",
        "Price is what you pay. Value is what you get.",
        "The stock market is designed to transfer money from the Active to the Patient.",
        "Someone's sitting in the shade today because someone planted a tree a long time ago.",
        "Risk comes from not knowing what you're doing.",
        "It's far better to buy a wonderful company at a fair price than a fair company at a wonderful price."
    ]
    
    @State private var currentQuote = ""
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: "lightbulb.fill")
                .foregroundColor(.yellow)
                .font(.title3)

            VStack(alignment: .leading, spacing: 4) {
                Text("💡 Warren's Daily Wisdom")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                Text("\"\(currentQuote)\"")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.9))
                    .lineLimit(2)
                    .italic()
            }

            Spacer()

            Button(action: onDismiss) {
                Image(systemName: "xmark.circle.fill")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.6))
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.15),
                            Color.white.opacity(0.05)
                        ],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
        )
        .onAppear {
            currentQuote = buffettQuotes.randomElement() ?? buffettQuotes[0]
        }
    }
}*/

// MARK: - Glassmorphic Tab Selector
struct GlassmorphicTabSelector: View {
    @Binding var selectedTab: Int
    let tabs: [String]
    
    var body: some View {
        ZStack {
            // Background
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)
            
            HStack(spacing: 0) {
                ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            selectedTab = index
                        }
                    }) {
                        ZStack {
                            if selectedTab == index {
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(
                                        LinearGradient(
                                            colors: [
                                                Color.yellow.opacity(0.8),
                                                Color.orange.opacity(0.6)
                                            ],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .shadow(color: .yellow.opacity(0.3), radius: 8, x: 0, y: 4)
                            }
                            
                            Text(tab)
                                .font(.subheadline)
                                .fontWeight(selectedTab == index ? .bold : .medium)
                                .foregroundColor(selectedTab == index ? .black : .white.opacity(0.8))
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(4)
        }
        .frame(height: 52)
    }
}

// MARK: - Preview
#Preview {
    DashboardView()
        .preferredColorScheme(.dark)
}
