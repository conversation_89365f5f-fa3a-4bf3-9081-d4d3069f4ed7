//
//  DashboardView.swift
//  VibeFinance - <PERSON> Inspired Financial Education
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

struct DashboardView: View {
    @State private var selectedTab = 0
    @State private var showingBuffettQuote = true
    
    private let tabs = ["Feed", "Quests", "Squads"]
    
    var body: some View {
        NavigationView {
            ZStack {
                // Warren Buffett inspired gradient background
                LinearGradient(
                    colors: [
                        Color(red: 0.1, green: 0.2, blue: 0.4),
                        Color(red: 0.2, green: 0.3, blue: 0.5),
                        Color(red: 0.1, green: 0.25, blue: 0.45)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Warren Buffett Daily Wisdom Card
                    if showingBuffettQuote {
                        BuffettWisdomCard {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showingBuffettQuote = false
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.top, 8)
                    }
                    
                    // Glassmorphic Tab Selector
                    GlassmorphicTabSelector(
                        selectedTab: $selectedTab,
                        tabs: tabs
                    )
                    .padding(.horizontal, 16)
                    .padding(.top, showingBuffettQuote ? 8 : 16)
                    
                    // Content based on selected tab
                    TabView(selection: $selectedTab) {
                        // Feed Tab
                        BuffettInspiredFeedView()
                            .tag(0)
                        
                        // Quests Tab
                        BuffettQuestsView()
                            .tag(1)
                        
                        // Squads Tab
                        BuffettSquadsView()
                            .tag(2)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                }
            }
            .navigationTitle("Wealth Builder")
            .navigationBarTitleDisplayMode(.large)
            .preferredColorScheme(.dark)
        }
    }
}

// MARK: - Warren Buffett Wisdom Card
struct BuffettWisdomCard: View {
    let onDismiss: () -> Void
    
    private let buffettQuotes = [
        "Rule No. 1: Never lose money. Rule No. 2: Never forget rule No. 1.",
        "Time is the friend of the wonderful company, the enemy of the mediocre.",
        "Price is what you pay. Value is what you get.",
        "The stock market is designed to transfer money from the Active to the Patient.",
        "Someone's sitting in the shade today because someone planted a tree a long time ago.",
        "Risk comes from not knowing what you're doing.",
        "It's far better to buy a wonderful company at a fair price than a fair company at a wonderful price."
    ]
    
    @State private var currentQuote = ""
    
    var body: some View {
        ZStack {
            // Glassmorphic background
            RoundedRectangle(cornerRadius: 20)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.25),
                            Color.white.opacity(0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)
            
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("💡 Warren's Daily Wisdom")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text("Building wealth through timeless principles")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    
                    Spacer()
                    
                    Button(action: onDismiss) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.white.opacity(0.6))
                    }
                }
                
                Text("\"\(currentQuote)\"")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .italic()
                    .lineLimit(3)
                
                HStack {
                    Text("— Warren Buffett")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.yellow.opacity(0.9))
                    
                    Spacer()
                    
                    Text("Oracle of Omaha")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            .padding(16)
        }
        .onAppear {
            currentQuote = buffettQuotes.randomElement() ?? buffettQuotes[0]
        }
    }
}

// MARK: - Glassmorphic Tab Selector
struct GlassmorphicTabSelector: View {
    @Binding var selectedTab: Int
    let tabs: [String]
    
    var body: some View {
        ZStack {
            // Background
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.2),
                            Color.white.opacity(0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)
            
            HStack(spacing: 0) {
                ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            selectedTab = index
                        }
                    }) {
                        ZStack {
                            if selectedTab == index {
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(
                                        LinearGradient(
                                            colors: [
                                                Color.yellow.opacity(0.8),
                                                Color.orange.opacity(0.6)
                                            ],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                                    .shadow(color: .yellow.opacity(0.3), radius: 8, x: 0, y: 4)
                            }
                            
                            Text(tab)
                                .font(.subheadline)
                                .fontWeight(selectedTab == index ? .bold : .medium)
                                .foregroundColor(selectedTab == index ? .black : .white.opacity(0.8))
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 44)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(4)
        }
        .frame(height: 52)
    }
}

// MARK: - Preview
#Preview {
    DashboardView()
        .preferredColorScheme(.dark)
}
