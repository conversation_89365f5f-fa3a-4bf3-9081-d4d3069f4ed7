//
//  BrokerageConnectionSheet.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct BrokerageConnectionSheet: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var realTradingManager: RealTradingManager
    @State private var apiKey = ""
    @State private var secretKey = ""
    @State private var isPaperTrading = true
    @State private var isConnecting = false
    @State private var showingError = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 16) {
                        Image(systemName: "link.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.blue)
                        
                        Text("Connect Alpaca Account")
                            .font(.title)
                            .fontWeight(.bold)
                        
                        Text("Enter your Alpaca API credentials to start trading")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    
                    // Connection Form
                    VStack(spacing: 20) {
                        // API Key Input
                        VStack(alignment: .leading, spacing: 8) {
                            Text("API Key")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            TextField("Enter your API key", text: $apiKey)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .autocapitalization(.none)
                                .disableAutocorrection(true)
                        }
                        
                        // Secret Key Input
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Secret Key")
                                .font(.headline)
                                .fontWeight(.semibold)
                            
                            SecureField("Enter your secret key", text: $secretKey)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                        }
                        
                        // Paper Trading Toggle
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text("Paper Trading")
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                    
                                    Text("Practice with virtual money (recommended for beginners)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                Spacer()
                                
                                Toggle("", isOn: $isPaperTrading)
                                    .toggleStyle(SwitchToggleStyle(tint: .green))
                            }
                            
                            if !isPaperTrading {
                                HStack {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(.orange)
                                    
                                    Text("Live trading uses real money. Ensure you understand the risks.")
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                }
                                .padding(.top, 4)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemGray6))
                        )
                    }
                    
                    // Security Information
                    VStack(spacing: 16) {
                        Text("Security & Privacy")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        VStack(spacing: 12) {
                            SecurityInfoRow(
                                icon: "lock.shield.fill",
                                title: "Encrypted Storage",
                                description: "Your credentials are encrypted and stored securely"
                            )
                            
                            SecurityInfoRow(
                                icon: "eye.slash.fill",
                                title: "Limited Access",
                                description: "We only access trading functions, never personal data"
                            )
                            
                            SecurityInfoRow(
                                icon: "checkmark.seal.fill",
                                title: "SEC Regulated",
                                description: "Alpaca is a registered broker-dealer with the SEC"
                            )
                        }
                    }
                    
                    // How to Get API Keys
                    VStack(alignment: .leading, spacing: 12) {
                        Text("How to Get API Keys")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            InstructionStep(number: 1, text: "Sign up for an Alpaca account at alpaca.markets")
                            InstructionStep(number: 2, text: "Complete account verification")
                            InstructionStep(number: 3, text: "Go to 'API Keys' in your dashboard")
                            InstructionStep(number: 4, text: "Generate new API keys")
                            InstructionStep(number: 5, text: "Copy and paste them here")
                        }
                        
                        Button("Open Alpaca Website") {
                            if let url = URL(string: "https://alpaca.markets") {
                                UIApplication.shared.open(url)
                            }
                        }
                        .font(.subheadline)
                        .foregroundColor(.blue)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
                    
                    // Connect Button
                    Button(action: connectAccount) {
                        HStack {
                            if isConnecting {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .foregroundColor(.white)
                            }
                            Text(isConnecting ? "Connecting..." : "Connect Account")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(canConnect ? Color.blue : Color.gray)
                        )
                    }
                    .disabled(!canConnect || isConnecting)
                    
                    // Disconnect Button (if already connected)
                    if realTradingManager.isConnected {
                        Button("Disconnect Account") {
                            Task {
                                await realTradingManager.disconnectBrokerageAccount()
                                dismiss()
                            }
                        }
                        .font(.subheadline)
                        .foregroundColor(.red)
                    }
                }
                .padding()
            }
            .navigationTitle("Brokerage Account")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .alert("Connection Error", isPresented: $showingError) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }
        }
    }
    
    private var canConnect: Bool {
        !apiKey.isEmpty && !secretKey.isEmpty
    }
    
    private func connectAccount() {
        isConnecting = true
        
        Task {
            await realTradingManager.connectBrokerageAccount(
                apiKey: apiKey,
                secretKey: secretKey,
                isPaper: isPaperTrading
            )
            
            await MainActor.run {
                self.isConnecting = false
                
                if realTradingManager.isConnected {
                    dismiss()
                } else if let error = realTradingManager.errorMessage {
                    self.errorMessage = error
                    self.showingError = true
                }
            }
        }
    }
}

// MARK: - Security Info Row
struct SecurityInfoRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.green)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

// MARK: - Instruction Step
struct InstructionStep: View {
    let number: Int
    let text: String
    
    var body: some View {
        HStack(spacing: 12) {
            Text("\(number)")
                .font(.caption)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 20, height: 20)
                .background(
                    Circle()
                        .fill(Color.blue)
                )
            
            Text(text)
                .font(.caption)
                .foregroundColor(.primary)
            
            Spacer()
        }
    }
}

// MARK: - Real Trade Sheet
struct RealTradeSheet: View {
    let stock: StockPrice
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var realTradingManager: RealTradingManager
    
    @State private var orderSide: OrderSide = .buy
    @State private var orderType: OrderType = .market
    @State private var quantity: Double = 1
    @State private var limitPrice: Double = 0
    @State private var timeInForce: TimeInForce = .gtc
    @State private var isPlacingOrder = false
    @State private var showingConfirmation = false
    @State private var validationResult: OrderValidationResult = .valid
    
    private var estimatedCost: Double {
        let price = orderType == .limit ? limitPrice : stock.price
        return quantity * price
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Stock Header
                    StockTradeHeader(stock: stock)
                    
                    // Order Configuration
                    VStack(spacing: 20) {
                        // Order Side
                        OrderSideSelector(selectedSide: $orderSide)
                        
                        // Order Type
                        OrderTypeSelector(selectedType: $orderType)
                        
                        // Quantity
                        QuantitySelector(
                            quantity: $quantity,
                            stock: stock,
                            orderSide: orderSide,
                            availableShares: getAvailableShares()
                        )
                        
                        // Limit Price (if limit order)
                        if orderType == .limit {
                            LimitPriceSelector(
                                limitPrice: $limitPrice,
                                currentPrice: stock.price
                            )
                        }
                        
                        // Time in Force
                        TimeInForceSelector(selectedTIF: $timeInForce)
                    }
                    
                    // Order Summary
                    OrderSummarySection(
                        stock: stock,
                        orderSide: orderSide,
                        orderType: orderType,
                        quantity: quantity,
                        limitPrice: orderType == .limit ? limitPrice : nil,
                        estimatedCost: estimatedCost
                    )
                    
                    // Validation Messages
                    if let message = validationResult.message {
                        ValidationMessageView(
                            message: message,
                            isError: !validationResult.isValid
                        )
                    }
                    
                    // Place Order Button
                    Button(action: {
                        showingConfirmation = true
                    }) {
                        HStack {
                            if isPlacingOrder {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .foregroundColor(.white)
                            }
                            Text("Place \(orderSide.displayName) Order")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(validationResult.isValid ? (orderSide == .buy ? Color.green : Color.red) : Color.gray)
                        )
                    }
                    .disabled(!validationResult.isValid || isPlacingOrder)
                }
                .padding()
            }
            .navigationTitle("Trade \(stock.symbol)")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .alert("Confirm Order", isPresented: $showingConfirmation) {
                Button("Cancel", role: .cancel) { }
                Button("Place Order", role: .destructive) {
                    placeOrder()
                }
            } message: {
                Text("Are you sure you want to place this \(orderSide.displayName.lowercased()) order for \(Int(quantity)) shares of \(stock.symbol)?")
            }
            .onChange(of: quantity) { _ in validateOrder() }
            .onChange(of: limitPrice) { _ in validateOrder() }
            .onChange(of: orderSide) { _ in validateOrder() }
            .onAppear {
                limitPrice = stock.price
                validateOrder()
            }
        }
    }
    
    private func getAvailableShares() -> Double {
        if orderSide == .sell {
            return realTradingManager.getHolding(for: stock.symbol)?.quantity ?? 0
        }
        return Double(Int((realTradingManager.accountInfo?.buyingPower ?? 0) / stock.price))
    }
    
    private func validateOrder() {
        let price = orderType == .limit ? limitPrice : stock.price
        validationResult = realTradingManager.validateOrder(
            symbol: stock.symbol,
            quantity: quantity,
            side: orderSide,
            price: price
        )
    }
    
    private func placeOrder() {
        isPlacingOrder = true
        
        Task {
            let success: Bool
            
            if orderType == .market {
                success = await realTradingManager.placeMarketOrder(
                    symbol: stock.symbol,
                    quantity: quantity,
                    side: orderSide
                )
            } else {
                success = await realTradingManager.placeLimitOrder(
                    symbol: stock.symbol,
                    quantity: quantity,
                    side: orderSide,
                    limitPrice: limitPrice
                )
            }
            
            await MainActor.run {
                self.isPlacingOrder = false
                if success {
                    dismiss()
                }
            }
        }
    }
}

// MARK: - Supporting Trade Components
struct StockTradeHeader: View {
    let stock: StockPrice

    var body: some View {
        VStack(spacing: 16) {
            Circle()
                .fill(Color.purple.opacity(0.1))
                .frame(width: 80, height: 80)
                .overlay(
                    Text(stock.symbol.prefix(2))
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                )

            VStack(spacing: 8) {
                Text(stock.symbol)
                    .font(.title)
                    .fontWeight(.bold)

                Text("$\(stock.price, specifier: "%.2f")")
                    .font(.largeTitle)
                    .fontWeight(.bold)

                HStack(spacing: 4) {
                    Image(systemName: stock.change >= 0 ? "arrow.up.right" : "arrow.down.right")
                        .font(.caption)
                    Text("$\(abs(stock.change), specifier: "%.2f") (\(stock.changePercent >= 0 ? "+" : "")\(stock.changePercent, specifier: "%.2f")%)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(stock.change >= 0 ? .green : .red)
            }
        }
    }
}

struct OrderSideSelector: View {
    @Binding var selectedSide: OrderSide

    var body: some View {
        HStack(spacing: 0) {
            ForEach(OrderSide.allCases, id: \.self) { side in
                Button(action: {
                    selectedSide = side
                }) {
                    Text(side.displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(selectedSide == side ? .white : .primary)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedSide == side ? (side == .buy ? Color.green : Color.red) : Color(.systemGray6))
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}

struct OrderTypeSelector: View {
    @Binding var selectedType: OrderType

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Order Type")
                .font(.headline)
                .fontWeight(.semibold)

            HStack(spacing: 12) {
                ForEach([OrderType.market, OrderType.limit], id: \.self) { type in
                    Button(action: {
                        selectedType = type
                    }) {
                        Text(type.displayName)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(selectedType == type ? .white : .primary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(selectedType == type ? Color.purple : Color(.systemGray6))
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
}

struct QuantitySelector: View {
    @Binding var quantity: Double
    let stock: StockPrice
    let orderSide: OrderSide
    let availableShares: Double

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Quantity")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                if orderSide == .sell {
                    Text("Available: \(Int(availableShares))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            HStack(spacing: 16) {
                Button(action: {
                    if quantity > 1 {
                        quantity -= 1
                    }
                }) {
                    Image(systemName: "minus.circle.fill")
                        .font(.title2)
                        .foregroundColor(quantity > 1 ? .purple : .gray)
                }
                .disabled(quantity <= 1)

                VStack(spacing: 4) {
                    Text("\(Int(quantity))")
                        .font(.title)
                        .fontWeight(.bold)
                    Text("shares")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(minWidth: 80)

                Button(action: {
                    if quantity < availableShares {
                        quantity += 1
                    }
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                        .foregroundColor(quantity < availableShares ? .purple : .gray)
                }
                .disabled(quantity >= availableShares)
            }
            .frame(maxWidth: .infinity)
        }
    }
}

struct LimitPriceSelector: View {
    @Binding var limitPrice: Double
    let currentPrice: Double

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Limit Price")
                .font(.headline)
                .fontWeight(.semibold)

            HStack {
                Text("$")
                    .font(.title2)
                    .fontWeight(.semibold)

                TextField("0.00", value: $limitPrice, format: .number.precision(.fractionLength(2)))
                    .font(.title2)
                    .fontWeight(.semibold)
                    .keyboardType(.decimalPad)

                Spacer()

                VStack(alignment: .trailing, spacing: 2) {
                    Text("Current: $\(currentPrice, specifier: "%.2f")")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
    }
}

struct TimeInForceSelector: View {
    @Binding var selectedTIF: TimeInForce

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Time in Force")
                .font(.headline)
                .fontWeight(.semibold)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(TimeInForce.allCases, id: \.self) { tif in
                        Button(action: {
                            selectedTIF = tif
                        }) {
                            Text(tif.displayName)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(selectedTIF == tif ? .white : .primary)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(selectedTIF == tif ? Color.purple : Color(.systemGray6))
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}

struct OrderSummarySection: View {
    let stock: StockPrice
    let orderSide: OrderSide
    let orderType: OrderType
    let quantity: Double
    let limitPrice: Double?
    let estimatedCost: Double

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Order Summary")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: 8) {
                SummaryRow(title: "Symbol", value: stock.symbol)
                SummaryRow(title: "Side", value: orderSide.displayName)
                SummaryRow(title: "Type", value: orderType.displayName)
                SummaryRow(title: "Quantity", value: "\(Int(quantity)) shares")

                if let limitPrice = limitPrice {
                    SummaryRow(title: "Limit Price", value: String(format: "$%.2f", limitPrice))
                }

                Divider()

                SummaryRow(
                    title: "Estimated \(orderSide == .buy ? "Cost" : "Proceeds")",
                    value: String(format: "$%.2f", estimatedCost),
                    isTotal: true
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

struct SummaryRow: View {
    let title: String
    let value: String
    var isTotal: Bool = false

    var body: some View {
        HStack {
            Text(title)
                .font(isTotal ? .headline : .subheadline)
                .fontWeight(isTotal ? .semibold : .regular)
                .foregroundColor(isTotal ? .primary : .secondary)

            Spacer()

            Text(value)
                .font(isTotal ? .headline : .subheadline)
                .fontWeight(isTotal ? .bold : .medium)
        }
    }
}

struct ValidationMessageView: View {
    let message: String
    let isError: Bool

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: isError ? "exclamationmark.triangle.fill" : "info.circle.fill")
                .foregroundColor(isError ? .red : .orange)

            Text(message)
                .font(.caption)
                .foregroundColor(isError ? .red : .orange)

            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill((isError ? Color.red : Color.orange).opacity(0.1))
        )
    }
}

struct RealHoldingDetailView: View {
    let holding: RealHolding
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Text("Holding details for \(holding.symbol)")
                        .font(.title)
                        .fontWeight(.bold)

                    // Add detailed holding information here
                }
                .padding()
            }
            .navigationTitle(holding.symbol)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct SubscriptionUpgradeView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Upgrade to Pro")
                    .font(.title)
                    .fontWeight(.bold)

                Text("Get access to real trading and more features")
                    .font(.body)
                    .foregroundColor(.secondary)

                Spacer()
            }
            .padding()
            .navigationTitle("Upgrade")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    BrokerageConnectionSheet()
        .environmentObject(RealTradingManager())
}
