//
//  PreferencesSetupView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct PreferencesSetupView: View {
    @EnvironmentObject var userManager: UserManager
    @Environment(\.dismiss) var dismiss
    
    @State private var currentStep = 0
    @State private var selectedInterests: Set<String> = []
    @State private var selectedGoals: Set<String> = []
    @State private var selectedRiskTolerance: RiskTolerance = .moderate
    @State private var notificationsEnabled = true
    @State private var isLoading = false
    
    private let totalSteps = 4
    
    var body: some View {
        NavigationView {
            ZStack {
                // Background gradient
                LinearGradient(
                    colors: [
                        Color.purple.opacity(0.1),
                        Color.pink.opacity(0.05)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Progress bar
                    ProgressView(value: Double(currentStep + 1), total: Double(totalSteps))
                        .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                        .padding(.horizontal, 20)
                        .padding(.top, 10)
                    
                    // Content
                    TabView(selection: $currentStep) {
                        InterestsStepView(selectedInterests: $selectedInterests)
                            .tag(0)
                        
                        GoalsStepView(selectedGoals: $selectedGoals)
                            .tag(1)
                        
                        RiskToleranceStepView(selectedRiskTolerance: $selectedRiskTolerance)
                            .tag(2)
                        
                        NotificationsStepView(notificationsEnabled: $notificationsEnabled)
                            .tag(3)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                    .animation(.easeInOut, value: currentStep)
                    
                    // Navigation buttons
                    HStack(spacing: 16) {
                        if currentStep > 0 {
                            Button("Back") {
                                withAnimation {
                                    currentStep -= 1
                                }
                            }
                            .foregroundColor(.purple)
                        }
                        
                        Spacer()
                        
                        Button(currentStep < totalSteps - 1 ? "Next" : "Complete Setup") {
                            if currentStep < totalSteps - 1 {
                                withAnimation {
                                    currentStep += 1
                                }
                            } else {
                                completeSetup()
                            }
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 24)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(isNextButtonEnabled ? Color.purple : Color.gray)
                        )
                        .disabled(!isNextButtonEnabled || isLoading)
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 30)
                }
            }
            .navigationTitle("Setup Your Vibe")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Skip") {
                        completeSetup()
                    }
                    .foregroundColor(.purple)
                }
            }
        }
    }
    
    private var isNextButtonEnabled: Bool {
        switch currentStep {
        case 0: return !selectedInterests.isEmpty
        case 1: return !selectedGoals.isEmpty
        case 2: return true // Risk tolerance always has a default
        case 3: return true // Notifications step is always valid
        default: return false
        }
    }
    
    private func completeSetup() {
        isLoading = true
        
        Task {
            var preferences = UserPreferences()
            preferences.interests = Array(selectedInterests)
            preferences.goals = Array(selectedGoals)
            preferences.riskTolerance = selectedRiskTolerance
            preferences.notificationsEnabled = notificationsEnabled
            
            await userManager.updatePreferences(preferences)
            
            await MainActor.run {
                self.isLoading = false
                self.dismiss()
            }
        }
    }
}

struct InterestsStepView: View {
    @Binding var selectedInterests: Set<String>
    
    private let interests = UserManager.availableInterests
    
    var body: some View {
        VStack(spacing: 24) {
            VStack(spacing: 12) {
                Text("🎯")
                    .font(.system(size: 60))
                
                Text("What interests you?")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("Select topics you'd like to see in your feed")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.top, 40)
            
            ScrollView {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 12) {
                    ForEach(interests, id: \.self) { interest in
                        InterestChip(
                            title: interest,
                            isSelected: selectedInterests.contains(interest)
                        ) {
                            if selectedInterests.contains(interest) {
                                selectedInterests.remove(interest)
                            } else {
                                selectedInterests.insert(interest)
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
            
            Spacer()
        }
    }
}

struct GoalsStepView: View {
    @Binding var selectedGoals: Set<String>
    
    private let goals = UserManager.availableGoals
    
    var body: some View {
        VStack(spacing: 24) {
            VStack(spacing: 12) {
                Text("🚀")
                    .font(.system(size: 60))
                
                Text("What are your goals?")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("Choose what you want to achieve financially")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.top, 40)
            
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(goals, id: \.self) { goal in
                        GoalRow(
                            title: goal,
                            isSelected: selectedGoals.contains(goal)
                        ) {
                            if selectedGoals.contains(goal) {
                                selectedGoals.remove(goal)
                            } else {
                                selectedGoals.insert(goal)
                            }
                        }
                    }
                }
                .padding(.horizontal, 20)
            }
            
            Spacer()
        }
    }
}

struct RiskToleranceStepView: View {
    @Binding var selectedRiskTolerance: RiskTolerance
    
    var body: some View {
        VStack(spacing: 24) {
            VStack(spacing: 12) {
                Text("⚖️")
                    .font(.system(size: 60))
                
                Text("What's your risk style?")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("This helps us suggest investments that match your comfort level")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            .padding(.top, 40)
            
            VStack(spacing: 16) {
                ForEach(RiskTolerance.allCases, id: \.self) { risk in
                    RiskToleranceCard(
                        riskTolerance: risk,
                        isSelected: selectedRiskTolerance == risk
                    ) {
                        selectedRiskTolerance = risk
                    }
                }
            }
            .padding(.horizontal, 20)
            
            Spacer()
        }
    }
}

struct NotificationsStepView: View {
    @Binding var notificationsEnabled: Bool
    
    var body: some View {
        VStack(spacing: 24) {
            VStack(spacing: 12) {
                Text("🔔")
                    .font(.system(size: 60))
                
                Text("Stay in the loop!")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("Get notified about new quests, market updates, and squad activities")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
            .padding(.top, 40)
            
            VStack(spacing: 20) {
                NotificationToggle(
                    title: "Daily Quests",
                    description: "Get reminded about new daily challenges",
                    isEnabled: $notificationsEnabled
                )
                
                NotificationToggle(
                    title: "Market Updates",
                    description: "Important news about your interests",
                    isEnabled: $notificationsEnabled
                )
                
                NotificationToggle(
                    title: "Squad Activities",
                    description: "When your squad makes investment decisions",
                    isEnabled: $notificationsEnabled
                )
            }
            .padding(.horizontal, 20)
            
            Spacer()
        }
    }
}

// MARK: - Supporting Views

struct InterestChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(isSelected ? .white : .purple)
                .padding(.horizontal, 16)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color.purple : Color.purple.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color.purple, lineWidth: isSelected ? 0 : 1)
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct GoalRow: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .font(.body)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .purple : .gray)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.purple.opacity(0.1) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.purple : Color.gray.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct RiskToleranceCard: View {
    let riskTolerance: RiskTolerance
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 8) {
                Text(riskTolerance.displayName)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(riskTolerance.description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.purple.opacity(0.1) : Color.clear)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(isSelected ? Color.purple : Color.gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct NotificationToggle: View {
    let title: String
    let description: String
    @Binding var isEnabled: Bool
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $isEnabled)
                .toggleStyle(SwitchToggleStyle(tint: .purple))
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.gray.opacity(0.1))
        )
    }
}

#Preview {
    PreferencesSetupView()
        .environmentObject(UserManager())
}
