//
//  SimulatorTradeView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct SimulatorTradeView: View {
    let stock: StockPrice
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var simulatorManager: SimulatorManager
    
    @State private var tradeType: TradeType = .buy
    @State private var shares: Double = 1
    @State private var isExecutingTrade = false
    @State private var showingConfirmation = false
    
    private var totalCost: Double {
        shares * stock.price
    }
    
    private var canExecuteTrade: Bool {
        if tradeType == .buy {
            return totalCost <= simulatorManager.virtualBalance && shares > 0
        } else {
            let holding = simulatorManager.holdings.first { $0.symbol == stock.symbol }
            return (holding?.shares ?? 0) >= shares && shares > 0
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Stock Header
                    TradeStockHeader(stock: stock)
                    
                    // Trade Type Selector
                    TradeTypeSelector(selectedType: $tradeType)
                    
                    // Shares Input
                    SharesInputSection(
                        shares: $shares,
                        stock: stock,
                        tradeType: tradeType,
                        availableShares: getAvailableShares()
                    )
                    
                    // Order Summary
                    OrderSummarySection(
                        stock: stock,
                        shares: shares,
                        tradeType: tradeType,
                        totalCost: totalCost
                    )
                    
                    // Account Balance
                    AccountBalanceSection(
                        virtualBalance: simulatorManager.virtualBalance,
                        totalCost: totalCost,
                        tradeType: tradeType
                    )
                    
                    // Execute Trade Button
                    Button(action: {
                        showingConfirmation = true
                    }) {
                        HStack {
                            if isExecutingTrade {
                                ProgressView()
                                    .scaleEffect(0.8)
                                    .foregroundColor(.white)
                            }
                            Text(tradeType == .buy ? "Buy \(stock.symbol)" : "Sell \(stock.symbol)")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(canExecuteTrade ? (tradeType == .buy ? Color.green : Color.red) : Color.gray)
                        )
                    }
                    .disabled(!canExecuteTrade || isExecutingTrade)
                    .padding(.horizontal)
                }
                .padding()
            }
            .navigationTitle("Trade \(stock.symbol)")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden()
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
            .alert("Confirm Trade", isPresented: $showingConfirmation) {
                Button("Cancel", role: .cancel) { }
                Button(tradeType == .buy ? "Buy" : "Sell", role: .destructive) {
                    executeTrade()
                }
            } message: {
                Text("Are you sure you want to \(tradeType.rawValue) \(Int(shares)) shares of \(stock.symbol) for $\(totalCost, specifier: "%.2f")?")
            }
        }
    }
    
    private func getAvailableShares() -> Double {
        if tradeType == .sell {
            return simulatorManager.holdings.first { $0.symbol == stock.symbol }?.shares ?? 0
        }
        return Double(Int(simulatorManager.virtualBalance / stock.price))
    }
    
    private func executeTrade() {
        isExecutingTrade = true
        
        Task {
            let success: Bool
            
            if tradeType == .buy {
                success = await simulatorManager.buyStock(
                    symbol: stock.symbol,
                    shares: shares,
                    price: stock.price
                )
            } else {
                success = await simulatorManager.sellStock(
                    symbol: stock.symbol,
                    shares: shares,
                    price: stock.price
                )
            }
            
            await MainActor.run {
                self.isExecutingTrade = false
                if success {
                    self.dismiss()
                }
            }
        }
    }
}

// MARK: - Trade Stock Header
struct TradeStockHeader: View {
    let stock: StockPrice
    
    var body: some View {
        VStack(spacing: 16) {
            // Stock Icon
            Circle()
                .fill(Color.purple.opacity(0.1))
                .frame(width: 80, height: 80)
                .overlay(
                    Text(stock.symbol.prefix(2))
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.purple)
                )
            
            // Stock Info
            VStack(spacing: 8) {
                Text(stock.symbol)
                    .font(.title)
                    .fontWeight(.bold)
                
                Text(getCompanyName(for: stock.symbol))
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                // Current Price
                VStack(spacing: 4) {
                    Text("$\(stock.price, specifier: "%.2f")")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    HStack(spacing: 4) {
                        Image(systemName: stock.change >= 0 ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        Text("$\(abs(stock.change), specifier: "%.2f") (\(stock.changePercent >= 0 ? "+" : "")\(stock.changePercent, specifier: "%.2f")%)")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(stock.change >= 0 ? .green : .red)
                }
            }
        }
    }
    
    private func getCompanyName(for symbol: String) -> String {
        switch symbol {
        case "AAPL": return "Apple Inc."
        case "TSLA": return "Tesla Inc."
        case "NVDA": return "NVIDIA Corp."
        case "GOOGL": return "Alphabet Inc."
        case "MSFT": return "Microsoft Corp."
        case "AMZN": return "Amazon.com Inc."
        case "META": return "Meta Platforms Inc."
        case "NFLX": return "Netflix Inc."
        default: return "Company"
        }
    }
}

// MARK: - Trade Type Selector
struct TradeTypeSelector: View {
    @Binding var selectedType: TradeType
    
    var body: some View {
        HStack(spacing: 0) {
            ForEach(TradeType.allCases, id: \.self) { type in
                Button(action: {
                    selectedType = type
                }) {
                    Text(type.displayName)
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(selectedType == type ? .white : .primary)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedType == type ? (type == .buy ? Color.green : Color.red) : Color(.systemGray6))
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal)
    }
}

// MARK: - Shares Input Section
struct SharesInputSection: View {
    @Binding var shares: Double
    let stock: StockPrice
    let tradeType: TradeType
    let availableShares: Double
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Number of Shares")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                if tradeType == .sell {
                    Text("Available: \(Int(availableShares))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // Shares Stepper
            HStack(spacing: 16) {
                Button(action: {
                    if shares > 1 {
                        shares -= 1
                    }
                }) {
                    Image(systemName: "minus.circle.fill")
                        .font(.title2)
                        .foregroundColor(shares > 1 ? .purple : .gray)
                }
                .disabled(shares <= 1)
                
                VStack(spacing: 4) {
                    Text("\(Int(shares))")
                        .font(.title)
                        .fontWeight(.bold)
                    Text("shares")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(minWidth: 80)
                
                Button(action: {
                    let maxShares = tradeType == .buy ? availableShares : availableShares
                    if shares < maxShares {
                        shares += 1
                    }
                }) {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)
                        .foregroundColor(shares < availableShares ? .purple : .gray)
                }
                .disabled(shares >= availableShares)
            }
            .frame(maxWidth: .infinity)
            
            // Quick Amount Buttons
            if tradeType == .buy {
                HStack(spacing: 12) {
                    ForEach([25, 50, 100, 250], id: \.self) { amount in
                        let maxShares = Int(simulatorManager.virtualBalance / stock.price)
                        let targetShares = min(amount / Int(stock.price), maxShares)
                        
                        Button("$\(amount)") {
                            shares = Double(targetShares)
                        }
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.purple)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.purple, lineWidth: 1)
                        )
                        .disabled(targetShares <= 0)
                    }
                }
            }
        }
        .padding(.horizontal)
    }
}

// MARK: - Order Summary Section
struct OrderSummarySection: View {
    let stock: StockPrice
    let shares: Double
    let tradeType: TradeType
    let totalCost: Double
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Order Summary")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                OrderSummaryRow(title: "Symbol", value: stock.symbol)
                OrderSummaryRow(title: "Shares", value: "\(Int(shares))")
                OrderSummaryRow(title: "Price per Share", value: "$\(stock.price, specifier: "%.2f")")
                
                Divider()
                
                OrderSummaryRow(
                    title: "Total \(tradeType == .buy ? "Cost" : "Proceeds")",
                    value: "$\(totalCost, specifier: "%.2f")",
                    isTotal: true
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
        .padding(.horizontal)
    }
}

// MARK: - Order Summary Row
struct OrderSummaryRow: View {
    let title: String
    let value: String
    var isTotal: Bool = false
    
    var body: some View {
        HStack {
            Text(title)
                .font(isTotal ? .headline : .subheadline)
                .fontWeight(isTotal ? .semibold : .regular)
                .foregroundColor(isTotal ? .primary : .secondary)
            
            Spacer()
            
            Text(value)
                .font(isTotal ? .headline : .subheadline)
                .fontWeight(isTotal ? .bold : .medium)
                .foregroundColor(isTotal ? .primary : .primary)
        }
    }
}

// MARK: - Account Balance Section
struct AccountBalanceSection: View {
    let virtualBalance: Double
    let totalCost: Double
    let tradeType: TradeType
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Account Balance")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                HStack {
                    Text("Current Balance")
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("$\(virtualBalance, specifier: "%.2f")")
                        .fontWeight(.medium)
                }
                
                if tradeType == .buy {
                    HStack {
                        Text("After Purchase")
                            .foregroundColor(.secondary)
                        Spacer()
                        Text("$\(virtualBalance - totalCost, specifier: "%.2f")")
                            .fontWeight(.medium)
                            .foregroundColor(virtualBalance - totalCost >= 0 ? .primary : .red)
                    }
                } else {
                    HStack {
                        Text("After Sale")
                            .foregroundColor(.secondary)
                        Spacer()
                        Text("$\(virtualBalance + totalCost, specifier: "%.2f")")
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
        .padding(.horizontal)
    }
}

// MARK: - Supporting Types
enum TradeType: String, CaseIterable {
    case buy = "buy"
    case sell = "sell"
    
    var displayName: String {
        switch self {
        case .buy: return "Buy"
        case .sell: return "Sell"
        }
    }
}

#Preview {
    SimulatorTradeView(
        stock: StockPrice(
            symbol: "AAPL",
            price: 150.25,
            change: 2.50,
            changePercent: 1.69,
            timestamp: Date()
        )
    )
    .environmentObject(SimulatorManager())
}
