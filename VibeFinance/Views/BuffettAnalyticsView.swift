//
//  BuffettAnalyticsView.swift
//  VibeFinance - <PERSON> Buffett Inspired Analytics
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

// MARK: - Analytics Data Models

struct AnalyticsMetric: Identifiable {
    let id = UUID()
    let title: String
    let value: String
    let change: String
    let isPositive: Bool
    let icon: String
    let description: String
    let benchmark: String?
    let context: String
}

struct PortfolioInsight {
    let title: String
    let description: String
    let recommendation: String
    let priority: InsightPriority
    let buffettWisdom: String

    enum InsightPriority {
        case high, medium, low

        var color: Color {
            switch self {
            case .high: return .red
            case .medium: return .orange
            case .low: return .blue
            }
        }
    }
}

struct BuffettAnalyticsView: View {
    @State private var selectedTab = 0
    @State private var selectedTimePeriod: TimePeriod = .oneMonth
    @State private var showingDetailedView = false
    @State private var selectedMetric: AnalyticsMetric?
    @StateObject private var accessibilityManager = AccessibilityManager()

    private let tabs = ["Overview", "Performance", "Risk", "Holdings"]
    
    enum TimePeriod: String, CaseIterable {
        case oneWeek = "1W"
        case oneMonth = "1M"
        case threeMonths = "3M"
        case oneYear = "1Y"
        case all = "All"
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Unified Warren Buffett inspired gradient background
                VibeFinanceDesignSystem.Colors.primaryGradient
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Compact Analytics Header with Key Metrics
                    CompactAnalyticsHeader(
                        selectedTimePeriod: $selectedTimePeriod,
                        onMetricTap: { metric in
                            selectedMetric = metric
                            showingDetailedView = true
                        }
                    )
                    .padding(.horizontal, 16)
                    .padding(.top, 8)

                    // Enhanced Tab Selector with Warren Buffett styling
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(Array(tabs.enumerated()), id: \.offset) { index, tab in
                                Button(action: {
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        selectedTab = index
                                    }
                                }) {
                                    VStack(spacing: 4) {
                                        Text(tab)
                                            .font(.system(size: 16, weight: selectedTab == index ? .bold : .medium))
                                            .foregroundColor(selectedTab == index ? VibeFinanceDesignSystem.Colors.accentGold : .white.opacity(0.7))
                                        
                                        // Active indicator
                                        Rectangle()
                                            .fill(selectedTab == index ? VibeFinanceDesignSystem.Colors.accentGold : Color.clear)
                                            .frame(height: 2)
                                            .animation(.easeInOut(duration: 0.3), value: selectedTab)
                                    }
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                            }
                        }
                        .padding(.horizontal, 16)
                    }
                    .padding(.top, 8)

                    // Tab Content with Enhanced Analytics
                    TabView(selection: $selectedTab) {
                        // Overview Tab - Enhanced with Warren's Insights
                        EnhancedAnalyticsOverviewTab(
                            timePeriod: selectedTimePeriod,
                            onInsightTap: { insight in
                                // Handle insight tap
                            }
                        )
                        .tag(0)
                        
                        // Performance Tab - Enhanced with Benchmarks
                        EnhancedAnalyticsPerformanceTab(
                            timePeriod: selectedTimePeriod,
                            onBenchmarkTap: { benchmark in
                                // Handle benchmark tap
                            }
                        )
                        .tag(1)
                        
                        // Risk Tab - Enhanced with Risk Analysis
                        EnhancedAnalyticsRiskTab(
                            onRiskDetailTap: { riskDetail in
                                // Handle risk detail tap
                            }
                        )
                        .tag(2)
                        
                        // Holdings Tab - Enhanced with Position Analysis
                        EnhancedAnalyticsHoldingsTab(
                            onHoldingTap: { holding in
                                // Handle holding tap
                            }
                        )
                        .tag(3)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                }
            }
            .navigationTitle("Analytics")
            .navigationBarTitleDisplayMode(.inline)
            .sheet(isPresented: $showingDetailedView) {
                if let metric = selectedMetric {
                    DetailedMetricView(metric: metric)
                        .environmentObject(accessibilityManager)
                }
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }
}

// MARK: - Supporting Views

struct CompactAnalyticsHeader: View {
    @Binding var selectedTimePeriod: BuffettAnalyticsView.TimePeriod
    let onMetricTap: (AnalyticsMetric) -> Void
    
    private let keyMetrics = [
        AnalyticsMetric(
            title: "Portfolio Value",
            value: "$127,450",
            change: "+12.5%",
            isPositive: true,
            icon: "chart.pie.fill",
            description: "Total portfolio value including all holdings",
            benchmark: "S&P 500: +8.2%",
            context: "Your portfolio is outperforming the market by 4.3%"
        ),
        AnalyticsMetric(
            title: "Monthly Return",
            value: "8.2%",
            change: "+2.1%",
            isPositive: true,
            icon: "arrow.up.right",
            description: "Monthly return on investment",
            benchmark: "Market Average: 6.1%",
            context: "Strong performance driven by tech holdings"
        ),
        AnalyticsMetric(
            title: "Risk Score",
            value: "6.8/10",
            change: "-0.3",
            isPositive: true,
            icon: "shield.fill",
            description: "Portfolio risk assessment",
            benchmark: nil,
            context: "Moderate risk level with good diversification"
        )
    ]
    
    var body: some View {
        VStack(spacing: 12) {
            // Time Period Selector
            HStack {
                Text("Analytics")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Spacer()
                
                // Time Period Pills
                HStack(spacing: 8) {
                    ForEach(BuffettAnalyticsView.TimePeriod.allCases, id: \.self) { period in
                        Button(action: {
                            selectedTimePeriod = period
                        }) {
                            Text(period.rawValue)
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(selectedTimePeriod == period ? .black : .white.opacity(0.8))
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(selectedTimePeriod == period ? VibeFinanceDesignSystem.Colors.accentGold : Color.white.opacity(0.1))
                                )
                        }
                    }
                }
            }
            
            // Key Metrics Carousel
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(keyMetrics) { metric in
                        Button(action: {
                            onMetricTap(metric)
                        }) {
                            VStack(alignment: .leading, spacing: 8) {
                                HStack {
                                    Image(systemName: metric.icon)
                                        .font(.title3)
                                        .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                                    
                                    Spacer()
                                    
                                    HStack(spacing: 4) {
                                        Image(systemName: metric.isPositive ? "arrow.up.right" : "arrow.down.right")
                                            .font(.caption)
                                        
                                        Text(metric.change)
                                            .font(.caption)
                                            .fontWeight(.semibold)
                                    }
                                    .foregroundColor(metric.isPositive ? .green : .red)
                                }
                                
                                Text(metric.title)
                                    .font(.caption)
                                    .foregroundColor(.white.opacity(0.8))
                                
                                Text(metric.value)
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                            }
                            .padding(12)
                            .frame(width: 140, height: 80)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.white.opacity(0.1))
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(Color.white.opacity(0.2), lineWidth: 1)
                                    )
                            )
                        }
                    }
                }
                .padding(.horizontal, 16)
            }
        }
    }
}

// MARK: - Enhanced Tab Views

struct EnhancedAnalyticsOverviewTab: View {
    let timePeriod: BuffettAnalyticsView.TimePeriod
    let onInsightTap: (PortfolioInsight) -> Void

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                Text("Enhanced Overview Tab - Coming Soon")
                    .foregroundColor(.white)
            }
            .padding(16)
        }
    }
}

struct EnhancedAnalyticsPerformanceTab: View {
    let timePeriod: BuffettAnalyticsView.TimePeriod
    let onBenchmarkTap: (String) -> Void

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                Text("Enhanced Performance Tab - Coming Soon")
                    .foregroundColor(.white)
            }
            .padding(16)
        }
    }
}

struct EnhancedAnalyticsRiskTab: View {
    let onRiskDetailTap: (String) -> Void

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                Text("Enhanced Risk Tab - Coming Soon")
                    .foregroundColor(.white)
            }
            .padding(16)
        }
    }
}

struct EnhancedAnalyticsHoldingsTab: View {
    let onHoldingTap: (String) -> Void

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                Text("Enhanced Holdings Tab - Coming Soon")
                    .foregroundColor(.white)
            }
            .padding(16)
        }
    }
}

struct DetailedMetricView: View {
    let metric: AnalyticsMetric
    @EnvironmentObject var accessibilityManager: AccessibilityManager

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Metric Header
                    VStack(spacing: 8) {
                        Image(systemName: metric.icon)
                            .font(.system(size: 40))
                            .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)

                        Text(metric.title)
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        Text(metric.value)
                            .font(.largeTitle)
                            .fontWeight(.bold)
                            .foregroundColor(.white)

                        HStack(spacing: 4) {
                            Image(systemName: metric.isPositive ? "arrow.up.right" : "arrow.down.right")
                                .font(.title3)

                            Text(metric.change)
                                .font(.title3)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(metric.isPositive ? .green : .red)
                    }

                    // Description
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Description")
                            .font(.headline)
                            .foregroundColor(.white)

                        Text(metric.description)
                            .font(.body)
                            .foregroundColor(.white.opacity(0.9))
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.white.opacity(0.1))
                    )

                    // Context
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Context")
                            .font(.headline)
                            .foregroundColor(.white)

                        Text(metric.context)
                            .font(.body)
                            .foregroundColor(.white.opacity(0.9))
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.white.opacity(0.1))
                    )
                }
                .padding(16)
            }
            .background(VibeFinanceDesignSystem.Colors.primaryGradient)
            .navigationTitle("Metric Details")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    BuffettAnalyticsView()
        .preferredColorScheme(.dark)
}
