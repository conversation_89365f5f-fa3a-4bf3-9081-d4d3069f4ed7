//
//  BuffettAnalyticsView.swift
//  VibeFinance - <PERSON> Buffett Inspired Analytics
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

struct BuffettAnalyticsView: View {
    @State private var selectedTab = 0
    @State private var selectedTimePeriod: TimePeriod = .oneMonth
    
    private let tabs = ["Overview", "Performance", "Risk", "Holdings"]
    
    enum TimePeriod: String, CaseIterable {
        case oneWeek = "1W"
        case oneMonth = "1M"
        case threeMonths = "3M"
        case oneYear = "1Y"
        case all = "All"
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Unified Warren Buffett inspired gradient background
                VibeFinanceDesignSystem.Colors.primaryGradient
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Warren's Analytics Philosophy Card
                    BuffettAnalyticsPhilosophyCard()
                        .padding(.horizontal, 16)
                        .padding(.top, 8)
                    
                    // Time Period Selector
                    BuffettTimePeriodSelector(selectedPeriod: $selectedTimePeriod)
                        .padding(.horizontal, 16)
                        .padding(.top, 8)
                    
                    // Unified Tab Selector with <PERSON> Buffett styling
                    VibeTabSelector(
                        selectedTab: $selectedTab,
                        tabs: tabs
                    )
                    .padding(.horizontal, 16)
                    .padding(.top, 8)
                    
                    // Content based on selected tab
                    TabView(selection: $selectedTab) {
                        // Overview Tab
                        BuffettAnalyticsOverviewTab(timePeriod: selectedTimePeriod)
                            .tag(0)
                        
                        // Performance Tab
                        BuffettAnalyticsPerformanceTab(timePeriod: selectedTimePeriod)
                            .tag(1)
                        
                        // Risk Tab
                        BuffettAnalyticsRiskTab()
                            .tag(2)
                        
                        // Holdings Tab
                        BuffettAnalyticsHoldingsTab()
                            .tag(3)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                }
            }
            .navigationTitle("Analytics")
            .navigationBarTitleDisplayMode(.large)
            .preferredColorScheme(.dark)
        }
    }
}

// MARK: - Analytics Philosophy Card
struct BuffettAnalyticsPhilosophyCard: View {
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.purple.opacity(0.3),
                            Color.blue.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("📊")
                        .font(.title2)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Warren's Analytics Wisdom")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text("Focus on what matters for long-term wealth")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    
                    Spacer()
                }
                
                Text("\"Risk comes from not knowing what you're doing. These metrics help you understand your investments better.\"")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.9))
                    .italic()
            }
            .padding(16)
        }
    }
}

// MARK: - Time Period Selector
struct BuffettTimePeriodSelector: View {
    @Binding var selectedPeriod: BuffettAnalyticsView.TimePeriod
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.15),
                            Color.white.opacity(0.05)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
                .blur(radius: 0.5)
            
            HStack(spacing: 0) {
                ForEach(BuffettAnalyticsView.TimePeriod.allCases, id: \.self) { period in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            selectedPeriod = period
                        }
                    }) {
                        ZStack {
                            if selectedPeriod == period {
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(
                                        LinearGradient(
                                            colors: [
                                                Color.yellow.opacity(0.8),
                                                Color.orange.opacity(0.6)
                                            ],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                            }
                            
                            Text(period.rawValue)
                                .font(.caption)
                                .fontWeight(selectedPeriod == period ? .bold : .medium)
                                .foregroundColor(selectedPeriod == period ? .black : .white.opacity(0.8))
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 32)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(4)
        }
        .frame(height: 40)
    }
}

// MARK: - Analytics Overview Tab
struct BuffettAnalyticsOverviewTab: View {
    let timePeriod: BuffettAnalyticsView.TimePeriod
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Portfolio Performance Summary
                BuffettPerformanceSummaryCard()
                    .padding(.horizontal, 16)
                
                // Key Metrics Grid
                BuffettKeyMetricsGrid()
                    .padding(.horizontal, 16)
                
                // Warren's Investment Principles Progress
                BuffettPrinciplesProgressCard()
                    .padding(.horizontal, 16)
            }
            .padding(.vertical, 16)
        }
    }
}

// MARK: - Performance Summary Card
struct BuffettPerformanceSummaryCard: View {
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.green.opacity(0.3),
                            Color.blue.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)
            
            VStack(spacing: 16) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("📈 Portfolio Performance")
                            .font(.headline)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text("Following Warren's principles")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("+23.4%")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(.green)
                        
                        Text("vs S&P 500: +18.2%")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                }
                
                HStack(spacing: 20) {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Total Return")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                        Text("$28,470")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Annual Return")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                        Text("15.8%")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.yellow)
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Sharpe Ratio")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                        Text("1.42")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                    
                    Spacer()
                }
            }
            .padding(20)
        }
    }
}

// MARK: - Key Metrics Grid
struct BuffettKeyMetricsGrid: View {
    private let metrics = [
        ("🎯", "Hit Rate", "73%", "Winning trades"),
        ("⏰", "Avg Hold Time", "247 days", "Long-term focus"),
        ("💎", "Best Performer", "AAPL", "+45.2% gain"),
        ("🛡️", "Max Drawdown", "-8.3%", "Risk control")
    ]
    
    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
            ForEach(Array(metrics.enumerated()), id: \.offset) { index, metric in
                BuffettMetricCard(
                    emoji: metric.0,
                    title: metric.1,
                    value: metric.2,
                    subtitle: metric.3
                )
            }
        }
    }
}

// MARK: - Metric Card
struct BuffettMetricCard: View {
    let emoji: String
    let title: String
    let value: String
    let subtitle: String
    
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(0.15),
                            Color.white.opacity(0.05)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.white.opacity(0.2), lineWidth: 1)
                )
                .blur(radius: 0.5)
            
            VStack(spacing: 8) {
                Text(emoji)
                    .font(.title2)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                
                Text(value)
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text(subtitle)
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.6))
                    .multilineTextAlignment(.center)
            }
            .padding(12)
        }
    }
}

// MARK: - Principles Progress Card
struct BuffettPrinciplesProgressCard: View {
    private let principles = [
        ("🎯", "Value Investing", 0.85, "Finding undervalued companies"),
        ("⏳", "Long-term Thinking", 0.92, "Holding for years, not months"),
        ("🏰", "Economic Moats", 0.78, "Companies with competitive advantages"),
        ("💰", "Compound Interest", 0.88, "Letting money work for you")
    ]

    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.orange.opacity(0.3),
                            Color.yellow.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Text("🧠 Warren's Principles Progress")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    Text("86% Complete")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.yellow)
                }

                VStack(spacing: 12) {
                    ForEach(Array(principles.enumerated()), id: \.offset) { index, principle in
                        HStack(spacing: 12) {
                            Text(principle.0)
                                .font(.title3)

                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text(principle.1)
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.white)

                                    Spacer()

                                    Text("\(Int(principle.2 * 100))%")
                                        .font(.caption)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.yellow)
                                }

                                ZStack(alignment: .leading) {
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(Color.white.opacity(0.2))
                                        .frame(height: 6)

                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(
                                            LinearGradient(
                                                colors: [.yellow, .orange],
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            )
                                        )
                                        .frame(width: UIScreen.main.bounds.width * 0.6 * principle.2, height: 6)
                                }

                                Text(principle.3)
                                    .font(.caption2)
                                    .foregroundColor(.white.opacity(0.7))
                            }
                        }
                    }
                }
            }
            .padding(20)
        }
    }
}

// MARK: - Analytics Performance Tab
struct BuffettAnalyticsPerformanceTab: View {
    let timePeriod: BuffettAnalyticsView.TimePeriod

    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Performance Chart Placeholder
                BuffettPerformanceChartCard()
                    .padding(.horizontal, 16)

                // Benchmark Comparison
                BuffettBenchmarkComparisonCard()
                    .padding(.horizontal, 16)

                // Monthly Returns
                BuffettMonthlyReturnsCard()
                    .padding(.horizontal, 16)
            }
            .padding(.vertical, 16)
        }
    }
}

// MARK: - Performance Chart Card
struct BuffettPerformanceChartCard: View {
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.blue.opacity(0.3),
                            Color.purple.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            VStack(spacing: 16) {
                HStack {
                    Text("📊 Portfolio Growth")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    Text("$128,470")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                }

                // Simulated chart area
                ZStack {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.black.opacity(0.3))
                        .frame(height: 120)

                    VStack {
                        Text("📈")
                            .font(.largeTitle)
                        Text("Portfolio Performance Chart")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                        Text("Coming Soon")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.5))
                    }
                }

                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Starting Value")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                        Text("$100,000")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Total Gain")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.7))
                        Text("+$28,470")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }
                }
            }
            .padding(20)
        }
    }
}

// MARK: - Benchmark Comparison Card
struct BuffettBenchmarkComparisonCard: View {
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.green.opacity(0.3),
                            Color.teal.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            VStack(spacing: 16) {
                HStack {
                    Text("🏆 Beating the Market")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)

                    Spacer()

                    Text("+5.2%")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                }

                VStack(spacing: 12) {
                    HStack {
                        Text("Your Portfolio")
                            .font(.subheadline)
                            .foregroundColor(.white)

                        Spacer()

                        Text("23.4%")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.green)
                    }

                    HStack {
                        Text("S&P 500")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))

                        Spacer()

                        Text("18.2%")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white.opacity(0.8))
                    }

                    HStack {
                        Text("Berkshire Hathaway")
                            .font(.subheadline)
                            .foregroundColor(.white.opacity(0.8))

                        Spacer()

                        Text("21.1%")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.yellow)
                    }
                }

                Text("\"Our favorite holding period is forever.\" - Warren Buffett")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
                    .italic()
                    .multilineTextAlignment(.center)
            }
            .padding(20)
        }
    }
}

// MARK: - Analytics Risk Tab
struct BuffettAnalyticsRiskTab: View {
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Risk Overview
                BuffettRiskOverviewCard()
                    .padding(.horizontal, 16)

                // Diversification Analysis
                BuffettDiversificationCard()
                    .padding(.horizontal, 16)

                // Warren's Risk Wisdom
                BuffettRiskWisdomCard()
                    .padding(.horizontal, 16)
            }
            .padding(.vertical, 16)
        }
    }
}

// MARK: - Analytics Holdings Tab
struct BuffettAnalyticsHoldingsTab: View {
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 16) {
                // Top Holdings
                BuffettTopHoldingsCard()
                    .padding(.horizontal, 16)

                // Sector Allocation
                BuffettSectorAllocationCard()
                    .padding(.horizontal, 16)

                // Recent Transactions
                BuffettRecentTransactionsCard()
                    .padding(.horizontal, 16)
            }
            .padding(.vertical, 16)
        }
    }
}

// MARK: - Monthly Returns Card
struct BuffettMonthlyReturnsCard: View {
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.orange.opacity(0.3),
                            Color.red.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            VStack(alignment: .leading, spacing: 16) {
                Text("📅 Monthly Performance")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("Consistent growth following Warren's principles")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))

                // Placeholder for monthly returns grid
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 8) {
                    ForEach(["Jan", "Feb", "Mar", "Apr", "May", "Jun"], id: \.self) { month in
                        VStack(spacing: 4) {
                            Text(month)
                                .font(.caption2)
                                .foregroundColor(.white.opacity(0.7))

                            Text("+\(Int.random(in: 1...8))%")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(.green)
                        }
                        .padding(8)
                        .background(
                            RoundedRectangle(cornerRadius: 6)
                                .fill(Color.white.opacity(0.1))
                        )
                    }
                }
            }
            .padding(20)
        }
    }
}

// MARK: - Placeholder Cards for Risk and Holdings
struct BuffettRiskOverviewCard: View {
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.red.opacity(0.3),
                            Color.orange.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            VStack(spacing: 16) {
                Text("🛡️ Risk Analysis")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("\"Risk comes from not knowing what you're doing.\"")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.9))
                    .italic()
                    .multilineTextAlignment(.center)

                HStack(spacing: 20) {
                    VStack {
                        Text("Low")
                            .font(.caption)
                            .foregroundColor(.green)
                        Text("Risk Score")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                    }

                    VStack {
                        Text("8.3%")
                            .font(.caption)
                            .foregroundColor(.yellow)
                        Text("Max Drawdown")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                    }

                    VStack {
                        Text("1.42")
                            .font(.caption)
                            .foregroundColor(.green)
                        Text("Sharpe Ratio")
                            .font(.caption2)
                            .foregroundColor(.white.opacity(0.7))
                    }
                }
            }
            .padding(20)
        }
    }
}

struct BuffettDiversificationCard: View {
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.purple.opacity(0.3),
                            Color.pink.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            VStack(spacing: 16) {
                Text("🎯 Diversification")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("Well-diversified across Warren's favorite sectors")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            .padding(20)
        }
    }
}

struct BuffettRiskWisdomCard: View {
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.yellow.opacity(0.3),
                            Color.orange.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            VStack(spacing: 12) {
                Text("💡 Warren's Risk Wisdom")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("\"Rule No. 1: Never lose money. Rule No. 2: Never forget rule No. 1.\"")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.9))
                    .italic()
                    .multilineTextAlignment(.center)
            }
            .padding(20)
        }
    }
}

struct BuffettTopHoldingsCard: View {
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.blue.opacity(0.3),
                            Color.cyan.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            VStack(spacing: 16) {
                Text("🏆 Top Holdings")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("Your best performing investments")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            .padding(20)
        }
    }
}

struct BuffettSectorAllocationCard: View {
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.green.opacity(0.3),
                            Color.mint.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            VStack(spacing: 16) {
                Text("🏭 Sector Allocation")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("Balanced across Warren's preferred sectors")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            .padding(20)
        }
    }
}

struct BuffettRecentTransactionsCard: View {
    var body: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 16)
                .fill(
                    LinearGradient(
                        colors: [
                            Color.orange.opacity(0.3),
                            Color.yellow.opacity(0.2)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.white.opacity(0.3), lineWidth: 1)
                )
                .blur(radius: 0.5)

            VStack(spacing: 16) {
                Text("📋 Recent Activity")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("Your latest investment decisions")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
            }
            .padding(20)
        }
    }
}

#Preview {
    BuffettAnalyticsView()
        .preferredColorScheme(.dark)
}
