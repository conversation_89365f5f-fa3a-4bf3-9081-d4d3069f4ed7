//
//  RealTradingComponents.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Real Holding Card
struct RealHoldingCard: View {
    let holding: RealHolding
    @State private var showingDetail = false
    
    var body: some View {
        Button(action: {
            showingDetail = true
        }) {
            VStack(spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(holding.symbol)
                            .font(.headline)
                            .fontWeight(.bold)
                        
                        Text("\(holding.quantity, specifier: "%.0f") shares")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("$\(holding.marketValue, specifier: "%.2f")")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        HStack(spacing: 4) {
                            Image(systemName: holding.unrealizedPL >= 0 ? "arrow.up.right" : "arrow.down.right")
                                .font(.caption)
                            Text("$\(abs(holding.unrealizedPL), specifier: "%.2f")")
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(holding.unrealizedPL >= 0 ? .green : .red)
                    }
                }
                
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Current Price")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("$\(holding.currentPrice, specifier: "%.2f")")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Avg Cost")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("$\(holding.averagePrice, specifier: "%.2f")")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("Return")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("\(holding.unrealizedPLPercent >= 0 ? "+" : "")\(holding.unrealizedPLPercent, specifier: "%.2f")%")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(holding.unrealizedPL >= 0 ? .green : .red)
                    }
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingDetail) {
            RealHoldingDetailView(holding: holding)
        }
    }
}

// MARK: - Real Order Card
struct RealOrderCard: View {
    let order: RealOrder
    @EnvironmentObject var realTradingManager: RealTradingManager
    @State private var showingCancelAlert = false
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 8) {
                        Text(order.side.displayName)
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                RoundedRectangle(cornerRadius: 6)
                                    .fill(order.side == .buy ? Color.green : Color.red)
                            )
                        
                        Text(order.symbol)
                            .font(.headline)
                            .fontWeight(.bold)
                    }
                    
                    Text("\(order.qty, specifier: "%.0f") shares • \(order.type.displayName)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(order.status.capitalized)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(statusColor(order.status))
                    
                    if let price = order.filledAvgPrice {
                        Text("$\(price, specifier: "%.2f")")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    } else if let limitPrice = order.limitPrice {
                        Text("$\(limitPrice, specifier: "%.2f")")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                }
            }
            
            if order.isActive {
                HStack {
                    Text("Filled: \(order.filledQty, specifier: "%.0f") / \(order.qty, specifier: "%.0f")")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Button("Cancel") {
                        showingCancelAlert = true
                    }
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.red)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
        .alert("Cancel Order", isPresented: $showingCancelAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Confirm", role: .destructive) {
                Task {
                    await realTradingManager.cancelOrder(order.id)
                }
            }
        } message: {
            Text("Are you sure you want to cancel this order?")
        }
    }
    
    private func statusColor(_ status: String) -> Color {
        switch status.lowercased() {
        case "filled": return .green
        case "cancelled": return .red
        case "new", "pending_new": return .blue
        case "partially_filled": return .orange
        default: return .secondary
        }
    }
}

// MARK: - Quick Trade Button
struct QuickTradeButton: View {
    let title: String
    let symbol: String
    let side: OrderSide
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: side == .buy ? "plus.circle.fill" : "minus.circle.fill")
                    .font(.title2)
                    .foregroundColor(side == .buy ? .green : .red)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Quick Sell Card
struct QuickSellCard: View {
    let holding: RealHolding
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(holding.symbol)
                    .font(.headline)
                    .fontWeight(.bold)
                
                Text("\(holding.quantity, specifier: "%.0f") shares")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("$\(holding.currentPrice, specifier: "%.2f")")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack(spacing: 4) {
                    Image(systemName: "minus.circle.fill")
                        .font(.caption)
                    Text("Sell")
                        .font(.caption)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.red)
            }
            .padding()
            .frame(width: 120)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Performance Summary Card
struct PerformanceSummaryCard: View {
    let performance: RealPortfolioPerformance
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Performance Summary")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    MetricRow(title: "Total Value", value: "$\(performance.totalValue, specifier: "%.2f")")
                    MetricRow(title: "Total Gain/Loss", value: "$\(performance.totalGainLoss, specifier: "%.2f")", color: performance.isPositive ? .green : .red)
                    MetricRow(title: "Cash Balance", value: "$\(performance.cashBalance, specifier: "%.2f")")
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 8) {
                    MetricRow(title: "Day Change", value: "$\(performance.dayChange, specifier: "%.2f")", color: performance.isDayPositive ? .green : .red, alignment: .trailing)
                    MetricRow(title: "Return %", value: "\(performance.totalGainLossPercent >= 0 ? "+" : "")\(performance.totalGainLossPercent, specifier: "%.2f")%", color: performance.isPositive ? .green : .red, alignment: .trailing)
                    MetricRow(title: "Buying Power", value: "$\(performance.buyingPower, specifier: "%.2f")", alignment: .trailing)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Performers Card
struct PerformersCard: View {
    let best: RealHolding
    let worst: RealHolding
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Top Performers")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Best")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(best.symbol)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text("+\(best.unrealizedPLPercent, specifier: "%.2f")%")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.green)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 8) {
                    Text("Worst")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text(worst.symbol)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Text("\(worst.unrealizedPLPercent, specifier: "%.2f")%")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.red)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Asset Allocation Card
struct AssetAllocationCard: View {
    let holdings: [RealHolding]
    
    var totalValue: Double {
        holdings.reduce(0) { $0 + $1.marketValue }
    }
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Asset Allocation")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                ForEach(holdings.prefix(5), id: \.id) { holding in
                    let percentage = totalValue > 0 ? (holding.marketValue / totalValue) * 100 : 0
                    
                    HStack {
                        Text(holding.symbol)
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        Text("\(percentage, specifier: "%.1f")%")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    ProgressView(value: percentage / 100)
                        .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Metric Row
struct MetricRow: View {
    let title: String
    let value: String
    var color: Color = .primary
    var alignment: HorizontalAlignment = .leading
    
    var body: some View {
        VStack(alignment: alignment, spacing: 2) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
    }
}

// MARK: - Empty States
struct EmptyPortfolioView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "chart.pie")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No Holdings Yet")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Start investing to see your portfolio here")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
    }
}

struct EmptyOrdersView: View {
    let filter: String
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "list.bullet")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("No \(filter) Orders")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Your \(filter.lowercased()) orders will appear here")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
    }
}

// MARK: - Search Bar
struct SearchBar: View {
    @Binding var text: String
    let placeholder: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField(placeholder, text: $text)
                .textFieldStyle(PlainTextFieldStyle())
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(.systemGray6))
        )
    }
}
