//
//  AnalyticsComponents.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Time Period Selector
struct TimePeriodSelector: View {
    @Binding var selectedPeriod: TimePeriod
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(TimePeriod.allCases, id: \.self) { period in
                    Button(action: {
                        selectedPeriod = period
                    }) {
                        Text(period.rawValue)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(selectedPeriod == period ? .white : .primary)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(selectedPeriod == period ? Color.purple : Color(.systemGray6))
                            )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding(.horizontal)
        }
    }
}

// MARK: - Portfolio Summary Card
struct PortfolioSummaryCard: View {
    let analytics: PortfolioAnalytics
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Portfolio Summary")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Text(analytics.performanceGrade)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.purple)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.purple.opacity(0.1))
                    )
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    MetricItem(title: "Total Value", value: "$\(String(format: "%.2f", analytics.totalValue))")
                    MetricItem(title: "Total Return", value: "\(analytics.totalReturn >= 0 ? "+" : "")\(String(format: "%.2f", analytics.totalReturn))%", color: analytics.totalReturn >= 0 ? .green : .red)
                    MetricItem(title: "Positions", value: "\(analytics.numberOfPositions)")
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 8) {
                    MetricItem(title: "Gain/Loss", value: "$\(String(format: "%.2f", analytics.totalGainLoss))", color: analytics.totalGainLoss >= 0 ? .green : .red, alignment: .trailing)
                    MetricItem(title: "Win Rate", value: "\(String(format: "%.1f", analytics.winRate))%", alignment: .trailing)
                    MetricItem(title: "Sharpe Ratio", value: "\(String(format: "%.2f", analytics.sharpeRatio))", alignment: .trailing)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Performance Chart Card
struct PerformanceChartCard: View {
    let data: [PerformanceDataPoint]
    let timePeriod: TimePeriod
    @State private var selectedPoint: PerformanceDataPoint?
    
    private var filteredData: [PerformanceDataPoint] {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -timePeriod.days, to: Date()) ?? Date()
        return data.filter { $0.date >= cutoffDate }.sorted { $0.date < $1.date }
    }
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Performance Chart")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                if let point = selectedPoint {
                    VStack(alignment: .trailing, spacing: 2) {
                        Text("$\(String(format: "%.2f", point.portfolioValue))")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                        Text(point.formattedDate)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            // Chart Area
            GeometryReader { geometry in
                ZStack {
                    // Background
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemBackground))
                        .shadow(color: .black.opacity(0.05), radius: 2)
                    
                    // Chart
                    LineChart(
                        data: filteredData,
                        geometry: geometry,
                        selectedPoint: $selectedPoint
                    )
                    .padding()
                }
            }
            .frame(height: 200)
            
            // Chart Legend
            HStack {
                Text(timePeriod.displayName)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                if let first = filteredData.first, let last = filteredData.last {
                    let change = last.portfolioValue - first.portfolioValue
                    let changePercent = first.portfolioValue > 0 ? (change / first.portfolioValue) * 100 : 0
                    
                    HStack(spacing: 4) {
                        Image(systemName: change >= 0 ? "arrow.up.right" : "arrow.down.right")
                            .font(.caption)
                        Text("\(change >= 0 ? "+" : "")\(String(format: "%.2f", changePercent))%")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(change >= 0 ? .green : .red)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Line Chart
struct LineChart: View {
    let data: [PerformanceDataPoint]
    let geometry: GeometryProxy
    @Binding var selectedPoint: PerformanceDataPoint?
    
    private var minValue: Double {
        data.map { $0.portfolioValue }.min() ?? 0
    }
    
    private var maxValue: Double {
        data.map { $0.portfolioValue }.max() ?? 100
    }
    
    private var valueRange: Double {
        maxValue - minValue
    }
    
    var body: some View {
        ZStack {
            // Grid lines
            VStack {
                ForEach(0..<5) { i in
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 0.5)
                    if i < 4 { Spacer() }
                }
            }
            
            // Line path
            Path { path in
                guard !data.isEmpty else { return }
                
                let width = geometry.size.width
                let height = geometry.size.height
                
                for (index, point) in data.enumerated() {
                    let x = width * Double(index) / Double(max(data.count - 1, 1))
                    let y = height * (1 - (point.portfolioValue - minValue) / max(valueRange, 1))
                    
                    if index == 0 {
                        path.move(to: CGPoint(x: x, y: y))
                    } else {
                        path.addLine(to: CGPoint(x: x, y: y))
                    }
                }
            }
            .stroke(
                LinearGradient(
                    colors: [.purple, .blue],
                    startPoint: .leading,
                    endPoint: .trailing
                ),
                lineWidth: 2
            )
            
            // Data points
            ForEach(Array(data.enumerated()), id: \.offset) { index, point in
                let x = geometry.size.width * Double(index) / Double(max(data.count - 1, 1))
                let y = geometry.size.height * (1 - (point.portfolioValue - minValue) / max(valueRange, 1))
                
                Circle()
                    .fill(Color.purple)
                    .frame(width: selectedPoint?.id == point.id ? 8 : 4, height: selectedPoint?.id == point.id ? 8 : 4)
                    .position(x: x, y: y)
                    .onTapGesture {
                        selectedPoint = point
                    }
            }
        }
        .onAppear {
            selectedPoint = data.last
        }
    }
}

// MARK: - Key Metrics Grid
struct KeyMetricsGrid: View {
    let analytics: PortfolioAnalytics
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Key Metrics")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                RiskMetricCard(
                    title: "Volatility",
                    value: "\(String(format: "%.1f", analytics.volatility))%",
                    subtitle: analytics.riskLevel.rawValue,
                    color: Color(analytics.riskLevel.color)
                )
                
                RiskMetricCard(
                    title: "Diversification",
                    value: "\(String(format: "%.0f", analytics.diversificationScore))",
                    subtitle: "Score",
                    color: .blue
                )

                RiskMetricCard(
                    title: "Avg Hold Period",
                    value: "\(String(format: "%.0f", analytics.averageHoldingPeriod))",
                    subtitle: "Days",
                    color: .orange
                )

                RiskMetricCard(
                    title: "Cash %",
                    value: "\(String(format: "%.1f", analytics.cashPercentage))%",
                    subtitle: "Allocation",
                    color: .green
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}



// MARK: - Quick Insights Card
struct QuickInsightsCard: View {
    let analytics: PortfolioAnalytics
    let risk: RiskMetrics
    let sentiment: MarketSentiment
    
    var body: some View {
        VStack(spacing: 16) {
            Text("Quick Insights")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            VStack(spacing: 12) {
                InsightRow(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Performance",
                    description: "Your portfolio is \(analytics.totalReturn >= 0 ? "up" : "down") \(String(format: "%.1f", abs(analytics.totalReturn)))% overall",
                    color: analytics.totalReturn >= 0 ? .green : .red
                )
                
                InsightRow(
                    icon: "shield.checkered",
                    title: "Risk Level",
                    description: "Risk Level: \(String(format: "%.1f", risk.beta))",
                    color: risk.beta > 1.5 ? .red : risk.beta > 1.0 ? .orange : .green
                )
                
                InsightRow(
                    icon: "brain.head.profile",
                    title: "Market Sentiment",
                    description: sentiment.sentimentDescription,
                    color: Color(sentiment.sentimentColor)
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Insight Row
struct InsightRow: View {
    let icon: String
    let title: String
    let description: String
    let color: Color
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
            
            Spacer()
        }
    }
}

// MARK: - Metric Item
struct MetricItem: View {
    let title: String
    let value: String
    var color: Color = .primary
    var alignment: HorizontalAlignment = .leading
    
    var body: some View {
        VStack(alignment: alignment, spacing: 2) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
    }
}

// MARK: - Benchmark Comparison Card
struct BenchmarkComparisonCard: View {
    let comparison: BenchmarkComparison
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Benchmark Comparison")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Text(comparison.outperformanceDescription)
                    .font(.caption)
                    .foregroundColor(comparison.isOutperforming ? .green : .red)
            }
            
            VStack(spacing: 12) {
                BenchmarkRow(
                    name: "Your Portfolio",
                    returnValue: comparison.portfolioReturn,
                    isPortfolio: true
                )

                BenchmarkRow(
                    name: "S&P 500",
                    returnValue: comparison.sp500Return
                )

                BenchmarkRow(
                    name: "NASDAQ",
                    returnValue: comparison.nasdaqReturn
                )

                BenchmarkRow(
                    name: "Total Market",
                    returnValue: comparison.totalMarketReturn
                )
            }
            
            HStack {
                Text("Alpha: \(String(format: "%.2f", comparison.alpha))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text("Best Match: \(comparison.bestBenchmark)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Benchmark Row
struct BenchmarkRow: View {
    let name: String
    let returnValue: Double
    var isPortfolio: Bool = false
    
    var body: some View {
        HStack {
            Text(name)
                .font(.subheadline)
                .fontWeight(isPortfolio ? .semibold : .regular)
                .foregroundColor(isPortfolio ? .purple : .primary)
            
            Spacer()
            
            Text("\(returnValue >= 0 ? "+" : "")\(String(format: "%.2f", returnValue))%")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(returnValue >= 0 ? .green : .red)
        }
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isPortfolio ? Color.purple.opacity(0.1) : Color.clear)
        )
    }
}

// MARK: - Risk Overview Card
struct RiskOverviewCard: View {
    let risk: RiskMetrics

    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Risk Overview")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Text("\(String(format: "%.1f", risk.beta))")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(risk.beta > 1.5 ? .red : risk.beta > 1.0 ? .orange : .green)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill((risk.beta > 1.5 ? Color.red : risk.beta > 1.0 ? Color.orange : Color.green).opacity(0.1))
                    )
            }

            // Risk Level Indicator
            VStack(spacing: 8) {
                HStack {
                    Text("Risk Level")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text(risk.riskLevel.rawValue)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(Color(risk.riskLevel.color))
                }

                ProgressView(value: risk.riskScore / 100)
                    .progressViewStyle(LinearProgressViewStyle(tint: Color(risk.riskLevel.color)))
                    .scaleEffect(y: 2)
            }

            Text(risk.riskDescription)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Risk Metrics Detail Card
struct RiskMetricsDetailCard: View {
    let risk: RiskMetrics

    var body: some View {
        VStack(spacing: 16) {
            Text("Risk Metrics")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                RiskMetricCard(
                    title: "Value at Risk (95%)",
                    value: "\(String(format: "%.1f%%", risk.maxDrawdown * 100))",
                    subtitle: "Potential 1-day loss",
                    color: .red
                )

                RiskMetricCard(
                    title: "Beta",
                    value: "\(String(format: "%.2f", risk.beta))",
                    subtitle: "vs Market",
                    color: .blue
                )

                RiskMetricCard(
                    title: "Max Drawdown",
                    value: "\(String(format: "%.1f", risk.maxDrawdown))%",
                    subtitle: "Largest loss",
                    color: .orange
                )

                RiskMetricCard(
                    title: "Concentration",
                    value: "\(String(format: "%.1f", risk.concentrationRisk))%",
                    subtitle: "Largest position",
                    color: .purple
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Risk Metric Card
struct RiskMetricCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Text(value)
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)

            Text(subtitle)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
        )
    }
}

// MARK: - Sector Allocation Chart Card
struct SectorAllocationChartCard: View {
    let sectors: SectorAnalysis

    var body: some View {
        VStack(spacing: 16) {
            Text("Sector Allocation")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            // Pie Chart (simplified as horizontal bars)
            VStack(spacing: 8) {
                ForEach(sectors.allocations.prefix(6), id: \.id) { allocation in
                    SectorAllocationBar(allocation: allocation)
                }
            }

            HStack {
                Text("Diversification Score")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                Spacer()
                Text("\(String(format: "%.0f", sectors.diversificationScore))/100")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(sectors.isDiversified ? .green : .orange)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Sector Allocation Bar
struct SectorAllocationBar: View {
    let allocation: SectorAllocation

    var body: some View {
        VStack(spacing: 4) {
            HStack {
                HStack(spacing: 8) {
                    Image(systemName: allocation.sectorIcon)
                        .font(.caption)
                        .foregroundColor(.purple)

                    Text(allocation.sector)
                        .font(.caption)
                        .fontWeight(.medium)
                }

                Spacer()

                Text("\(String(format: "%.1f", allocation.percentage))%")
                    .font(.caption)
                    .fontWeight(.semibold)
            }

            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color(.systemGray5))
                        .frame(height: 6)
                        .cornerRadius(3)

                    Rectangle()
                        .fill(Color.purple)
                        .frame(width: geometry.size.width * (allocation.percentage / 100), height: 6)
                        .cornerRadius(3)
                        .animation(.easeInOut(duration: 0.5), value: allocation.percentage)
                }
            }
            .frame(height: 6)
        }
    }
}

// MARK: - Market Sentiment Card
struct MarketSentimentCard: View {
    let sentiment: MarketSentiment

    var body: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Market Sentiment")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Text(sentiment.sentimentLabel)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(Color(sentiment.sentimentColor))
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(sentiment.sentimentColor).opacity(0.1))
                    )
            }

            // Sentiment Gauge
            VStack(spacing: 8) {
                ZStack {
                    Circle()
                        .stroke(Color(.systemGray5), lineWidth: 8)
                        .frame(width: 120, height: 120)

                    Circle()
                        .trim(from: 0, to: sentiment.overallSentiment)
                        .stroke(Color(sentiment.sentimentColor), lineWidth: 8)
                        .frame(width: 120, height: 120)
                        .rotationEffect(.degrees(-90))
                        .animation(.easeInOut(duration: 1), value: sentiment.overallSentiment)

                    VStack {
                        Text("\(Int(sentiment.overallSentiment * 100))")
                            .font(.title2)
                            .fontWeight(.bold)
                            .foregroundColor(Color(sentiment.sentimentColor))
                        Text("Score")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Text("Trend: \(sentiment.sentimentTrend)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Text(sentiment.sentimentDescription)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Performance Metrics Card
struct PerformanceMetricsCard: View {
    let analytics: PortfolioAnalytics

    var body: some View {
        VStack(spacing: 16) {
            Text("Performance Metrics")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 12) {
                PerformanceMetricRow(
                    title: "Total Return",
                    value: "\(analytics.totalReturn >= 0 ? "+" : "")\(String(format: "%.2f", analytics.totalReturn))%",
                    color: analytics.totalReturn >= 0 ? .green : .red
                )

                PerformanceMetricRow(
                    title: "Sharpe Ratio",
                    value: "\(String(format: "%.2f", analytics.sharpeRatio))",
                    color: analytics.sharpeRatio > 1 ? .green : analytics.sharpeRatio > 0 ? .orange : .red
                )

                PerformanceMetricRow(
                    title: "Win Rate",
                    value: "\(String(format: "%.1f", analytics.winRate))%",
                    color: analytics.winRate > 60 ? .green : analytics.winRate > 40 ? .orange : .red
                )

                PerformanceMetricRow(
                    title: "Avg Holding Period",
                    value: "\(String(format: "%.0f", analytics.averageHoldingPeriod)) days",
                    color: .blue
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Performance Metric Row
struct PerformanceMetricRow: View {
    let title: String
    let value: String
    let color: Color

    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)

            Spacer()

            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Additional Analytics Components

// MARK: - Concentration Risk Card
struct ConcentrationRiskCard: View {
    let analytics: PortfolioAnalytics

    var body: some View {
        VStack(spacing: 16) {
            Text("Concentration Risk")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            if let largestPosition = analytics.largestPosition {
                VStack(spacing: 12) {
                    HStack {
                        Text("Largest Position")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        Spacer()
                        Text(largestPosition.symbol)
                            .font(.subheadline)
                            .fontWeight(.semibold)
                    }

                    let percentage = analytics.totalValue > 0 ? (largestPosition.marketValue / analytics.totalValue) * 100 : 0

                    VStack(spacing: 4) {
                        HStack {
                            Text("Portfolio Weight")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text("\(String(format: "%.1f", percentage))%")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(percentage > 20 ? .red : percentage > 10 ? .orange : .green)
                        }

                        ProgressView(value: percentage / 100)
                            .progressViewStyle(LinearProgressViewStyle(tint: percentage > 20 ? .red : percentage > 10 ? .orange : .green))
                            .scaleEffect(y: 2)
                    }

                    Text(percentage > 20 ? "High concentration risk - consider diversifying" : percentage > 10 ? "Moderate concentration - monitor position size" : "Good diversification")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            } else {
                Text("No position data available")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Risk Recommendations Card
struct RiskRecommendationsCard: View {
    let risk: RiskMetrics
    let analytics: PortfolioAnalytics

    private var recommendations: [String] {
        var recs: [String] = []

        if risk.beta > 1.5 {
            recs.append("Consider reducing your largest position to below 20% of portfolio")
        }

        if analytics.diversificationScore < 50 {
            recs.append("Increase diversification across more sectors and asset classes")
        }

        if risk.beta > 1.5 {
            recs.append("Your portfolio is highly correlated with market movements")
        }

        if risk.volatility > 0.3 {
            recs.append("Consider adding some defensive positions to reduce overall risk")
        }

        if recs.isEmpty {
            recs.append("Your portfolio has a good risk profile")
        }

        return recs
    }

    var body: some View {
        VStack(spacing: 16) {
            Text("Risk Recommendations")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 12) {
                ForEach(Array(recommendations.enumerated()), id: \.offset) { index, recommendation in
                    HStack(alignment: .top, spacing: 12) {
                        Image(systemName: "lightbulb.fill")
                            .font(.caption)
                            .foregroundColor(.orange)
                            .frame(width: 16, height: 16)

                        Text(recommendation)
                            .font(.caption)
                            .foregroundColor(.primary)
                            .multilineTextAlignment(.leading)

                        Spacer()
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Sector Performance Card
struct SectorPerformanceCard: View {
    let sectors: SectorAnalysis

    var body: some View {
        VStack(spacing: 16) {
            Text("Sector Performance")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 8) {
                ForEach(sectors.allocations.prefix(5), id: \.id) { allocation in
                    HStack {
                        HStack(spacing: 8) {
                            Image(systemName: allocation.sectorIcon)
                                .font(.caption)
                                .foregroundColor(.purple)

                            Text(allocation.sector)
                                .font(.caption)
                                .fontWeight(.medium)
                        }

                        Spacer()

                        Text("\(allocation.performance >= 0 ? "+" : "")\(String(format: "%.1f", allocation.performance))%")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(allocation.performance >= 0 ? .green : .red)
                    }
                    .padding(.vertical, 4)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Diversification Score Card
struct DiversificationScoreCard: View {
    let sectors: SectorAnalysis

    var body: some View {
        VStack(spacing: 16) {
            Text("Diversification Analysis")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 12) {
                HStack {
                    Text("Diversification Score")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(String(format: "%.0f", sectors.diversificationScore))/100")
                        .font(.title3)
                        .fontWeight(.bold)
                        .foregroundColor(sectors.isDiversified ? .green : .orange)
                }

                ProgressView(value: sectors.diversificationScore / 100)
                    .progressViewStyle(LinearProgressViewStyle(tint: sectors.isDiversified ? .green : .orange))
                    .scaleEffect(y: 2)

                VStack(spacing: 8) {
                    HStack {
                        Text("Most Allocated")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Spacer()
                        Text(sectors.mostAllocatedSector)
                            .font(.caption)
                            .fontWeight(.medium)
                    }

                    HStack {
                        Text("Least Allocated")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        Spacer()
                        Text(sectors.leastAllocatedSector)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                }

                Text(sectors.diversificationRecommendation)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Sector Recommendations Card
struct SectorRecommendationsCard: View {
    let sectors: SectorAnalysis

    var body: some View {
        VStack(spacing: 16) {
            Text("Sector Recommendations")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 12) {
                RecommendationRow(
                    icon: "chart.pie.fill",
                    title: "Diversification",
                    description: sectors.diversificationRecommendation
                )

                if !sectors.isDiversified {
                    RecommendationRow(
                        icon: "exclamationmark.triangle.fill",
                        title: "Risk Warning",
                        description: "Consider reducing concentration in \(sectors.mostAllocatedSector)"
                    )
                }

                RecommendationRow(
                    icon: "lightbulb.fill",
                    title: "Opportunity",
                    description: "Consider adding exposure to \(sectors.leastAllocatedSector) sector"
                )
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - AI Insights Card
struct AIInsightsCard: View {
    @State private var insights: [AnalyticsInsight] = []

    var body: some View {
        VStack(spacing: 16) {
            Text("AI-Generated Insights")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            if insights.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "brain.head.profile")
                        .font(.title2)
                        .foregroundColor(.purple)

                    Text("AI is analyzing your portfolio...")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding()
            } else {
                VStack(spacing: 12) {
                    ForEach(insights) { insight in
                        InsightCard(insight: insight, disclosureLevel: .full)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
        .onAppear {
            generateMockInsights()
        }
    }

    private func generateMockInsights() {
        insights = [
            AnalyticsInsight(
                title: "Strong Performance",
                description: "Your portfolio is outperforming the S&P 500 by 3.2% this quarter",
                type: .marketTrend,
                confidence: 0.85,
                buffettQuote: "Time is the friend of the wonderful business.",
                actionable: false,
                priority: .medium
            ),
            AnalyticsInsight(
                title: "Concentration Risk",
                description: "Consider reducing your largest position to improve diversification",
                type: .riskAssessment,
                confidence: 0.92,
                buffettQuote: "Risk comes from not knowing what you're doing.",
                actionable: true,
                priority: .high
            )
        ]
    }
}

// MARK: - Action Items Card
struct ActionItemsCard: View {
    private let actionItems = [
        "Rebalance portfolio to target allocation",
        "Review and update stop-loss orders",
        "Consider tax-loss harvesting opportunities",
        "Evaluate underperforming positions"
    ]

    var body: some View {
        VStack(spacing: 16) {
            Text("Recommended Actions")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(spacing: 12) {
                ForEach(Array(actionItems.enumerated()), id: \.offset) { index, item in
                    ActionItemRow(item: item, isCompleted: false)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Recent News Card
struct RecentNewsCard: View {
    let sentiment: MarketSentiment

    var body: some View {
        VStack(spacing: 16) {
            Text("Recent News")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            if sentiment.newsItems.isEmpty {
                Text("No recent news available")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding()
            } else {
                VStack(spacing: 12) {
                    ForEach(sentiment.newsItems.prefix(3)) { newsItem in
                        NewsItemRow(newsItem: newsItem)
                    }
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Returns Distribution Card
struct ReturnsDistributionCard: View {
    let analytics: PortfolioAnalytics

    var body: some View {
        VStack(spacing: 16) {
            Text("Returns Distribution")
                .font(.headline)
                .fontWeight(.semibold)
                .frame(maxWidth: .infinity, alignment: .leading)

            // Simplified returns distribution visualization
            VStack(spacing: 12) {
                HStack {
                    Text("Positive Returns")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(String(format: "%.1f", analytics.winRate))%")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }

                ProgressView(value: analytics.winRate / 100)
                    .progressViewStyle(LinearProgressViewStyle(tint: .green))
                    .scaleEffect(y: 2)

                HStack {
                    Text("Average Return")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(String(format: "%.2f", analytics.totalReturn))%")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(analytics.totalReturn >= 0 ? .green : .red)
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Supporting Components

struct RecommendationRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(.orange)
                .frame(width: 16, height: 16)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .fontWeight(.semibold)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
            }

            Spacer()
        }
    }
}

// Using InsightCard from AnalyticsContextualComponents.swift

struct ActionItemRow: View {
    let item: String
    @State var isCompleted: Bool

    var body: some View {
        HStack(spacing: 12) {
            Button(action: {
                isCompleted.toggle()
            }) {
                Image(systemName: isCompleted ? "checkmark.circle.fill" : "circle")
                    .font(.title3)
                    .foregroundColor(isCompleted ? .green : .gray)
            }

            Text(item)
                .font(.subheadline)
                .foregroundColor(isCompleted ? .secondary : .primary)
                .strikethrough(isCompleted)

            Spacer()
        }
    }
}

struct NewsItemRow: View {
    let newsItem: SentimentNewsItem

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(newsItem.symbol)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.purple)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        RoundedRectangle(cornerRadius: 6)
                            .fill(Color.purple.opacity(0.1))
                    )

                Spacer()

                Text(newsItem.sentimentLabel)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(Color(newsItem.sentimentColor))
            }

            Text(newsItem.headline)
                .font(.caption)
                .foregroundColor(.primary)
                .lineLimit(2)

            HStack {
                Text(newsItem.source)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()

                Text(newsItem.timestamp, style: .relative)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
        )
    }
}
