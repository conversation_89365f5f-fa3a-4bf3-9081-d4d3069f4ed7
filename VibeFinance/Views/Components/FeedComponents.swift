//
//  FeedComponents.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

// MARK: - Feed Action Types
enum FeedAction {
    case like
    case bookmark
    case share
    case invest
}

enum FeedFilter: String, CaseIterable {
    case all = "All"
    case stocks = "Stocks"
    case crypto = "Crypto"
    case news = "News"
    case education = "Education"
    case bookmarked = "Bookmarked"
    
    var icon: String {
        switch self {
        case .all: return "house.fill"
        case .stocks: return "chart.line.uptrend.xyaxis"
        case .crypto: return "bitcoinsign.circle"
        case .news: return "newspaper"
        case .education: return "graduationcap"
        case .bookmarked: return "bookmark.fill"
        }
    }
}

// MARK: - Feed Header View
struct FeedHeaderView: View {
    @Binding var showingFilters: Bool
    @Binding var selectedFilter: FeedFilter
    
    var body: some View {
        VStack(spacing: 16) {
            // Welcome Message
            HStack {
                VStack(alignment: .leading) {
                    Text("Good morning! 🌅")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text("Here's what's happening in finance")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                Spacer()
                Button(action: { showingFilters = true }) {
                    Image(systemName: "line.3.horizontal.decrease.circle")
                        .font(.title2)
                        .foregroundColor(.purple)
                }
            }
            
            // Filter Chips
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(FeedFilter.allCases, id: \.self) { filter in
                        FilterChip(
                            filter: filter,
                            isSelected: selectedFilter == filter
                        ) {
                            selectedFilter = filter
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}

// MARK: - Filter Chip
struct FilterChip: View {
    let filter: FeedFilter
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: filter.icon)
                    .font(.caption)
                Text(filter.rawValue)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(isSelected ? Color.purple : Color(.systemGray6))
            )
            .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Feed Item Card
struct FeedItemCard: View {
    let feedItem: FeedItem
    let onAction: (FeedAction) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with category and time
            HStack {
                CategoryBadge(category: feedItem.content.category)
                Spacer()
                Text(feedItem.timeAgo)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // Content
            VStack(alignment: .leading, spacing: 8) {
                Text(feedItem.content.summary)
                    .font(.body)
                    .lineLimit(nil)
                
                // Tags
                if !feedItem.content.tags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 8) {
                            ForEach(feedItem.content.tags.prefix(3), id: \.self) { tag in
                                Text("#\(tag)")
                                    .font(.caption)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(Color.purple.opacity(0.1))
                                    )
                                    .foregroundColor(.purple)
                            }
                        }
                        .padding(.horizontal)
                    }
                }
            }
            
            // Investment Suggestion
            if let suggestion = feedItem.content.investmentSuggestion {
                InvestmentSuggestionCard(suggestion: suggestion) {
                    onAction(.invest)
                }
            }
            
            // Actions
            HStack(spacing: 20) {
                FeedActionButton(
                    icon: "heart",
                    fillIcon: "heart.fill",
                    count: feedItem.getReactionCount(for: .like),
                    isActive: feedItem.reactions.contains { $0.type == .like }
                ) {
                    onAction(.like)
                }
                
                FeedActionButton(
                    icon: "bookmark",
                    fillIcon: "bookmark.fill",
                    isActive: feedItem.isBookmarked
                ) {
                    onAction(.bookmark)
                }
                
                FeedActionButton(
                    icon: "square.and.arrow.up",
                    fillIcon: "square.and.arrow.up"
                ) {
                    onAction(.share)
                }
                
                Spacer()
                
                // Vibe Score
                VibeScoreView(score: feedItem.content.vibeScore)
            }
            
            // Citation
            CitationView(citation: feedItem.content.citation)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Category Badge
struct CategoryBadge: View {
    let category: FeedCategory
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: category.icon)
                .font(.caption)
            Text(category.displayName)
                .font(.caption)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(category.color.opacity(0.2))
        )
        .foregroundColor(category.color)
    }
}

// MARK: - Investment Suggestion Card
struct InvestmentSuggestionCard: View {
    let suggestion: InvestmentSuggestion
    let onInvest: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)
                Text("Investment Opportunity")
                    .font(.caption)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            Text(suggestion.description)
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Suggested Amount")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    Text("$\(suggestion.suggestedAmount, specifier: "%.0f")")
                        .font(.caption)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                Button(action: onInvest) {
                    Text("Invest Now")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.green)
                        )
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Feed Action Button
struct FeedActionButton: View {
    let icon: String
    let fillIcon: String
    var count: Int = 0
    let isActive: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 4) {
                Image(systemName: isActive ? fillIcon : icon)
                    .font(.caption)
                    .foregroundColor(isActive ? .purple : .secondary)
                
                if count > 0 {
                    Text("\(count)")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Vibe Score View
struct VibeScoreView: View {
    let score: Int
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: "flame.fill")
                .font(.caption)
                .foregroundColor(scoreColor)
            Text("\(score)")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(scoreColor)
        }
    }
    
    private var scoreColor: Color {
        switch score {
        case 80...100: return .red
        case 60...79: return .orange
        case 40...59: return .yellow
        default: return .gray
        }
    }
}

// MARK: - Citation View
struct CitationView: View {
    let citation: Citation
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: "link.circle.fill")
                .font(.caption)
                .foregroundColor(.blue)
            
            Text("Source: \(citation.source)")
                .font(.caption2)
                .foregroundColor(.secondary)
            
            Spacer()
            
            if let publishedAt = citation.publishedAt {
                Text(publishedAt, style: .relative)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
}

// MARK: - Feed Loading View
struct FeedLoadingView: View {
    var body: some View {
        VStack(spacing: 20) {
            ForEach(0..<3, id: \.self) { _ in
                FeedItemSkeleton()
            }
        }
    }
}

// MARK: - Feed Item Skeleton
struct FeedItemSkeleton: View {
    @State private var isAnimating = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color(.systemGray5))
                    .frame(width: 60, height: 20)
                Spacer()
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color(.systemGray5))
                    .frame(width: 40, height: 12)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                ForEach(0..<3, id: \.self) { _ in
                    RoundedRectangle(cornerRadius: 4)
                        .fill(Color(.systemGray5))
                        .frame(height: 12)
                }
            }
            
            HStack {
                ForEach(0..<4, id: \.self) { _ in
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(.systemGray5))
                        .frame(width: 30, height: 20)
                }
                Spacer()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .opacity(isAnimating ? 0.5 : 1.0)
        .animation(
            Animation.easeInOut(duration: 1.0).repeatForever(autoreverses: true),
            value: isAnimating
        )
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - Load More Button
struct LoadMoreButton: View {
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: "arrow.down.circle")
                Text("Load More")
            }
            .font(.subheadline)
            .fontWeight(.medium)
            .foregroundColor(.purple)
            .padding()
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.purple, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}
