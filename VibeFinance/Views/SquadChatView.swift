//
//  SquadChatView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct SquadChatView: View {
    let squad: Squad
    @EnvironmentObject var squadManager: SquadManager
    @EnvironmentObject var userManager: UserManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var messages: [SquadMessage] = []
    @State private var newMessage = ""
    @State private var showingProposalSheet = false
    @State private var isLoading = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Chat Header
            SquadChatHeader(squad: squad) {
                showingProposalSheet = true
            }
            
            // Messages List
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(messages) { message in
                            SquadMessageView(
                                message: message,
                                isCurrentUser: message.userID == userManager.user?.id
                            )
                        }
                        
                        if isLoading {
                            ProgressView()
                                .padding()
                        }
                    }
                    .padding()
                }
                .onChange(of: messages.count) { _ in
                    withAnimation {
                        proxy.scrollTo(messages.last?.id, anchor: .bottom)
                    }
                }
            }
            
            // Message Input
            SquadMessageInput(
                newMessage: $newMessage,
                onSend: sendMessage,
                onProposal: { showingProposalSheet = true }
            )
        }
        .navigationTitle(squad.name)
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden()
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("Back") {
                    dismiss()
                }
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingProposalSheet = true }) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.purple)
                }
            }
        }
        .sheet(isPresented: $showingProposalSheet) {
            CreateInvestmentProposalView(squad: squad)
        }
        .onAppear {
            loadMessages()
        }
    }
    
    private func sendMessage() {
        guard !newMessage.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty,
              let user = userManager.user else { return }
        
        let message = SquadMessage(
            squadID: squad.id,
            userID: user.id,
            username: user.username,
            content: newMessage,
            type: .text,
            timestamp: Date()
        )
        
        messages.append(message)
        newMessage = ""
        
        // Save to database
        Task {
            await saveMessage(message)
        }
    }
    
    private func loadMessages() {
        // Load messages from database
        // For now, add some sample messages
        messages = [
            SquadMessage(
                squadID: squad.id,
                userID: UUID(),
                username: "Alex",
                content: "Hey everyone! What do you think about investing in renewable energy stocks?",
                type: .text,
                timestamp: Date().addingTimeInterval(-3600)
            ),
            SquadMessage(
                squadID: squad.id,
                userID: UUID(),
                username: "Sarah",
                content: "I love the idea! Tesla and other clean energy companies have been performing well.",
                type: .text,
                timestamp: Date().addingTimeInterval(-3000)
            ),
            SquadMessage(
                squadID: squad.id,
                userID: userManager.user?.id ?? UUID(),
                username: userManager.user?.username ?? "You",
                content: "Should we create a proposal to vote on it?",
                type: .text,
                timestamp: Date().addingTimeInterval(-1800)
            )
        ]
    }
    
    private func saveMessage(_ message: SquadMessage) async {
        // Save message to database
        // Implementation would use SupabaseService
    }
}

// MARK: - Squad Chat Header
struct SquadChatHeader: View {
    let squad: Squad
    let onCreateProposal: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                Text(squad.emoji)
                    .font(.title)
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(Color.purple.opacity(0.1))
                    )
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(squad.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    Text("\(squad.memberCount) members • Active")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Button(action: onCreateProposal) {
                    HStack(spacing: 4) {
                        Image(systemName: "plus")
                        Text("Propose")
                    }
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color.purple)
                    )
                }
            }
            
            // Squad Stats
            HStack(spacing: 20) {
                SquadStatItem(
                    icon: "dollarsign.circle.fill",
                    title: "Total Value",
                    value: "$12.5K",
                    color: .green
                )
                
                SquadStatItem(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Return",
                    value: "+8.4%",
                    color: .blue
                )
                
                SquadStatItem(
                    icon: "person.3.fill",
                    title: "Members",
                    value: "\(squad.memberCount)",
                    color: .purple
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }
}

// MARK: - Squad Stat Item
struct SquadStatItem: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .font(.caption)
                .foregroundColor(color)
            
            Text(value)
                .font(.caption)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - Squad Message View
struct SquadMessageView: View {
    let message: SquadMessage
    let isCurrentUser: Bool
    
    var body: some View {
        HStack {
            if isCurrentUser {
                Spacer()
            }
            
            VStack(alignment: isCurrentUser ? .trailing : .leading, spacing: 4) {
                if !isCurrentUser {
                    Text(message.username)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.purple)
                }
                
                switch message.type {
                case .text:
                    Text(message.content)
                        .font(.body)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 8)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(isCurrentUser ? Color.purple : Color(.systemGray5))
                        )
                        .foregroundColor(isCurrentUser ? .white : .primary)
                        
                case .proposal:
                    InvestmentProposalMessageView(message: message)
                        
                case .system:
                    SystemMessageView(message: message)
                }
                
                Text(message.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity * 0.75, alignment: isCurrentUser ? .trailing : .leading)
            
            if !isCurrentUser {
                Spacer()
            }
        }
    }
}

// MARK: - Investment Proposal Message View
struct InvestmentProposalMessageView: View {
    let message: SquadMessage
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.yellow)
                Text("Investment Proposal")
                    .font(.caption)
                    .fontWeight(.semibold)
                Spacer()
            }
            
            Text(message.content)
                .font(.body)
            
            // Voting Buttons
            HStack(spacing: 12) {
                VoteButton(
                    icon: "hand.thumbsup.fill",
                    title: "Yes",
                    count: 5,
                    color: .green
                ) {
                    // Handle yes vote
                }
                
                VoteButton(
                    icon: "hand.thumbsdown.fill",
                    title: "No",
                    count: 1,
                    color: .red
                ) {
                    // Handle no vote
                }
                
                Spacer()
                
                Text("6/10 votes")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.blue.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - Vote Button
struct VoteButton: View {
    let icon: String
    let title: String
    let count: Int
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.caption)
                Text("\(count)")
                    .font(.caption)
                    .fontWeight(.semibold)
            }
            .foregroundColor(color)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(color.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - System Message View
struct SystemMessageView: View {
    let message: SquadMessage
    
    var body: some View {
        HStack {
            Spacer()
            Text(message.content)
                .font(.caption)
                .foregroundColor(.secondary)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
            Spacer()
        }
    }
}

// MARK: - Squad Message Input
struct SquadMessageInput: View {
    @Binding var newMessage: String
    let onSend: () -> Void
    let onProposal: () -> Void
    
    var body: some View {
        VStack(spacing: 12) {
            // Quick Actions
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    QuickActionButton(
                        icon: "lightbulb.fill",
                        title: "Propose Investment",
                        color: .yellow,
                        action: onProposal
                    )
                    
                    QuickActionButton(
                        icon: "chart.line.uptrend.xyaxis",
                        title: "Share Analysis",
                        color: .blue,
                        action: {
                            newMessage = "Check out this market analysis: "
                        }
                    )
                    
                    QuickActionButton(
                        icon: "newspaper",
                        title: "Share News",
                        color: .green,
                        action: {
                            newMessage = "Interesting news: "
                        }
                    )
                }
                .padding(.horizontal)
            }
            
            // Message Input
            HStack(spacing: 12) {
                TextField("Type a message...", text: $newMessage, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(1...4)
                
                Button(action: onSend) {
                    Image(systemName: "paperplane.fill")
                        .foregroundColor(.white)
                        .frame(width: 36, height: 36)
                        .background(
                            Circle()
                                .fill(newMessage.isEmpty ? Color.gray : Color.purple)
                        )
                }
                .disabled(newMessage.isEmpty)
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
}

// MARK: - Quick Action Button
struct QuickActionButton: View {
    let icon: String
    let title: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.caption)
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(color)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(color.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Supporting Types
struct SquadMessage: Identifiable {
    let id = UUID()
    let squadID: UUID
    let userID: UUID
    let username: String
    let content: String
    let type: MessageType
    let timestamp: Date
}





struct InvestmentProposal: Identifiable {
    let id = UUID()
    let squadID: UUID
    let creatorID: UUID
    let title: String
    let description: String
    let symbol: String
    let amount: Double
    let createdAt: Date
    var votesFor: Int = 0
    var votesAgainst: Int = 0
    var isActive: Bool = true
}

struct Vote {
    let proposalID: UUID
    let userID: UUID
    let voteType: VoteType
    let timestamp: Date
}

struct SquadMembership {
    let squadID: UUID
    let userID: UUID
    let role: MemberRole
    let joinedAt: Date
}

enum MemberRole: String {
    case admin = "admin"
    case member = "member"
}

#Preview {
    SquadChatView(
        squad: Squad(
            name: "Tech Titans",
            description: "Investing in the future of technology",
            emoji: "💻",
            creatorID: UUID(),
            isPublic: true,
            maxMembers: 50
        )
    )
    .environmentObject(SquadManager())
    .environmentObject(UserManager())
}
