//
//  QuestDetailView.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI

struct QuestDetailView: View {
    let quest: Quest
    @EnvironmentObject var questManager: QuestManager
    @EnvironmentObject var userManager: UserManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var currentTaskIndex = 0
    @State private var showingCompletion = false
    @State private var userAnswers: [String: String] = [:]
    
    var currentTask: QuestTask? {
        guard currentTaskIndex < quest.tasks.count else { return nil }
        return quest.tasks[currentTaskIndex]
    }
    
    var progress: Double {
        guard !quest.tasks.isEmpty else { return 0 }
        return Double(currentTaskIndex) / Double(quest.tasks.count)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Progress Header
                QuestProgressHeader(
                    quest: quest,
                    progress: progress,
                    currentTask: currentTaskIndex + 1,
                    totalTasks: quest.tasks.count
                )
                
                // Task Content
                ScrollView {
                    VStack(spacing: 24) {
                        if let task = currentTask {
                            QuestTaskView(
                                task: task,
                                userAnswer: Binding(
                                    get: { userAnswers[task.id.uuidString] ?? "" },
                                    set: { userAnswers[task.id.uuidString] = $0 }
                                )
                            )
                        } else {
                            QuestCompletionView(quest: quest)
                        }
                    }
                    .padding()
                }
                
                // Action Buttons
                VStack(spacing: 12) {
                    if currentTask != nil {
                        HStack(spacing: 16) {
                            if currentTaskIndex > 0 {
                                Button("Previous") {
                                    withAnimation {
                                        currentTaskIndex -= 1
                                    }
                                }
                                .buttonStyle(SecondaryButtonStyle())
                            }
                            
                            Button(currentTaskIndex == quest.tasks.count - 1 ? "Complete Quest" : "Next") {
                                handleNextAction()
                            }
                            .buttonStyle(PrimaryButtonStyle())
                            .disabled(!canProceed)
                        }
                    } else {
                        Button("Claim Rewards") {
                            claimRewards()
                        }
                        .buttonStyle(PrimaryButtonStyle())
                    }
                }
                .padding()
                .background(Color(.systemBackground))
            }
            .navigationTitle(quest.title)
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden()
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingCompletion) {
            QuestCompletionCelebrationView(quest: quest)
        }
    }
    
    private var canProceed: Bool {
        guard let task = currentTask else { return false }
        
        switch task.type {
        case .multipleChoice:
            return userAnswers[task.id.uuidString] != nil
        case .reading:
            return true // Can always proceed from reading tasks
        case .video:
            return true // Assume video is watched
        case .research, .simulation:
            return userAnswers[task.id.uuidString]?.isEmpty == false
        case .calculation:
            return userAnswers[task.id.uuidString] != nil
        case .reflection:
            return userAnswers[task.id.uuidString]?.isEmpty == false
        }
    }
    
    private func handleNextAction() {
        // Save current answer
        if let task = currentTask {
            Task {
                await questManager.saveTaskProgress(
                    questID: quest.id,
                    taskID: task.id,
                    answer: userAnswers[task.id.uuidString] ?? ""
                )
            }
        }
        
        if currentTaskIndex == quest.tasks.count - 1 {
            // Complete quest
            completeQuest()
        } else {
            // Move to next task
            withAnimation {
                currentTaskIndex += 1
            }
        }
    }
    
    private func completeQuest() {
        Task {
            await questManager.completeQuest(quest.id)
            await MainActor.run {
                showingCompletion = true
            }
        }
    }
    
    private func claimRewards() {
        Task {
            await questManager.claimQuestRewards(quest.id)
            await userManager.addXP(quest.xpReward)
            dismiss()
        }
    }
}

// MARK: - Quest Progress Header
struct QuestProgressHeader: View {
    let quest: Quest
    let progress: Double
    let currentTask: Int
    let totalTasks: Int
    
    var body: some View {
        VStack(spacing: 16) {
            // Progress Bar
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("Progress")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                    Text("\(currentTask)/\(totalTasks)")
                        .font(.caption)
                        .fontWeight(.medium)
                }
                
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle(tint: .purple))
                    .scaleEffect(y: 2)
            }
            
            // Quest Info
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(quest.category.displayName)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(Color(quest.category.color).opacity(0.2))
                        )
                        .foregroundColor(Color(quest.category.color))
                    
                    Text(quest.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    HStack(spacing: 4) {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                        Text("\(quest.xpReward) XP")
                            .fontWeight(.semibold)
                    }
                    .font(.caption)
                    
                    Text("\(quest.estimatedTime) min")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }
}

// MARK: - Quest Task View
struct QuestTaskView: View {
    let task: QuestTask
    @Binding var userAnswer: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Task Header
            VStack(alignment: .leading, spacing: 8) {
                Text(task.title)
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(task.description)
                    .font(.body)
                    .foregroundColor(.secondary)
            }
            
            // Task Content
            switch task.type {
            case .multipleChoice:
                MultipleChoiceTaskView(task: task, selectedAnswer: $userAnswer)
            case .calculation:
                TrueFalseTaskView(task: task, selectedAnswer: $userAnswer)
            case .reading:
                ReadingTaskView(task: task)
            case .video:
                VideoTaskView(task: task)
            case .research:
                ResearchTaskView(task: task, userInput: $userAnswer)
            case .simulation:
                SimulationTaskView(task: task, userInput: $userAnswer)
            case .reflection:
                ReflectionTaskView(task: task, userInput: $userAnswer)
            }
            
            // XP Reward
            HStack {
                Image(systemName: "star.fill")
                    .foregroundColor(.yellow)
                Text("+\(task.xpReward) XP")
                    .fontWeight(.semibold)
                Spacer()
            }
            .font(.caption)
            .padding(.top)
        }
    }
}

// MARK: - Multiple Choice Task View
struct MultipleChoiceTaskView: View {
    let task: QuestTask
    @Binding var selectedAnswer: String
    
    private let options = [
        "A) Stocks represent ownership in a company",
        "B) Stocks are loans to companies",
        "C) Stocks are government bonds",
        "D) Stocks are cryptocurrency"
    ]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Choose the correct answer:")
                .font(.headline)
                .fontWeight(.semibold)
            
            ForEach(options, id: \.self) { option in
                Button(action: {
                    selectedAnswer = option
                }) {
                    HStack {
                        Image(systemName: selectedAnswer == option ? "checkmark.circle.fill" : "circle")
                            .foregroundColor(selectedAnswer == option ? .purple : .gray)
                        
                        Text(option)
                            .foregroundColor(.primary)
                        
                        Spacer()
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(selectedAnswer == option ? Color.purple : Color.gray.opacity(0.3), lineWidth: 2)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}

// MARK: - True/False Task View
struct TrueFalseTaskView: View {
    let task: QuestTask
    @Binding var selectedAnswer: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("True or False:")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text("Diversification helps reduce investment risk.")
                .font(.body)
                .padding()
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color(.systemGray6))
                )
            
            HStack(spacing: 16) {
                Button(action: {
                    selectedAnswer = "True"
                }) {
                    HStack {
                        Image(systemName: selectedAnswer == "True" ? "checkmark.circle.fill" : "circle")
                        Text("True")
                    }
                    .foregroundColor(selectedAnswer == "True" ? .green : .primary)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(selectedAnswer == "True" ? Color.green : Color.gray.opacity(0.3), lineWidth: 2)
                    )
                }
                .buttonStyle(PlainButtonStyle())
                
                Button(action: {
                    selectedAnswer = "False"
                }) {
                    HStack {
                        Image(systemName: selectedAnswer == "False" ? "checkmark.circle.fill" : "circle")
                        Text("False")
                    }
                    .foregroundColor(selectedAnswer == "False" ? .red : .primary)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(selectedAnswer == "False" ? Color.red : Color.gray.opacity(0.3), lineWidth: 2)
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}

// MARK: - Reading Task View
struct ReadingTaskView: View {
    let task: QuestTask
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("📚 Educational Content")
                .font(.headline)
                .fontWeight(.semibold)
            
            ScrollView {
                VStack(alignment: .leading, spacing: 12) {
                    Text("Understanding Stock Market Basics")
                        .font(.title3)
                        .fontWeight(.semibold)
                    
                    Text("""
                    The stock market is where investors buy and sell shares of publicly traded companies. When you buy a stock, you're purchasing a small piece of ownership in that company.
                    
                    Key concepts to understand:
                    
                    • **Shares**: Units of ownership in a company
                    • **Dividends**: Payments companies make to shareholders
                    • **Market Cap**: Total value of a company's shares
                    • **P/E Ratio**: Price-to-earnings ratio, a valuation metric
                    
                    Remember: Investing always involves risk, but education and diversification can help manage that risk.
                    """)
                        .font(.body)
                        .lineSpacing(4)
                }
            }
            .frame(maxHeight: 300)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
        }
    }
}

// MARK: - Video Task View
struct VideoTaskView: View {
    let task: QuestTask
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("🎥 Educational Video")
                .font(.headline)
                .fontWeight(.semibold)
            
            // Video Placeholder
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.black)
                .frame(height: 200)
                .overlay(
                    VStack {
                        Image(systemName: "play.circle.fill")
                            .font(.system(size: 50))
                            .foregroundColor(.white)
                        Text("Introduction to ETFs")
                            .font(.headline)
                            .foregroundColor(.white)
                        Text("5:30")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                )
        }
    }
}

// MARK: - Research Task View
struct ResearchTaskView: View {
    let task: QuestTask
    @Binding var userInput: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("🔍 Research Task")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text("Research Apple Inc. (AAPL) and write a brief analysis:")
                .font(.body)
            
            TextEditor(text: $userInput)
                .frame(minHeight: 120)
                .padding(8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
                .overlay(
                    Text(userInput.isEmpty ? "Enter your analysis here..." : "")
                        .foregroundColor(.gray)
                        .allowsHitTesting(false)
                        .padding(.leading, 12)
                        .padding(.top, 16),
                    alignment: .topLeading
                )
        }
    }
}

// MARK: - Simulation Task View
struct SimulationTaskView: View {
    let task: QuestTask
    @Binding var userInput: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("🎮 Investment Simulation")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text("You have $1,000 to invest. Choose your allocation:")
                .font(.body)
            
            VStack(spacing: 12) {
                AllocationSlider(title: "Stocks", percentage: .constant(60))
                AllocationSlider(title: "Bonds", percentage: .constant(30))
                AllocationSlider(title: "Cash", percentage: .constant(10))
            }
            
            Button("Confirm Allocation") {
                userInput = "Stocks: 60%, Bonds: 30%, Cash: 10%"
            }
            .buttonStyle(SecondaryButtonStyle())
        }
    }
}

// MARK: - Allocation Slider
struct AllocationSlider: View {
    let title: String
    @Binding var percentage: Double
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Spacer()
                Text("\(Int(percentage))%")
                    .font(.subheadline)
                    .fontWeight(.semibold)
            }
            
            Slider(value: $percentage, in: 0...100, step: 5)
                .accentColor(.purple)
        }
    }
}

// MARK: - Button Styles
struct PrimaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.headline)
            .fontWeight(.semibold)
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.purple)
                    .opacity(configuration.isPressed ? 0.8 : 1.0)
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
    }
}

struct SecondaryButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .font(.headline)
            .fontWeight(.semibold)
            .foregroundColor(.purple)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.purple, lineWidth: 2)
                    .opacity(configuration.isPressed ? 0.8 : 1.0)
            )
            .scaleEffect(configuration.isPressed ? 0.98 : 1.0)
    }
}

#Preview {
    QuestDetailView(
        quest: Quest(
            title: "Stock Market Basics",
            description: "Learn the fundamentals of investing",
            category: .stocks,
            difficulty: .beginner,
            xpReward: 100,
            estimatedTime: 15,
            tasks: []
        )
    )
    .environmentObject(QuestManager())
    .environmentObject(UserManager())
}

// MARK: - Missing Task Views
struct ReflectionTaskView: View {
    let task: QuestTask
    @Binding var userInput: String

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Reflection")
                .font(.headline)
                .fontWeight(.semibold)

            Text(task.data?.question ?? "Reflect on what you've learned")
                .font(.body)
                .foregroundColor(.secondary)

            TextField("Your thoughts...", text: $userInput, axis: .vertical)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .lineLimit(5...10)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemGray6))
        )
    }
}
