//
//  BuildVerification.swift
//  VibeFinance - Build Success Verification
//
//  Created by MAGESH DHANASEKARAN on 12/26/24.
//  Simple verification that the app builds successfully
//

import Foundation
import SwiftUI

/// Build verification utilities
struct BuildVerification {
    
    /// Verify all core components can be instantiated
    static func verifyBuild() -> Bool {
        do {
            // Test core managers
            let _ = SimpleAppleIntelligenceManager.shared
            let _ = UserManager()
            let _ = AuthManager()
            let _ = FeedManager()
            let _ = QuestManager()
            let _ = SquadManager()
            
            // Test data models
            let _ = Portfolio.mockPortfolio()
            let _ = User(email: "<EMAIL>", username: "test", preferences: UserPreferences(), subscriptionStatus: .free)
            
            // Test views can be created
            let _ = SimpleAIChatView()
            let _ = SimplePortfolioAnalysisView()
            let _ = MainTabView()
            
            print("✅ Build verification successful - all components instantiated")
            return true
            
        } catch {
            print("❌ Build verification failed: \(error)")
            return false
        }
    }
    
    /// Test AI functionality
    static func testAIFunctionality() async -> Bool {
        let ai = SimpleAppleIntelligenceManager.shared
        
        do {
            // Test portfolio analysis
            let portfolio = Portfolio.mockPortfolio()
            let analysis = await ai.analyzePortfolio(portfolio)
            guard !analysis.isEmpty else {
                print("❌ Portfolio analysis failed")
                return false
            }
            
            // Test financial advice
            let advice = await ai.generateFinancialAdvice(for: "How should I invest?")
            guard !advice.isEmpty else {
                print("❌ Financial advice failed")
                return false
            }
            
            // Test quest generation
            let quest = await ai.generateQuest(for: 3)
            guard !quest.title.isEmpty else {
                print("❌ Quest generation failed")
                return false
            }
            
            print("✅ AI functionality test successful")
            return true
            
        } catch {
            print("❌ AI functionality test failed: \(error)")
            return false
        }
    }
    
    /// Run all verification tests
    static func runAllTests() async -> Bool {
        print("🔨 Running VibeFinance Build Verification...")
        
        let buildSuccess = verifyBuild()
        let aiSuccess = await testAIFunctionality()
        
        let overallSuccess = buildSuccess && aiSuccess
        
        if overallSuccess {
            print("🎉 All verification tests passed! Build is successful.")
        } else {
            print("❌ Some verification tests failed.")
        }
        
        return overallSuccess
    }
}

/// Simple build status view
struct BuildStatusView: View {
    @State private var buildStatus: BuildStatus = .unknown
    @State private var isRunningTests = false
    
    enum BuildStatus {
        case unknown
        case testing
        case success
        case failure
        
        var color: Color {
            switch self {
            case .unknown: return .gray
            case .testing: return .blue
            case .success: return .green
            case .failure: return .red
            }
        }
        
        var icon: String {
            switch self {
            case .unknown: return "questionmark.circle"
            case .testing: return "gear.circle"
            case .success: return "checkmark.circle.fill"
            case .failure: return "xmark.circle.fill"
            }
        }
        
        var message: String {
            switch self {
            case .unknown: return "Ready to test build"
            case .testing: return "Running build verification..."
            case .success: return "Build successful! ✅"
            case .failure: return "Build failed ❌"
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 30) {
            // Header
            VStack(spacing: 12) {
                Text("VibeFinance")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Build Verification")
                    .font(.title2)
                    .foregroundColor(.secondary)
            }
            
            // Status
            VStack(spacing: 16) {
                Image(systemName: buildStatus.icon)
                    .font(.system(size: 60))
                    .foregroundColor(buildStatus.color)
                
                Text(buildStatus.message)
                    .font(.headline)
                    .foregroundColor(buildStatus.color)
            }
            
            // Test Button
            Button(action: runBuildTest) {
                HStack {
                    if isRunningTests {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                    Text(isRunningTests ? "Testing..." : "Run Build Test")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .frame(maxWidth: .infinity)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(isRunningTests ? Color.gray : Color.blue)
                )
            }
            .disabled(isRunningTests)
            
            // Features
            if buildStatus == .success {
                VStack(alignment: .leading, spacing: 8) {
                    Text("✅ Core App Components")
                    Text("✅ AI Assistant Functionality")
                    Text("✅ Portfolio Analysis")
                    Text("✅ Quest Generation")
                    Text("✅ User Interface")
                }
                .font(.subheadline)
                .foregroundColor(.green)
            }
            
            Spacer()
        }
        .padding()
    }
    
    private func runBuildTest() {
        isRunningTests = true
        buildStatus = .testing
        
        Task {
            let success = await BuildVerification.runAllTests()
            
            await MainActor.run {
                buildStatus = success ? .success : .failure
                isRunningTests = false
            }
        }
    }
}

#Preview {
    BuildStatusView()
}
