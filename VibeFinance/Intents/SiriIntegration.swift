//
//  SiriIntegration.swift
//  VibeFinance - Siri Integration and Voice Commands
//
//  Created by MAGESH DHANASEKARAN on 7/2/25.
//

import AppIntents
import Foundation
import Intents

// MARK: - <PERSON><PERSON> Voice Commands Configuration

@available(iOS 16.0, *)
struct VibeFinanceSiriCommands {
    
    // MARK: - Portfolio Commands
    static let portfolioCommands = [
        "Hey Sir<PERSON>, check my portfolio in VibeFinance",
        "Hey Sir<PERSON>, what's my portfolio worth",
        "Hey Sir<PERSON>, show my investments",
        "Hey Sir<PERSON>, how are my stocks doing",
        "Hey Sir<PERSON>, portfolio performance in VibeFinance"
    ]
    
    // MARK: - AI Advisor Commands
    static let aiAdvisorCommands = [
        "Hey Sir<PERSON>, ask <PERSON> about investing",
        "Hey Sir<PERSON>, get investment advice from VibeFinance",
        "Hey <PERSON><PERSON>, talk to <PERSON> about risk",
        "Hey Sir<PERSON>, ask <PERSON> about growth stocks",
        "Hey Sir<PERSON>, get financial advice"
    ]
    
    // MARK: - Market Commands
    static let marketCommands = [
        "Hey Sir<PERSON>, check the market",
        "Hey Sir<PERSON>, how's the stock market today",
        "Hey Sir<PERSON>, market status in VibeFinance",
        "Hey Sir<PERSON>, check S&P 500",
        "Hey Sir<PERSON>, what's the market doing"
    ]
    
    // MARK: - Stock Price Commands
    static let stockCommands = [
        "Hey Siri, check Apple stock price",
        "Hey Siri, what's Tesla trading at",
        "Hey Siri, Google stock price",
        "Hey Siri, Microsoft stock in VibeFinance",
        "Hey Siri, check NVIDIA price"
    ]
    
    // MARK: - Learning Commands
    static let learningCommands = [
        "Hey Siri, start learning in VibeFinance",
        "Hey Siri, show my quests",
        "Hey Siri, what can I learn today",
        "Hey Siri, continue my financial education",
        "Hey Siri, check my learning progress"
    ]
}

// MARK: - Voice Response Templates

struct SiriResponseTemplates {
    
    // MARK: - Portfolio Responses
    static func portfolioResponse(value: Double, change: Double, changePercent: Double) -> String {
        let changeText = change >= 0 ? "up" : "down"
        let absChange = abs(change)
        let absChangePercent = abs(changePercent)
        
        return """
        Your portfolio is worth $\(String(format: "%.2f", value)). \
        It's \(changeText) $\(String(format: "%.2f", absChange)) today, \
        which is \(String(format: "%.2f", absChangePercent)) percent.
        """
    }
    
    // MARK: - Stock Price Responses
    static func stockPriceResponse(symbol: String, price: Double, change: Double, changePercent: Double) -> String {
        let direction = change >= 0 ? "up" : "down"
        let absChange = abs(change)
        let absChangePercent = abs(changePercent)
        
        return """
        \(symbol) is trading at $\(String(format: "%.2f", price)). \
        It's \(direction) $\(String(format: "%.2f", absChange)) today, \
        or \(String(format: "%.2f", absChangePercent)) percent.
        """
    }
    
    // MARK: - Market Responses
    static func marketResponse(status: String, spyChange: Double, nasdaqChange: Double) -> String {
        let spyDirection = spyChange >= 0 ? "up" : "down"
        let nasdaqDirection = nasdaqChange >= 0 ? "up" : "down"
        
        return """
        The market is \(status). The S&P 500 is \(spyDirection) \(String(format: "%.2f", abs(spyChange))) percent. \
        The NASDAQ is \(nasdaqDirection) \(String(format: "%.2f", abs(nasdaqChange))) percent.
        """
    }
    
    // MARK: - AI Advice Responses
    static func aiAdviceResponse(advisor: String, advice: String) -> String {
        return "\(advisor) says: \(advice)"
    }
    
    // MARK: - Quest Responses
    static func questResponse(activeQuests: Int, completedQuests: Int, totalXP: Int) -> String {
        if activeQuests == 0 {
            return """
            You have no active quests right now. You've completed \(completedQuests) quests \
            and earned \(totalXP) experience points total. Ready to start a new quest?
            """
        } else {
            return """
            You have \(activeQuests) active quest\(activeQuests == 1 ? "" : "s"). \
            You've completed \(completedQuests) quests and earned \(totalXP) XP total.
            """
        }
    }
}

// MARK: - Siri Shortcuts Configuration

struct SiriShortcutsConfiguration {
    
    // MARK: - Suggested Shortcuts
    static let suggestedShortcuts = [
        SiriShortcut(
            phrase: "Check my investments",
            intent: CheckPortfolioIntent(),
            category: .portfolio
        ),
        SiriShortcut(
            phrase: "Get investment advice",
            intent: GetAIAdviceIntent(),
            category: .aiAdvice
        ),
        SiriShortcut(
            phrase: "Check the market",
            intent: CheckMarketIntent(),
            category: .market
        ),
        SiriShortcut(
            phrase: "Show my learning progress",
            intent: ViewQuestsIntent(),
            category: .learning
        )
    ]
    
    // MARK: - Quick Actions
    static let quickActions = [
        QuickAction(
            title: "Portfolio Value",
            subtitle: "Check current portfolio worth",
            intent: CheckPortfolioIntent(),
            systemImage: "chart.pie.fill"
        ),
        QuickAction(
            title: "AI Advice",
            subtitle: "Get investment guidance",
            intent: GetAIAdviceIntent(),
            systemImage: "brain.head.profile"
        ),
        QuickAction(
            title: "Market Status",
            subtitle: "Current market overview",
            intent: CheckMarketIntent(),
            systemImage: "chart.line.uptrend.xyaxis"
        ),
        QuickAction(
            title: "Learning Quests",
            subtitle: "View educational progress",
            intent: ViewQuestsIntent(),
            systemImage: "target"
        )
    ]
}

// MARK: - Supporting Structures

struct SiriShortcut {
    let phrase: String
    let intent: any AppIntent
    let category: ShortcutCategory
}

// Using QuickAction from AppleIntelligenceViews.swift

enum ShortcutCategory {
    case portfolio
    case aiAdvice
    case market
    case learning
    case trading
    case analysis
}

// MARK: - Voice Command Parser

class VoiceCommandParser {
    
    // MARK: - Command Recognition
    static func parseCommand(_ command: String) -> ParsedCommand? {
        let lowercased = command.lowercased()
        
        // Portfolio commands
        if lowercased.contains("portfolio") || lowercased.contains("investments") {
            return ParsedCommand(type: .portfolio, parameters: [:])
        }
        
        // Stock price commands
        if let stockSymbol = extractStockSymbol(from: lowercased) {
            return ParsedCommand(type: .stockPrice, parameters: ["symbol": stockSymbol])
        }
        
        // Market commands
        if lowercased.contains("market") || lowercased.contains("s&p") || lowercased.contains("nasdaq") {
            return ParsedCommand(type: .market, parameters: [:])
        }
        
        // AI advice commands
        if lowercased.contains("advice") || lowercased.contains("warren") || lowercased.contains("buffett") {
            let advisor = extractAdvisor(from: lowercased)
            return ParsedCommand(type: .aiAdvice, parameters: ["advisor": advisor])
        }
        
        // Learning commands
        if lowercased.contains("quest") || lowercased.contains("learn") || lowercased.contains("education") {
            return ParsedCommand(type: .learning, parameters: [:])
        }
        
        return nil
    }
    
    // MARK: - Helper Methods
    private static func extractStockSymbol(from command: String) -> String? {
        let stockMappings = [
            "apple": "AAPL",
            "tesla": "TSLA",
            "google": "GOOGL",
            "microsoft": "MSFT",
            "amazon": "AMZN",
            "nvidia": "NVDA",
            "meta": "META",
            "netflix": "NFLX"
        ]
        
        for (company, symbol) in stockMappings {
            if command.contains(company) {
                return symbol
            }
        }
        
        return nil
    }
    
    private static func extractAdvisor(from command: String) -> String {
        if command.contains("warren") || command.contains("buffett") {
            return "Warren Buffett"
        } else if command.contains("ray") || command.contains("dalio") {
            return "Ray Dalio"
        } else if command.contains("peter") || command.contains("lynch") {
            return "Peter Lynch"
        } else if command.contains("benjamin") || command.contains("graham") {
            return "Benjamin Graham"
        } else if command.contains("john") || command.contains("bogle") {
            return "John Bogle"
        } else if command.contains("cathie") || command.contains("wood") {
            return "Cathie Wood"
        }
        
        return "Warren Buffett" // Default
    }
}

struct ParsedCommand {
    let type: CommandType
    let parameters: [String: String]
}

enum CommandType {
    case portfolio
    case stockPrice
    case market
    case aiAdvice
    case learning
    case analysis
}

// MARK: - Siri Integration Manager

@MainActor
class SiriIntegrationManager: ObservableObject {
    static let shared = SiriIntegrationManager()
    
    @Published var isVoiceEnabled = true
    @Published var preferredVoiceSpeed: Double = 1.0
    @Published var enableHapticFeedback = true
    
    private init() {}
    
    // MARK: - Voice Settings
    func updateVoiceSettings(enabled: Bool, speed: Double, haptics: Bool) {
        isVoiceEnabled = enabled
        preferredVoiceSpeed = speed
        enableHapticFeedback = haptics
        
        // Save to UserDefaults
        UserDefaults.standard.set(enabled, forKey: "siri_voice_enabled")
        UserDefaults.standard.set(speed, forKey: "siri_voice_speed")
        UserDefaults.standard.set(haptics, forKey: "siri_haptic_feedback")
    }
    
    // MARK: - Shortcut Management
    func donateShortcut(for intent: any AppIntent, phrase: String) {
        // Donate the shortcut to Siri for learning
        let shortcut = INShortcut(intent: intent as! INIntent)
        let voiceShortcut = INVoiceShortcut(shortcut: shortcut, invocationPhrase: phrase)
        
        // In production, this would use INVoiceShortcutCenter
        print("Donated shortcut: \(phrase)")
    }
    
    // MARK: - Analytics
    func trackVoiceCommand(_ command: String, success: Bool) {
        // Track voice command usage for analytics
        let parameters = [
            "command": command,
            "success": success,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        // Send to analytics service
        print("Voice command tracked: \(parameters)")
    }
}
