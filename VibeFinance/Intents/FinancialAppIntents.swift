//
//  FinancialAppIntents.swift
//  VibeFinance - Apple Intelligence Powered
//
//  Created by MAGESH DHANASEKARAN on 12/26/24.
//  App Intents for Siri and Shortcuts integration
//

import Foundation
import AppIntents
import SwiftUI

// MARK: - Portfolio Analysis Intent
struct CheckPortfolioIntent: AppIntent {
    static var title: LocalizedStringResource = "Check Portfolio"
    static var description = IntentDescription("Get an AI-powered analysis of your investment portfolio")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Analysis Type")
    var analysisType: AnalysisType?
    
    static var parameterSummary: some ParameterSummary {
        Summary("Check my portfolio \(\.$analysisType)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let aiManager = AppleIntelligenceManager.shared
        
        // Get user's portfolio (mock data for demo)
        let portfolio = await getUserPortfolio()
        let context = await getUserContext()
        
        // Perform AI analysis
        let analysis = try await aiManager.analyzePortfolio(portfolio, context: context)
        
        let dialog = IntentDialog(stringLiteral: """
        Your portfolio has a risk score of \(analysis.riskScore)/10. 
        \(analysis.summary)
        """)
        
        return .result(
            dialog: dialog,
            view: PortfolioAnalysisSnippet(analysis: analysis)
        )
    }
    
    private func getUserPortfolio() async -> Portfolio {
        // In real implementation, fetch from user's actual portfolio
        return Portfolio.mockPortfolio()
    }
    
    private func getUserContext() async -> UserContext {
        // In real implementation, fetch user's actual context
        return UserContext.mockContext()
    }
}

// MARK: - Investment Advice Intent
struct GetInvestmentAdviceIntent: AppIntent {
    static var title: LocalizedStringResource = "Get Investment Advice"
    static var description = IntentDescription("Get personalized investment recommendations from AI")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Investment Amount")
    var amount: Double?
    
    @Parameter(title: "Risk Level")
    var riskLevel: RiskLevel?
    
    static var parameterSummary: some ParameterSummary {
        Summary("Get investment advice for \(\.$amount) with \(\.$riskLevel) risk")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let aiManager = AppleIntelligenceManager.shared
        
        // Create investment query context
        let query = InvestmentQuery(
            amount: amount ?? 1000,
            riskLevel: riskLevel ?? .moderate,
            timeHorizon: .longTerm
        )
        
        // Get AI recommendations
        let recommendations = try await getInvestmentRecommendations(query: query)
        
        let dialog = IntentDialog(stringLiteral: """
        Based on your profile, I recommend investing in \(recommendations.first?.symbol ?? "diversified ETFs"). 
        Expected return: \(recommendations.first?.expectedReturn ?? 8)% annually.
        """)
        
        return .result(dialog: dialog)
    }
    
    private func getInvestmentRecommendations(query: InvestmentQuery) async throws -> [InvestmentRecommendation] {
        // Mock implementation
        return [
            InvestmentRecommendation(
                symbol: "VTI",
                name: "Vanguard Total Stock Market ETF",
                expectedReturn: 8.5,
                riskLevel: .moderate,
                reasoning: "Broad market exposure with low fees"
            )
        ]
    }
}

// MARK: - Stock Analysis Intent
struct AnalyzeStockIntent: AppIntent {
    static var title: LocalizedStringResource = "Analyze Stock"
    static var description = IntentDescription("Get AI analysis of a specific stock")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Stock Symbol")
    var symbol: String
    
    static var parameterSummary: some ParameterSummary {
        Summary("Analyze \(\.$symbol) stock")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let aiManager = AppleIntelligenceManager.shared
        
        // Get stock analysis
        let analysis = try await analyzeStock(symbol: symbol)
        
        let dialog = IntentDialog(stringLiteral: """
        \(symbol) is currently \(analysis.recommendation.rawValue). 
        Price: $\(analysis.currentPrice), Target: $\(analysis.targetPrice)
        """)
        
        return .result(
            dialog: dialog,
            view: StockAnalysisSnippet(analysis: analysis)
        )
    }
    
    private func analyzeStock(symbol: String) async throws -> StockAnalysis {
        // Mock implementation
        return StockAnalysis(
            symbol: symbol,
            currentPrice: 150.0,
            targetPrice: 165.0,
            recommendation: .buy,
            confidence: 0.85
        )
    }
}

// MARK: - Financial Goal Intent
struct CreateFinancialGoalIntent: AppIntent {
    static var title: LocalizedStringResource = "Create Financial Goal"
    static var description = IntentDescription("Set up a new financial goal with AI guidance")
    static var openAppWhenRun: Bool = true
    
    @Parameter(title: "Goal Type")
    var goalType: GoalType
    
    @Parameter(title: "Target Amount")
    var targetAmount: Double
    
    @Parameter(title: "Time Frame")
    var timeFrame: TimeFrame
    
    static var parameterSummary: some ParameterSummary {
        Summary("Create a \(\.$goalType) goal of \(\.$targetAmount) in \(\.$timeFrame)")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog {
        let aiManager = AppleIntelligenceManager.shared
        
        // Create goal with AI recommendations
        let goal = FinancialGoal(
            type: goalType,
            targetAmount: targetAmount,
            timeFrame: timeFrame,
            createdAt: Date()
        )
        
        // Get AI-powered savings plan
        let savingsPlan = try await generateSavingsPlan(for: goal)
        
        let dialog = IntentDialog(stringLiteral: """
        Goal created! To reach $\(targetAmount) in \(timeFrame.displayName), 
        save $\(savingsPlan.monthlyAmount) per month.
        """)
        
        return .result(dialog: dialog)
    }
    
    private func generateSavingsPlan(for goal: FinancialGoal) async throws -> SavingsPlan {
        // Mock implementation
        let months = goal.timeFrame.months
        let monthlyAmount = goal.targetAmount / Double(months)
        
        return SavingsPlan(
            goal: goal,
            monthlyAmount: monthlyAmount,
            recommendedInvestments: ["VTI", "VXUS", "BND"]
        )
    }
}

// MARK: - Market Summary Intent
struct GetMarketSummaryIntent: AppIntent {
    static var title: LocalizedStringResource = "Get Market Summary"
    static var description = IntentDescription("Get AI-powered market summary and insights")
    static var openAppWhenRun: Bool = false
    
    @Parameter(title: "Market Focus")
    var marketFocus: MarketFocus?
    
    static var parameterSummary: some ParameterSummary {
        Summary("Get \(\.$marketFocus) market summary")
    }
    
    func perform() async throws -> some IntentResult & ProvidesDialog & ShowsSnippetView {
        let aiManager = AppleIntelligenceManager.shared
        
        // Get AI market analysis
        let summary = try await getMarketSummary(focus: marketFocus ?? .general)
        
        let dialog = IntentDialog(stringLiteral: """
        Market Update: \(summary.headline)
        S&P 500: \(summary.spyChange > 0 ? "+" : "")\(summary.spyChange)%
        """)
        
        return .result(
            dialog: dialog,
            view: MarketSummarySnippet(summary: summary)
        )
    }
    
    private func getMarketSummary(focus: MarketFocus) async throws -> MarketSummary {
        // Mock implementation
        return MarketSummary(
            headline: "Markets mixed as investors await Fed decision",
            spyChange: 0.25,
            sentiment: .neutral,
            keyEvents: ["Fed meeting", "Earnings season"]
        )
    }
}

// MARK: - Supporting Types

enum AnalysisType: String, AppEnum {
    case overview = "overview"
    case risk = "risk"
    case performance = "performance"
    case diversification = "diversification"
    
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Analysis Type")
    static var caseDisplayRepresentations: [AnalysisType: DisplayRepresentation] = [
        .overview: "Overview",
        .risk: "Risk Analysis",
        .performance: "Performance",
        .diversification: "Diversification"
    ]
}

enum RiskLevel: String, AppEnum {
    case conservative = "conservative"
    case moderate = "moderate"
    case aggressive = "aggressive"
    
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Risk Level")
    static var caseDisplayRepresentations: [RiskLevel: DisplayRepresentation] = [
        .conservative: "Conservative",
        .moderate: "Moderate",
        .aggressive: "Aggressive"
    ]
}

enum GoalType: String, AppEnum {
    case retirement = "retirement"
    case house = "house"
    case vacation = "vacation"
    case emergency = "emergency"
    case education = "education"
    
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Goal Type")
    static var caseDisplayRepresentations: [GoalType: DisplayRepresentation] = [
        .retirement: "Retirement",
        .house: "House Down Payment",
        .vacation: "Vacation",
        .emergency: "Emergency Fund",
        .education: "Education"
    ]
}

enum TimeFrame: String, AppEnum {
    case oneYear = "1year"
    case threeYears = "3years"
    case fiveYears = "5years"
    case tenYears = "10years"
    
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Time Frame")
    static var caseDisplayRepresentations: [TimeFrame: DisplayRepresentation] = [
        .oneYear: "1 Year",
        .threeYears: "3 Years",
        .fiveYears: "5 Years",
        .tenYears: "10 Years"
    ]
    
    var months: Int {
        switch self {
        case .oneYear: return 12
        case .threeYears: return 36
        case .fiveYears: return 60
        case .tenYears: return 120
        }
    }
    
    var displayName: String {
        switch self {
        case .oneYear: return "1 year"
        case .threeYears: return "3 years"
        case .fiveYears: return "5 years"
        case .tenYears: return "10 years"
        }
    }
}

enum MarketFocus: String, AppEnum {
    case general = "general"
    case stocks = "stocks"
    case crypto = "crypto"
    case bonds = "bonds"
    
    static var typeDisplayRepresentation = TypeDisplayRepresentation(name: "Market Focus")
    static var caseDisplayRepresentations: [MarketFocus: DisplayRepresentation] = [
        .general: "General Market",
        .stocks: "Stock Market",
        .crypto: "Cryptocurrency",
        .bonds: "Bond Market"
    ]
}

// MARK: - Data Models

struct InvestmentQuery {
    let amount: Double
    let riskLevel: RiskLevel
    let timeHorizon: TimeFrame
}

struct InvestmentRecommendation {
    let symbol: String
    let name: String
    let expectedReturn: Double
    let riskLevel: RiskLevel
    let reasoning: String
}

struct StockAnalysis {
    let symbol: String
    let currentPrice: Double
    let targetPrice: Double
    let recommendation: StockRecommendation
    let confidence: Double
}

enum StockRecommendation: String {
    case buy = "Buy"
    case hold = "Hold"
    case sell = "Sell"
}

struct FinancialGoal {
    let type: GoalType
    let targetAmount: Double
    let timeFrame: TimeFrame
    let createdAt: Date
}

struct SavingsPlan {
    let goal: FinancialGoal
    let monthlyAmount: Double
    let recommendedInvestments: [String]
}

struct MarketSummary {
    let headline: String
    let spyChange: Double
    let sentiment: MarketSentiment
    let keyEvents: [String]
}

enum MarketSentiment {
    case bullish
    case neutral
    case bearish
}

// MARK: - Snippet Views

struct PortfolioAnalysisSnippet: View {
    let analysis: PortfolioAnalysis
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "chart.pie.fill")
                    .foregroundColor(.purple)
                Text("Portfolio Analysis")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Risk Score")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(analysis.riskScore)/10")
                        .font(.title2)
                        .fontWeight(.bold)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Diversification")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("\(analysis.diversificationScore)/10")
                        .font(.title2)
                        .fontWeight(.bold)
                }
            }
            
            Text(analysis.summary)
                .font(.caption)
                .foregroundColor(.secondary)
                .lineLimit(2)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct StockAnalysisSnippet: View {
    let analysis: StockAnalysis
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundColor(.green)
                Text(analysis.symbol)
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
                Text(analysis.recommendation.rawValue)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(recommendationColor)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(recommendationColor.opacity(0.1))
                    .cornerRadius(6)
            }
            
            HStack {
                VStack(alignment: .leading) {
                    Text("Current")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("$\(analysis.currentPrice, specifier: "%.2f")")
                        .font(.title3)
                        .fontWeight(.semibold)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("Target")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("$\(analysis.targetPrice, specifier: "%.2f")")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var recommendationColor: Color {
        switch analysis.recommendation {
        case .buy: return .green
        case .hold: return .orange
        case .sell: return .red
        }
    }
}

struct MarketSummarySnippet: View {
    let summary: MarketSummary
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "globe")
                    .foregroundColor(.blue)
                Text("Market Summary")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            Text(summary.headline)
                .font(.subheadline)
                .fontWeight(.medium)
            
            HStack {
                Text("S&P 500")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text("\(summary.spyChange > 0 ? "+" : "")\(summary.spyChange, specifier: "%.2f")%")
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(summary.spyChange > 0 ? .green : .red)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}
