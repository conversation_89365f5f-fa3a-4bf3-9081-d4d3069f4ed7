//
//  TradingSafetySystem.swift
//  VibeFinance - Trading Safety & Risk Management System
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

// MARK: - Trading Safety Models

struct SafeTradeOrder {
    let id = UUID()
    let symbol: String
    let companyName: String
    let orderType: OrderType
    let quantity: Int
    let price: Double
    let estimatedTotal: Double
    let riskLevel: RiskLevel
    let warrenAnalysis: WarrenAnalysis
    
    enum OrderType {
        case buy, sell
        
        var title: String {
            switch self {
            case .buy: return "Buy"
            case .sell: return "Sell"
            }
        }
        
        var color: Color {
            switch self {
            case .buy: return .green
            case .sell: return .red
            }
        }
    }
    
    enum RiskLevel {
        case low, medium, high
        
        var title: String {
            switch self {
            case .low: return "Low Risk"
            case .medium: return "Medium Risk"
            case .high: return "High Risk"
            }
        }
        
        var color: Color {
            switch self {
            case .low: return .green
            case .medium: return .orange
            case .high: return .red
            }
        }
        
        var icon: String {
            switch self {
            case .low: return "shield.checkered"
            case .medium: return "exclamationmark.triangle"
            case .high: return "exclamationmark.octagon"
            }
        }
    }
}

struct WarrenAnalysis {
    let recommendation: String
    let reasoning: String
    let buffettWisdom: String
    let confidence: Double // 0.0 to 1.0
    let timeHorizon: String
}

struct RiskDisclosure {
    let title: String
    let description: String
    let severity: RiskSeverity
    
    enum RiskSeverity {
        case info, warning, critical
        
        var color: Color {
            switch self {
            case .info: return .blue
            case .warning: return .orange
            case .critical: return .red
            }
        }
        
        var icon: String {
            switch self {
            case .info: return "info.circle"
            case .warning: return "exclamationmark.triangle"
            case .critical: return "exclamationmark.octagon"
            }
        }
    }
}

// MARK: - Trading Confirmation Flow

struct TradingConfirmationView: View {
    let order: SafeTradeOrder
    let onConfirm: () -> Void
    let onCancel: () -> Void
    
    @State private var hasReadDisclosures = false
    @State private var hasConfirmedRisk = false
    @State private var showingRiskDetails = false
    @State private var currentStep = 1
    
    private let totalSteps = 3
    
    private let riskDisclosures = [
        RiskDisclosure(
            title: "Market Risk",
            description: "Stock prices can go down as well as up. You may lose some or all of your investment.",
            severity: .warning
        ),
        RiskDisclosure(
            title: "Volatility Risk",
            description: "This stock has shown high price volatility. Consider your risk tolerance.",
            severity: .warning
        ),
        RiskDisclosure(
            title: "Concentration Risk",
            description: "This purchase will increase your exposure to this sector. Diversification is key.",
            severity: .info
        )
    ]
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Progress Indicator
                TradingProgressIndicator(currentStep: currentStep, totalSteps: totalSteps)
                    .padding(.horizontal, 16)
                    .padding(.top, 8)
                
                ScrollView {
                    VStack(spacing: 20) {
                        switch currentStep {
                        case 1:
                            OrderReviewStep(order: order)
                        case 2:
                            RiskDisclosureStep(
                                disclosures: riskDisclosures,
                                hasReadDisclosures: $hasReadDisclosures
                            )
                        case 3:
                            WarrenAnalysisStep(
                                analysis: order.warrenAnalysis,
                                hasConfirmedRisk: $hasConfirmedRisk
                            )
                        default:
                            EmptyView()
                        }
                    }
                    .padding(16)
                }
                
                // Action Buttons
                TradingActionButtons(
                    currentStep: currentStep,
                    totalSteps: totalSteps,
                    canProceed: canProceedToNextStep,
                    onNext: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            if currentStep < totalSteps {
                                currentStep += 1
                            } else {
                                onConfirm()
                            }
                        }
                    },
                    onCancel: onCancel
                )
            }
            .navigationTitle("Confirm \(order.orderType.title)")
            .navigationBarTitleDisplayMode(.inline)
            .background(VibeFinanceDesignSystem.Colors.primaryGradient)
        }
    }
    
    private var canProceedToNextStep: Bool {
        switch currentStep {
        case 1: return true
        case 2: return hasReadDisclosures
        case 3: return hasConfirmedRisk
        default: return false
        }
    }
}

struct TradingProgressIndicator: View {
    let currentStep: Int
    let totalSteps: Int
    
    var body: some View {
        HStack(spacing: 8) {
            ForEach(1...totalSteps, id: \.self) { step in
                Circle()
                    .fill(step <= currentStep ? VibeFinanceDesignSystem.Colors.accentGold : Color.white.opacity(0.3))
                    .frame(width: 12, height: 12)
                    .overlay(
                        Text("\(step)")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(step <= currentStep ? .black : .white)
                    )
                
                if step < totalSteps {
                    Rectangle()
                        .fill(step < currentStep ? VibeFinanceDesignSystem.Colors.accentGold : Color.white.opacity(0.3))
                        .frame(height: 2)
                        .frame(maxWidth: .infinity)
                }
            }
        }
        .padding(.vertical, 12)
    }
}

struct OrderReviewStep: View {
    let order: SafeTradeOrder
    
    var body: some View {
        VStack(spacing: 16) {
            // Order Summary Card
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("Order Summary")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Text(order.orderType.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(order.orderType.color)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(order.orderType.color.opacity(0.2))
                        )
                }
                
                Divider()
                    .background(Color.white.opacity(0.3))
                
                VStack(spacing: 8) {
                    OrderDetailRow(title: "Company", value: order.companyName)
                    OrderDetailRow(title: "Symbol", value: order.symbol)
                    OrderDetailRow(title: "Quantity", value: "\(order.quantity) shares")
                    OrderDetailRow(title: "Price per share", value: String(format: "$%.2f", order.price))
                    
                    Divider()
                        .background(Color.white.opacity(0.3))
                    
                    OrderDetailRow(
                        title: "Estimated Total",
                        value: String(format: "$%.2f", order.estimatedTotal),
                        isTotal: true
                    )
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.1))
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
            )
            
            // Risk Level Indicator
            HStack {
                Image(systemName: order.riskLevel.icon)
                    .font(.title3)
                    .foregroundColor(order.riskLevel.color)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("Risk Assessment")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))
                    
                    Text(order.riskLevel.title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(order.riskLevel.color)
                }
                
                Spacer()
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(order.riskLevel.color.opacity(0.1))
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(order.riskLevel.color.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
}

struct OrderDetailRow: View {
    let title: String
    let value: String
    let isTotal: Bool
    
    init(title: String, value: String, isTotal: Bool = false) {
        self.title = title
        self.value = value
        self.isTotal = isTotal
    }
    
    var body: some View {
        HStack {
            Text(title)
                .font(isTotal ? .subheadline : .body)
                .fontWeight(isTotal ? .semibold : .regular)
                .foregroundColor(.white.opacity(0.8))
            
            Spacer()
            
            Text(value)
                .font(isTotal ? .subheadline : .body)
                .fontWeight(isTotal ? .bold : .medium)
                .foregroundColor(isTotal ? VibeFinanceDesignSystem.Colors.accentGold : .white)
        }
    }
}

struct RiskDisclosureStep: View {
    let disclosures: [RiskDisclosure]
    @Binding var hasReadDisclosures: Bool
    
    var body: some View {
        VStack(spacing: 16) {
            VStack(spacing: 8) {
                Text("⚠️")
                    .font(.system(size: 40))
                
                Text("Important Risk Disclosures")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                
                Text("Please read and understand these risks before proceeding")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            
            VStack(spacing: 12) {
                ForEach(disclosures, id: \.title) { disclosure in
                    RiskDisclosureCard(disclosure: disclosure)
                }
            }
            
            // Acknowledgment
            Button(action: {
                hasReadDisclosures.toggle()
            }) {
                HStack(spacing: 12) {
                    Image(systemName: hasReadDisclosures ? "checkmark.square.fill" : "square")
                        .font(.title3)
                        .foregroundColor(hasReadDisclosures ? VibeFinanceDesignSystem.Colors.accentGold : .white.opacity(0.6))
                    
                    Text("I have read and understand these risk disclosures")
                        .font(.subheadline)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                }
            }
            .buttonStyle(PlainButtonStyle())
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.1))
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                hasReadDisclosures ? VibeFinanceDesignSystem.Colors.accentGold : Color.white.opacity(0.3),
                                lineWidth: 1
                            )
                    )
            )
        }
    }
}

struct RiskDisclosureCard: View {
    let disclosure: RiskDisclosure
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: disclosure.severity.icon)
                .font(.title3)
                .foregroundColor(disclosure.severity.color)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(disclosure.title)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Text(disclosure.description)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.leading)
            }
            
            Spacer()
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(disclosure.severity.color.opacity(0.1))
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(disclosure.severity.color.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

struct WarrenAnalysisStep: View {
    let analysis: WarrenAnalysis
    @Binding var hasConfirmedRisk: Bool

    var body: some View {
        VStack(spacing: 16) {
            VStack(spacing: 8) {
                Text("🧠")
                    .font(.system(size: 40))

                Text("Warren's Analysis")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.white)

                Text("AI-powered investment wisdom")
                    .font(.subheadline)
                    .foregroundColor(.white.opacity(0.8))
            }

            // Analysis Card
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("Recommendation")
                        .font(.headline)
                        .foregroundColor(.white)

                    Spacer()

                    // Confidence Indicator
                    HStack(spacing: 4) {
                        ForEach(0..<5) { index in
                            Circle()
                                .fill(
                                    Double(index) < analysis.confidence * 5 ?
                                    VibeFinanceDesignSystem.Colors.accentGold :
                                    Color.white.opacity(0.3)
                                )
                                .frame(width: 8, height: 8)
                        }

                        Text("\(Int(analysis.confidence * 100))%")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }
                }

                Text(analysis.recommendation)
                    .font(.subheadline)
                    .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                    .fontWeight(.semibold)

                Divider()
                    .background(Color.white.opacity(0.3))

                Text("Reasoning")
                    .font(.headline)
                    .foregroundColor(.white)

                Text(analysis.reasoning)
                    .font(.body)
                    .foregroundColor(.white.opacity(0.9))
                    .multilineTextAlignment(.leading)

                Divider()
                    .background(Color.white.opacity(0.3))

                Text("Warren's Wisdom")
                    .font(.headline)
                    .foregroundColor(.white)

                Text("💡 \"\(analysis.buffettWisdom)\"")
                    .font(.body)
                    .italic()
                    .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                    .multilineTextAlignment(.leading)

                HStack {
                    Text("Time Horizon:")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.8))

                    Text(analysis.timeHorizon)
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)

                    Spacer()
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color.white.opacity(0.1))
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )
            )

            // Final Confirmation
            Button(action: {
                hasConfirmedRisk.toggle()
            }) {
                HStack(spacing: 12) {
                    Image(systemName: hasConfirmedRisk ? "checkmark.square.fill" : "square")
                        .font(.title3)
                        .foregroundColor(hasConfirmedRisk ? VibeFinanceDesignSystem.Colors.accentGold : .white.opacity(0.6))

                    VStack(alignment: .leading, spacing: 2) {
                        Text("I understand the risks and Warren's analysis")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)

                        Text("I confirm this trade aligns with my investment strategy")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }

                    Spacer()
                }
            }
            .buttonStyle(PlainButtonStyle())
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.1))
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                hasConfirmedRisk ? VibeFinanceDesignSystem.Colors.accentGold : Color.white.opacity(0.3),
                                lineWidth: 1
                            )
                    )
            )
        }
    }
}

struct TradingActionButtons: View {
    let currentStep: Int
    let totalSteps: Int
    let canProceed: Bool
    let onNext: () -> Void
    let onCancel: () -> Void

    var body: some View {
        VStack(spacing: 12) {
            HStack(spacing: 16) {
                // Cancel Button
                Button(action: onCancel) {
                    Text("Cancel")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.white.opacity(0.2))
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(Color.white.opacity(0.4), lineWidth: 1)
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())

                // Next/Confirm Button
                Button(action: onNext) {
                    Text(currentStep == totalSteps ? "Confirm Trade" : "Continue")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.black)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(
                                    canProceed ?
                                    VibeFinanceDesignSystem.Colors.accentGold :
                                    Color.gray.opacity(0.5)
                                )
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .disabled(!canProceed)
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 8)

            if currentStep == totalSteps {
                Text("By confirming, you agree to execute this trade")
                    .font(.caption2)
                    .foregroundColor(.white.opacity(0.6))
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 16)
                    .padding(.bottom, 8)
            }
        }
        .background(
            Rectangle()
                .fill(VibeFinanceDesignSystem.Colors.primaryBlue.opacity(0.9))
                .blur(radius: 20)
        )
    }
}

// MARK: - Trading Safety Extensions

extension View {
    func tradingSafetyConfirmation(
        order: SafeTradeOrder?,
        isPresented: Binding<Bool>,
        onConfirm: @escaping () -> Void
    ) -> some View {
        self.sheet(isPresented: isPresented) {
            if let order = order {
                TradingConfirmationView(
                    order: order,
                    onConfirm: {
                        onConfirm()
                        isPresented.wrappedValue = false
                    },
                    onCancel: {
                        isPresented.wrappedValue = false
                    }
                )
            }
        }
    }
}
