//
//  AccessibilitySystem.swift
//  VibeFinance - WCAG 2.1 AA Compliance & VoiceOver Optimization
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI

// MARK: - Accessibility Manager

class AccessibilityManager: ObservableObject {
    @Published var isVoiceOverEnabled = false
    @Published var preferredContentSizeCategory: ContentSizeCategory = .medium
    @Published var isReduceMotionEnabled = false
    @Published var isHighContrastEnabled = false
    
    init() {
        updateAccessibilitySettings()
        
        // Listen for accessibility changes
        NotificationCenter.default.addObserver(
            forName: UIAccessibility.voiceOverStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { _ in
            self.updateAccessibilitySettings()
        }
        
        NotificationCenter.default.addObserver(
            forName: UIContentSizeCategory.didChangeNotification,
            object: nil,
            queue: .main
        ) { _ in
            self.updateAccessibilitySettings()
        }
    }
    
    private func updateAccessibilitySettings() {
        isVoiceOverEnabled = UIAccessibility.isVoiceOverRunning
        preferredContentSizeCategory = UIApplication.shared.preferredContentSizeCategory
        isReduceMotionEnabled = UIAccessibility.isReduceMotionEnabled
        isHighContrastEnabled = UIAccessibility.isDarkerSystemColorsEnabled
    }
}

// MARK: - Accessible Color System

extension VibeFinanceDesignSystem.Colors {
    /// WCAG 2.1 AA compliant colors with 4.5:1 contrast ratio
    struct Accessible {
        // High contrast text colors
        static let primaryText = Color.white
        static let secondaryText = Color(white: 0.85) // 4.5:1 contrast on dark blue
        static let tertiaryText = Color(white: 0.75)  // 3:1 contrast (for large text)
        
        // High contrast accent colors
        static let accentGoldAccessible = Color(red: 1.0, green: 0.9, blue: 0.2) // Higher luminance
        static let profitAccessible = Color(red: 0.2, green: 0.8, blue: 0.2)     // Higher luminance green
        static let lossAccessible = Color(red: 1.0, green: 0.3, blue: 0.3)       // Higher luminance red
        
        // Focus indicators
        static let focusRing = Color.yellow
        static let focusBackground = Color.white.opacity(0.2)
        
        // Interactive states
        static let buttonPressed = Color.white.opacity(0.3)
        static let buttonHover = Color.white.opacity(0.15)
    }
}

// MARK: - Accessible Typography

extension VibeFinanceDesignSystem.Typography {
    struct Accessible {
        // Dynamic type support with accessibility scaling
        static func scaledFont(_ baseFont: Font, category: ContentSizeCategory) -> Font {
            let scaleFactor = AccessibilityManager.getScaleFactor(for: category)
            return baseFont.weight(.medium) // Slightly bolder for better readability
        }
        
        // Minimum font sizes for accessibility
        static let minimumBodySize: CGFloat = 16
        static let minimumCaptionSize: CGFloat = 12
        static let minimumButtonSize: CGFloat = 18
    }
}

extension AccessibilityManager {
    static func getScaleFactor(for category: ContentSizeCategory) -> CGFloat {
        switch category {
        case .extraSmall: return 0.8
        case .small: return 0.9
        case .medium: return 1.0
        case .large: return 1.1
        case .extraLarge: return 1.2
        case .extraExtraLarge: return 1.3
        case .extraExtraExtraLarge: return 1.4
        case .accessibilityMedium: return 1.6
        case .accessibilityLarge: return 1.8
        case .accessibilityExtraLarge: return 2.0
        case .accessibilityExtraExtraLarge: return 2.2
        case .accessibilityExtraExtraExtraLarge: return 2.4
        @unknown default: return 1.0
        }
    }
}

// MARK: - Accessible Components

struct AccessibleVibeButton: View {
    let title: String
    let icon: String?
    let action: () -> Void
    let style: ButtonStyle
    
    @EnvironmentObject var accessibilityManager: AccessibilityManager
    @State private var isFocused = false
    
    enum ButtonStyle {
        case primary, secondary, destructive
        
        var backgroundColor: Color {
            switch self {
            case .primary: return VibeFinanceDesignSystem.Colors.Accessible.accentGoldAccessible
            case .secondary: return Color.white.opacity(0.2)
            case .destructive: return VibeFinanceDesignSystem.Colors.Accessible.lossAccessible
            }
        }
        
        var foregroundColor: Color {
            switch self {
            case .primary: return VibeFinanceDesignSystem.Colors.primaryBlue
            case .secondary: return VibeFinanceDesignSystem.Colors.Accessible.primaryText
            case .destructive: return Color.white
            }
        }
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.system(size: 18, weight: .medium))
                        .accessibilityHidden(true) // Icon is decorative
                }
                
                Text(title)
                    .font(.system(size: max(18, VibeFinanceDesignSystem.Typography.Accessible.minimumButtonSize)))
                    .fontWeight(.semibold)
                    .dynamicTypeSize(.large...accessibilityManager.preferredContentSizeCategory)
            }
            .foregroundColor(style.foregroundColor)
            .frame(maxWidth: .infinity)
            .frame(minHeight: 44) // Minimum touch target size
            .padding(.horizontal, 16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(style.backgroundColor)
                    .overlay(
                        // Focus ring for keyboard navigation
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                VibeFinanceDesignSystem.Colors.Accessible.focusRing,
                                lineWidth: isFocused ? 3 : 0
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel(title)
        .accessibilityHint("Double tap to activate")
        .accessibilityAddTraits(.isButton)
        .onFocusChange { focused in
            isFocused = focused
        }
    }
}

struct AccessibleVibeCard<Content: View>: View {
    let content: Content
    let accessibilityLabel: String
    let accessibilityHint: String?
    let onTap: (() -> Void)?
    
    @EnvironmentObject var accessibilityManager: AccessibilityManager
    @State private var isFocused = false
    
    init(
        accessibilityLabel: String,
        accessibilityHint: String? = nil,
        onTap: (() -> Void)? = nil,
        @ViewBuilder content: () -> Content
    ) {
        self.accessibilityLabel = accessibilityLabel
        self.accessibilityHint = accessibilityHint
        self.onTap = onTap
        self.content = content()
    }
    
    var body: some View {
        Group {
            if let onTap = onTap {
                Button(action: onTap) {
                    cardContent
                }
                .buttonStyle(PlainButtonStyle())
                .accessibilityLabel(accessibilityLabel)
                .accessibilityHint(accessibilityHint ?? "Double tap to interact")
                .accessibilityAddTraits(.isButton)
            } else {
                cardContent
                    .accessibilityElement(children: .combine)
                    .accessibilityLabel(accessibilityLabel)
            }
        }
        .onFocusChange { focused in
            isFocused = focused
        }
    }
    
    private var cardContent: some View {
        content
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color.white.opacity(accessibilityManager.isHighContrastEnabled ? 0.25 : 0.15),
                                Color.white.opacity(accessibilityManager.isHighContrastEnabled ? 0.15 : 0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                accessibilityManager.isHighContrastEnabled ? 
                                Color.white.opacity(0.5) : Color.white.opacity(0.2),
                                lineWidth: 1
                            )
                    )
                    .overlay(
                        // Focus ring
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(
                                VibeFinanceDesignSystem.Colors.Accessible.focusRing,
                                lineWidth: isFocused ? 3 : 0
                            )
                    )
            )
    }
}

struct AccessibleFinancialMetric: View {
    let title: String
    let value: String
    let change: String?
    let isPositive: Bool?
    let icon: String?
    
    @EnvironmentObject var accessibilityManager: AccessibilityManager
    
    private var accessibilityDescription: String {
        var description = "\(title): \(value)"
        
        if let change = change, let isPositive = isPositive {
            let direction = isPositive ? "up" : "down"
            description += ", \(direction) \(change)"
        }
        
        return description
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.title3)
                        .foregroundColor(
                            accessibilityManager.isHighContrastEnabled ?
                            VibeFinanceDesignSystem.Colors.Accessible.accentGoldAccessible :
                            VibeFinanceDesignSystem.Colors.accentGold
                        )
                        .accessibilityHidden(true)
                }
                
                Text(title)
                    .font(.system(size: max(16, VibeFinanceDesignSystem.Typography.Accessible.minimumBodySize)))
                    .foregroundColor(
                        accessibilityManager.isHighContrastEnabled ?
                        VibeFinanceDesignSystem.Colors.Accessible.secondaryText :
                        VibeFinanceDesignSystem.Colors.textSecondary
                    )
                    .dynamicTypeSize(.large...accessibilityManager.preferredContentSizeCategory)
                
                Spacer()
            }
            
            Text(value)
                .font(.system(size: 24, weight: .bold, design: .rounded))
                .foregroundColor(
                    accessibilityManager.isHighContrastEnabled ?
                    VibeFinanceDesignSystem.Colors.Accessible.primaryText :
                    VibeFinanceDesignSystem.Colors.textPrimary
                )
                .dynamicTypeSize(.large...accessibilityManager.preferredContentSizeCategory)
            
            if let change = change, let isPositive = isPositive {
                HStack(spacing: 4) {
                    Image(systemName: isPositive ? "arrow.up.right" : "arrow.down.right")
                        .font(.caption)
                        .accessibilityHidden(true)
                    
                    Text(change)
                        .font(.system(size: max(14, VibeFinanceDesignSystem.Typography.Accessible.minimumCaptionSize)))
                        .dynamicTypeSize(.large...accessibilityManager.preferredContentSizeCategory)
                }
                .foregroundColor(
                    accessibilityManager.isHighContrastEnabled ?
                    (isPositive ? VibeFinanceDesignSystem.Colors.Accessible.profitAccessible : VibeFinanceDesignSystem.Colors.Accessible.lossAccessible) :
                    (isPositive ? VibeFinanceDesignSystem.Colors.profit : VibeFinanceDesignSystem.Colors.loss)
                )
            }
        }
        .accessibilityElement(children: .ignore)
        .accessibilityLabel(accessibilityDescription)
        .accessibilityAddTraits(.updatesFrequently)
    }
}

// MARK: - VoiceOver Navigation Helpers

struct VoiceOverNavigationView<Content: View>: View {
    let content: Content
    let navigationTitle: String
    
    init(navigationTitle: String, @ViewBuilder content: () -> Content) {
        self.navigationTitle = navigationTitle
        self.content = content()
    }
    
    var body: some View {
        content
            .accessibilityElement(children: .contain)
            .accessibilityLabel(navigationTitle)
            .accessibilityAddTraits(.isHeader)
            .onAppear {
                // Announce screen change for VoiceOver users
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    UIAccessibility.post(
                        notification: .screenChanged,
                        argument: navigationTitle
                    )
                }
            }
    }
}

// MARK: - Accessibility Extensions

extension View {
    func accessibleTapTarget(minSize: CGFloat = 44) -> some View {
        self.frame(minWidth: minSize, minHeight: minSize)
    }
    
    func accessibleContrast(_ accessibilityManager: AccessibilityManager) -> some View {
        self.environment(\.colorScheme, accessibilityManager.isHighContrastEnabled ? .dark : .dark)
    }
    
    func reducedMotion(_ accessibilityManager: AccessibilityManager) -> some View {
        self.animation(
            accessibilityManager.isReduceMotionEnabled ? .none : .default,
            value: accessibilityManager.isReduceMotionEnabled
        )
    }
}
