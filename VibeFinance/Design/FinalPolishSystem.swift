//
//  FinalPolishSystem.swift
//  VibeFinance - Final Polish & Edge Case Handling for Apple Design Award
//
//  Created by MAGESH DHANASEKARAN on 6/29/25.
//

import SwiftUI
import Network
import Combine

// MARK: - Edge Case Manager

@MainActor
class EdgeCaseManager: ObservableObject {
    @Published var networkStatus: EdgeCaseNetworkStatus = .connected
    @Published var isLowPowerMode = false
    @Published var isReducedMotion = false
    @Published var deviceOrientation: UIDeviceOrientation = .portrait
    @Published var memoryPressure: MemoryPressureLevel = .normal
    
    private let networkMonitor = NWPathMonitor()
    private let queue = DispatchQueue(label: "NetworkMonitor")
    
    static let shared = EdgeCaseManager()
    
    private init() {
        setupNetworkMonitoring()
        setupSystemMonitoring()
    }
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                if path.status == .satisfied {
                    self?.networkStatus = path.isExpensive ? .expensiveConnection : .connected
                } else {
                    self?.networkStatus = .disconnected
                }
            }
        }
        networkMonitor.start(queue: queue)
    }
    
    private func setupSystemMonitoring() {
        // Monitor low power mode
        NotificationCenter.default.addObserver(
            forName: .NSProcessInfoPowerStateDidChange,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.isLowPowerMode = ProcessInfo.processInfo.isLowPowerModeEnabled
            }
        }

        // Monitor reduced motion
        NotificationCenter.default.addObserver(
            forName: UIAccessibility.reduceMotionStatusDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.isReducedMotion = UIAccessibility.isReduceMotionEnabled
            }
        }

        // Monitor device orientation
        NotificationCenter.default.addObserver(
            forName: UIDevice.orientationDidChangeNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.deviceOrientation = UIDevice.current.orientation
            }
        }

        // Monitor memory pressure
        setupMemoryPressureMonitoring()
    }
    
    private func setupMemoryPressureMonitoring() {
        let source = DispatchSource.makeMemoryPressureSource(eventMask: .all, queue: .main)
        source.setEventHandler { [weak self] in
            let event = source.mask
            if event.contains(.critical) {
                self?.memoryPressure = .critical
            } else if event.contains(.warning) {
                self?.memoryPressure = .warning
            } else {
                self?.memoryPressure = .normal
            }
        }
        source.resume()
    }
}

enum EdgeCaseNetworkStatus {
    case connected
    case expensiveConnection
    case disconnected
}

// Using MemoryPressureLevel from BatteryThermalOptimizer.swift

// MARK: - Adaptive Performance Manager

@MainActor
class AdaptivePerformanceManager: ObservableObject {
    @Published var currentPerformanceLevel: PerformanceLevel = .high
    @Published var shouldReduceAnimations = false
    @Published var shouldReduceImageQuality = false
    @Published var shouldLimitBackgroundTasks = false
    
    @ObservedObject private var edgeCaseManager = EdgeCaseManager.shared
    
    static let shared = AdaptivePerformanceManager()
    
    private init() {
        setupPerformanceAdaptation()
    }
    
    private func setupPerformanceAdaptation() {
        // Observe edge case changes and adapt performance
        edgeCaseManager.$isLowPowerMode
            .combineLatest(edgeCaseManager.$memoryPressure, edgeCaseManager.$networkStatus)
            .sink { [weak self] lowPower, memoryPressure, networkStatus in
                self?.adaptPerformance(
                    lowPower: lowPower,
                    memoryPressure: memoryPressure,
                    networkStatus: networkStatus
                )
            }
            .store(in: &cancellables)
    }
    
    private var cancellables = Set<AnyCancellable>()
    
    private func adaptPerformance(
        lowPower: Bool,
        memoryPressure: MemoryPressureLevel,
        networkStatus: EdgeCaseNetworkStatus
    ) {
        // Determine performance level
        if lowPower || memoryPressure == .critical {
            currentPerformanceLevel = .low
        } else if memoryPressure == .warning || networkStatus == .expensiveConnection {
            currentPerformanceLevel = .medium
        } else {
            currentPerformanceLevel = .high
        }
        
        // Adapt specific features
        shouldReduceAnimations = lowPower || edgeCaseManager.isReducedMotion
        shouldReduceImageQuality = memoryPressure != .normal || networkStatus == .expensiveConnection
        shouldLimitBackgroundTasks = lowPower || memoryPressure == .critical
        
        UserAnalyticsManager.shared.trackEvent("performance_adaptation", parameters: [
            "level": currentPerformanceLevel.rawValue,
            "low_power": lowPower,
            "memory_pressure": String(describing: memoryPressure),
            "network_status": networkStatus.description
        ])
    }
}

// Using PerformanceLevel from AppleSiliconOptimization.swift

// MARK: - Graceful Degradation Components

struct AdaptiveImage: View {
    let url: URL?
    let placeholder: String
    
    @ObservedObject private var performanceManager = AdaptivePerformanceManager.shared
    @State private var imageQuality: ImageQuality = .high
    
    var body: some View {
        AsyncImage(url: adaptedURL) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            Image(systemName: placeholder)
                .foregroundColor(.white.opacity(0.6))
                .font(.title)
        }
        .onChange(of: performanceManager.shouldReduceImageQuality) { _, shouldReduce in
            imageQuality = shouldReduce ? .low : .high
        }
    }
    
    private var adaptedURL: URL? {
        guard let url = url else { return nil }
        
        if imageQuality == .low {
            // In a real app, you might append quality parameters
            return url
        }
        return url
    }
}

enum ImageQuality {
    case low, medium, high
}

struct AdaptiveAnimation: ViewModifier {
    @ObservedObject private var performanceManager = AdaptivePerformanceManager.shared
    
    let animation: Animation
    let fallbackAnimation: Animation
    
    func body(content: Content) -> some View {
        content
            .animation(
                performanceManager.shouldReduceAnimations ? fallbackAnimation : animation,
                value: UUID() // This should be replaced with actual animated value
            )
    }
}

struct GracefulErrorView: View {
    let error: AppError
    let onRetry: () -> Void
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            // Error Icon
            Image(systemName: error.icon)
                .font(.system(size: 48))
                .foregroundColor(error.color)
            
            // Error Message
            VStack(spacing: 8) {
                Text(error.title)
                    .font(.headline)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                
                Text(error.message)
                    .font(.body)
                    .foregroundColor(.white.opacity(0.8))
                    .multilineTextAlignment(.center)
            }
            
            // Warren Buffett Wisdom
            if let wisdom = error.buffettWisdom {
                VStack(spacing: 8) {
                    HStack {
                        Image(systemName: "lightbulb.fill")
                            .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                        Text("Warren's Wisdom")
                            .font(.caption)
                            .fontWeight(.semibold)
                            .foregroundColor(VibeFinanceDesignSystem.Colors.accentGold)
                    }
                    
                    Text(wisdom)
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.9))
                        .multilineTextAlignment(.center)
                        .italic()
                }
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.white.opacity(0.1))
                )
            }
            
            // Action Buttons
            HStack(spacing: 12) {
                if error.isRetryable {
                    Button(action: onRetry) {
                        Text("Try Again")
                            .font(.headline)
                            .foregroundColor(.black)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(VibeFinanceDesignSystem.Colors.accentGold)
                            .cornerRadius(12)
                    }
                }
                
                Button(action: onDismiss) {
                    Text("Dismiss")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.white.opacity(0.2))
                        .cornerRadius(12)
                }
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.9))
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(error.color.opacity(0.3), lineWidth: 1)
                )
        )
        .padding(.horizontal, 20)
    }
}

struct AppError {
    let title: String
    let message: String
    let icon: String
    let color: Color
    let isRetryable: Bool
    let buffettWisdom: String?
    
    static let networkError = AppError(
        title: "Connection Issue",
        message: "Unable to connect to our servers. Please check your internet connection.",
        icon: "wifi.slash",
        color: .orange,
        isRetryable: true,
        buffettWisdom: "\"Risk comes from not knowing what you're doing.\" - Sometimes the best investment is patience."
    )
    
    static let serverError = AppError(
        title: "Server Unavailable",
        message: "Our servers are temporarily unavailable. We're working to fix this.",
        icon: "server.rack",
        color: .red,
        isRetryable: true,
        buffettWisdom: "\"The stock market is a voting machine in the short run, but a weighing machine in the long run.\" - Technical issues are temporary."
    )
    
    static let dataError = AppError(
        title: "Data Unavailable",
        message: "Unable to load the requested data. This might be a temporary issue.",
        icon: "exclamationmark.triangle",
        color: .yellow,
        isRetryable: true,
        buffettWisdom: "\"It's better to be approximately right than precisely wrong.\" - We'll get your data as soon as possible."
    )
}

// MARK: - Offline Mode Support

struct OfflineModeView: View {
    @ObservedObject private var edgeCaseManager = EdgeCaseManager.shared
    
    var body: some View {
        if edgeCaseManager.networkStatus == .disconnected {
            VStack(spacing: 16) {
                HStack {
                    Image(systemName: "wifi.slash")
                        .foregroundColor(.orange)
                    
                    Text("You're offline")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.9))
                    
                    Spacer()
                    
                    Text("Limited features available")
                        .font(.caption2)
                        .foregroundColor(.white.opacity(0.7))
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.orange.opacity(0.2))
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                        )
                )
            }
            .padding(.horizontal, 16)
            .transition(.move(edge: .top).combined(with: .opacity))
        }
    }
}

// MARK: - Memory Management

class MemoryManager {
    static let shared = MemoryManager()
    
    private init() {
        setupMemoryWarningObserver()
    }
    
    private func setupMemoryWarningObserver() {
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { _ in
            Task { @MainActor in
                self.handleMemoryWarning()
            }
        }
    }
    
    @MainActor
    private func handleMemoryWarning() {
        // Clear caches
        URLCache.shared.removeAllCachedResponses()
        
        // Notify managers to clear their caches
        NotificationCenter.default.post(name: .memoryWarning, object: nil)
        
        UserAnalyticsManager.shared.trackEvent("memory_warning", parameters: [
            "timestamp": Date().timeIntervalSince1970
        ])
    }
}

extension Notification.Name {
    static let memoryWarning = Notification.Name("memoryWarning")
}

// MARK: - View Extensions

extension View {
    func adaptiveAnimation(
        _ animation: Animation,
        fallback: Animation = Animation.default
    ) -> some View {
        modifier(AdaptiveAnimation(animation: animation, fallbackAnimation: fallback))
    }
    
    func gracefulErrorHandling<T>(
        for result: Result<T, Error>,
        onRetry: @escaping () -> Void
    ) -> some View {
        ZStack {
            self
            
            if case .failure(_) = result {
                GracefulErrorView(
                    error: AppError.networkError, // Map actual error to AppError
                    onRetry: onRetry,
                    onDismiss: {}
                )
            }
        }
    }
    
    func withOfflineSupport() -> some View {
        VStack(spacing: 0) {
            OfflineModeView()
            self
        }
    }
}

// MARK: - Performance Monitoring

struct PerformanceMonitoringView<Content: View>: View {
    let content: Content
    let identifier: String
    
    @State private var renderStartTime = Date()
    @State private var frameCount = 0
    
    init(identifier: String, @ViewBuilder content: () -> Content) {
        self.identifier = identifier
        self.content = content()
    }
    
    var body: some View {
        content
            .onAppear {
                renderStartTime = Date()
                frameCount = 0
                startFrameRateMonitoring()
            }
            .onDisappear {
                stopFrameRateMonitoring()
            }
    }
    
    private func startFrameRateMonitoring() {
        // Monitor frame rate and report performance metrics
        Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { timer in
            let fps = Double(frameCount)
            frameCount = 0
            
            Task { @MainActor in
                UserAnalyticsManager.shared.trackPerformanceMetric(
                    "fps_\(identifier)",
                    value: fps,
                    unit: "fps"
                )

                if fps < 30 {
                    UserAnalyticsManager.shared.trackEvent("performance_issue", parameters: [
                        "component": identifier,
                        "fps": fps,
                        "type": "low_frame_rate"
                    ])
                }
            }
        }
    }
    
    private func stopFrameRateMonitoring() {
        // Stop monitoring
    }
}

extension View {
    func monitorPerformance(identifier: String) -> some View {
        PerformanceMonitoringView(identifier: identifier) {
            self
        }
    }
}
