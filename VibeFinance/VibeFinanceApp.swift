//
//  VibeFinanceApp.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import SwiftData

@main
struct VibeFinanceApp: App {
    @StateObject private var authManager = AuthManager()
    @StateObject private var userManager = UserManager()
    @StateObject private var feedManager = FeedManager()
    @StateObject private var questManager = QuestManager()
    @StateObject private var squadManager = SquadManager()
    @StateObject private var simulatorManager = SimulatorManager()
    @StateObject private var realTradingManager = RealTradingManager()
    @StateObject private var analyticsManager = AnalyticsManager()
    @StateObject private var chatManager = ChatManager()
    @StateObject private var subscriptionManager = SubscriptionManager()
    @StateObject private var paymentManager: PaymentManager
    @StateObject private var performanceManager = PerformanceManager.shared
    @StateObject private var networkOptimizer = NetworkOptimizer.shared

    init() {
        let subscriptionMgr = SubscriptionManager()
        _subscriptionManager = StateObject(wrappedValue: subscriptionMgr)
        _paymentManager = StateObject(wrappedValue: PaymentManager(subscriptionManager: subscriptionMgr))

        // Initialize production configuration
        setupProductionEnvironment()
    }

    private func setupProductionEnvironment() {
        // Log production readiness
        ProductionConfig.logProductionReadiness()

        // Configure logging level
        if ProductionConfig.isProduction {
            ProductionConfig.log("🚀 VibeFinance starting in PRODUCTION mode", category: "APP", level: .info)
        } else {
            ProductionConfig.log("🛠️ VibeFinance starting in DEVELOPMENT mode", category: "APP", level: .info)
        }

        // Log API configuration
        ProductionConfig.log("📡 Using real APIs: \(ProductionConfig.useRealAPIs)", category: "APP", level: .info)
        ProductionConfig.log("⚡ Real-time updates: \(ProductionConfig.enableRealTimeUpdates)", category: "APP", level: .info)

        // Start real-time services if enabled
        if ProductionConfig.enableRealTimeUpdates {
            MarketService.shared.startRealTimeUpdates()
        }
    }

    var body: some Scene {
        WindowGroup {
            if authManager.isAuthenticated {
                MainTabView()
                    .environmentObject(authManager)
                    .environmentObject(userManager)
                    .environmentObject(feedManager)
                    .environmentObject(questManager)
                    .environmentObject(squadManager)
                    .environmentObject(simulatorManager)
                    .environmentObject(realTradingManager)
                    .environmentObject(analyticsManager)
                    .environmentObject(chatManager)
                    .environmentObject(subscriptionManager)
                    .environmentObject(paymentManager)
                    .environmentObject(performanceManager)
                    .environmentObject(networkOptimizer)
                    .preferredColorScheme(.dark)
            } else {
                OnboardingView()
                    .environmentObject(authManager)
                    .environmentObject(userManager)
                    .preferredColorScheme(.dark)
            }
        }
    }
}
