//
//  VibeFinanceApp.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import SwiftUI
import SwiftData

@main
struct VibeFinanceApp: App {
    @StateObject private var authManager = AuthManager()
    @StateObject private var userManager = UserManager()
    @StateObject private var feedManager = FeedManager()
    @StateObject private var questManager = QuestManager()
    @StateObject private var squadManager = SquadManager()
    @StateObject private var simulatorManager = SimulatorManager()
    @StateObject private var realTradingManager = RealTradingManager()
    @StateObject private var analyticsManager = AnalyticsManager()
    @StateObject private var chatManager = ChatManager()
    @StateObject private var subscriptionManager = SubscriptionManager()
    @StateObject private var paymentManager: PaymentManager
    @StateObject private var performanceManager = PerformanceManager.shared
    @StateObject private var networkOptimizer = NetworkOptimizer.shared

    init() {
        let subscriptionMgr = SubscriptionManager()
        _subscriptionManager = StateObject(wrappedValue: subscriptionMgr)
        _paymentManager = StateObject(wrappedValue: PaymentManager(subscriptionManager: subscriptionMgr))
    }

    var body: some Scene {
        WindowGroup {
            if authManager.isAuthenticated {
                MainTabView()
                    .environmentObject(authManager)
                    .environmentObject(userManager)
                    .environmentObject(feedManager)
                    .environmentObject(questManager)
                    .environmentObject(squadManager)
                    .environmentObject(simulatorManager)
                    .environmentObject(realTradingManager)
                    .environmentObject(analyticsManager)
                    .environmentObject(chatManager)
                    .environmentObject(subscriptionManager)
                    .environmentObject(paymentManager)
                    .environmentObject(performanceManager)
                    .environmentObject(networkOptimizer)
                    .preferredColorScheme(.dark)
            } else {
                OnboardingView()
                    .environmentObject(authManager)
                    .environmentObject(userManager)
                    .preferredColorScheme(.dark)
            }
        }
    }
}
