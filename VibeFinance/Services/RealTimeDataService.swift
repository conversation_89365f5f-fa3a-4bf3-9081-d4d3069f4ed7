//
//  RealTimeDataService.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation
import Combine

// MARK: - Real-time Data Synchronization Service
@MainActor
class RealTimeDataService: ObservableObject {
    static let shared = RealTimeDataService()
    
    @Published var isConnected = false
    @Published var lastSyncTime = Date()
    @Published var syncStatus: SyncStatus = .idle
    
    private let marketService = MarketService.shared
    private let newsService = NewsService.shared
    private let supabaseService = SupabaseService.shared
    
    private var cancellables = Set<AnyCancellable>()
    private var syncTimer: Timer?
    private var subscribedPortfolios: Set<UUID> = []
    private var subscribedSymbols: Set<String> = []
    
    private init() {
        setupRealTimeSubscriptions()
    }
    
    deinit {
        stopAllSyncing()
    }
    
    // MARK: - Real-time Setup
    
    private func setupRealTimeSubscriptions() {
        // Subscribe to market data updates
        marketService.priceUpdates
            .sink { [weak self] updatedPrices in
                Task {
                    await self?.handleMarketDataUpdate(updatedPrices)
                }
            }
            .store(in: &cancellables)
        
        // Start periodic sync for portfolios and news
        startPeriodicSync()
    }
    
    private func startPeriodicSync() {
        syncTimer = Timer.scheduledTimer(withTimeInterval: ProductionConfig.RealTimeData.portfolioUpdateInterval, repeats: true) { [weak self] _ in
            Task {
                await self?.performPeriodicSync()
            }
        }
    }
    
    func stopAllSyncing() {
        syncTimer?.invalidate()
        syncTimer = nil
        cancellables.removeAll()
    }
    
    // MARK: - Portfolio Synchronization
    
    func subscribeToPortfolio(_ portfolioId: UUID) {
        subscribedPortfolios.insert(portfolioId)
        
        Task {
            await syncPortfolio(portfolioId)
        }
    }
    
    func unsubscribeFromPortfolio(_ portfolioId: UUID) {
        subscribedPortfolios.remove(portfolioId)
    }
    
    private func syncPortfolio(_ portfolioId: UUID) async {
        do {
            syncStatus = .syncing
            
            // Fetch latest portfolio data from Supabase
            let portfolio = try await supabaseService.fetch(
                VirtualPortfolio.self,
                from: "virtual_portfolios",
                where: "id=eq.\(portfolioId.uuidString)"
            ).first
            
            if let portfolio = portfolio {
                // Update portfolio values with latest market data
                await updatePortfolioWithLatestPrices(portfolio)
            }
            
            syncStatus = .success
            lastSyncTime = Date()
            
        } catch {
            syncStatus = .error(error.localizedDescription)
            ProductionConfig.log("Portfolio sync failed: \(error)", category: "REALTIME", level: .error)
        }
    }
    
    private func updatePortfolioWithLatestPrices(_ portfolio: VirtualPortfolio) async {
        var updatedPortfolio = portfolio
        
        for position in updatedPortfolio.positions {
            if let latestPrice = await marketService.getStockData(symbol: position.symbol) {
                // Update position with latest price
                var updatedPosition = position
                updatedPosition.currentPrice = latestPrice.price
                updatedPosition.lastUpdated = Date()
                
                // Update portfolio positions array
                if let index = updatedPortfolio.positions.firstIndex(where: { $0.id == position.id }) {
                    updatedPortfolio.positions[index] = updatedPosition
                }
            }
        }
        
        // Recalculate portfolio totals
        updatedPortfolio.totalValue = updatedPortfolio.balance + updatedPortfolio.positions.reduce(0) { total, position in
            total + (position.shares * position.currentPrice)
        }
        updatedPortfolio.updatedAt = Date()
        
        // Save updated portfolio to database
        do {
            try await supabaseService.update(
                updatedPortfolio,
                in: "virtual_portfolios",
                where: "id=eq.\(updatedPortfolio.id.uuidString)"
            )
        } catch {
            ProductionConfig.log("Failed to update portfolio: \(error)", category: "REALTIME", level: .error)
        }
    }
    
    // MARK: - Market Data Synchronization
    
    func subscribeToSymbol(_ symbol: String) {
        subscribedSymbols.insert(symbol)
        marketService.subscribeToSymbol(symbol)
    }
    
    func unsubscribeFromSymbol(_ symbol: String) {
        subscribedSymbols.remove(symbol)
        marketService.unsubscribeFromSymbol(symbol)
    }
    
    private func handleMarketDataUpdate(_ updatedPrices: [String: StockPrice]) async {
        // Notify all subscribed components about price updates
        for (symbol, price) in updatedPrices {
            ProductionConfig.log("📈 Price update: \(symbol) = $\(price.price)", category: "REALTIME", level: .debug)
        }
        
        // Update any portfolios that contain these symbols
        for portfolioId in subscribedPortfolios {
            await syncPortfolio(portfolioId)
        }
        
        lastSyncTime = Date()
    }
    
    // MARK: - News Synchronization
    
    private func syncLatestNews() async {
        do {
            let latestNews = try await newsService.fetchFinanceNews()
            
            // Process and cache latest news
            ProductionConfig.log("📰 Synced \(latestNews.count) news articles", category: "REALTIME", level: .info)
            
        } catch {
            ProductionConfig.log("News sync failed: \(error)", category: "REALTIME", level: .error)
        }
    }
    
    // MARK: - Periodic Sync
    
    private func performPeriodicSync() async {
        guard ProductionConfig.useRealAPIs else { return }
        
        syncStatus = .syncing
        
        // Sync portfolios
        for portfolioId in subscribedPortfolios {
            await syncPortfolio(portfolioId)
        }
        
        // Sync news every 5 minutes
        if Date().timeIntervalSince(lastSyncTime) > ProductionConfig.RealTimeData.newsUpdateInterval {
            await syncLatestNews()
        }
        
        syncStatus = .success
        lastSyncTime = Date()
    }
    
    // MARK: - Connection Management
    
    func connect() async {
        guard ProductionConfig.useRealAPIs else {
            ProductionConfig.log("Real APIs disabled, skipping connection", category: "REALTIME", level: .info)
            return
        }
        
        isConnected = true
        syncStatus = .connected
        
        ProductionConfig.log("🔗 Real-time data service connected", category: "REALTIME", level: .info)
        
        // Start initial sync
        await performPeriodicSync()
    }
    
    func disconnect() {
        isConnected = false
        syncStatus = .disconnected
        stopAllSyncing()
        
        ProductionConfig.log("🔌 Real-time data service disconnected", category: "REALTIME", level: .info)
    }
    
    // MARK: - Health Check
    
    func performHealthCheck() async -> Bool {
        do {
            // Test market data connection
            let _ = await marketService.getStockData(symbol: "AAPL")
            
            // Test database connection
            let _ = try await supabaseService.fetch(
                VirtualPortfolio.self,
                from: "virtual_portfolios",
                where: "limit=1"
            )
            
            return true
        } catch {
            ProductionConfig.log("Health check failed: \(error)", category: "REALTIME", level: .error)
            return false
        }
    }
}

// MARK: - Supporting Types

enum SyncStatus {
    case idle
    case connecting
    case connected
    case syncing
    case success
    case disconnected
    case error(String)
    
    var description: String {
        switch self {
        case .idle: return "Idle"
        case .connecting: return "Connecting..."
        case .connected: return "Connected"
        case .syncing: return "Syncing..."
        case .success: return "Synced"
        case .disconnected: return "Disconnected"
        case .error(let message): return "Error: \(message)"
        }
    }
    
    var isError: Bool {
        if case .error = self { return true }
        return false
    }
}
