//
//  GeminiAIService.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Gemini AI Service
class GeminiAIService: AIService, ObservableObject {
    private let networkManager = NetworkManager.shared
    private let config = APIConfiguration.shared

    init() {}
    
    // MARK: - Generate Response
    func generateResponse(prompt: String) async throws -> AIResponse {
        guard let url = config.buildGeminiURL() else {
            throw APIError(code: "INVALID_URL", message: "Invalid Gemini API URL", details: nil)
        }
        
        let requestBody = GeminiRequest(
            contents: [
                GeminiContent(
                    parts: [GeminiPart(text: prompt)]
                )
            ]
        )
        
        let jsonData = try JSONEncoder().encode(requestBody)
        
        let response: GeminiResponse = try await networkManager.request(
            url: url,
            method: .POST,
            headers: config.getGeminiHeaders(),
            body: jsonData,
            responseType: GeminiResponse.self
        )
        
        guard let candidate = response.candidates?.first,
              let content = candidate.content,
              let part = content.parts.first else {
            throw APIError(code: "NO_RESPONSE", message: "No response from Gemini AI", details: nil)
        }
        
        return AIResponse(
            text: part.text,
            confidence: 0.85, // Gemini doesn't provide confidence scores
            suggestions: extractSuggestions(from: part.text)
        )
    }
    
    // MARK: - Analyze Portfolio
    func analyzePortfolio(portfolio: Portfolio) async throws -> PortfolioAnalysis {
        let portfolioDescription = createPortfolioDescription(portfolio)
        let prompt = """
        As a financial advisor AI, analyze this portfolio and provide insights:
        
        Portfolio Details:
        \(portfolioDescription)
        
        Please provide:
        1. Overall risk assessment (score 1-10)
        2. Diversification analysis (score 1-10)
        3. Top 3 recommendations for improvement
        4. Expected annual return estimate
        
        Format your response as a structured analysis.
        """
        
        let aiResponse = try await generateResponse(prompt: prompt)
        
        return PortfolioAnalysis(
            totalValue: portfolio.totalValue,
            totalReturn: calculateTotalReturn(portfolio),
            riskScore: extractRiskScore(from: aiResponse.text),
            diversificationScore: extractDiversificationScore(from: aiResponse.text),
            recommendations: extractRecommendations(from: aiResponse.text)
        )
    }
    
    // MARK: - Get Investment Recommendations
    func getInvestmentRecommendations(riskProfile: RiskProfile) async throws -> [InvestmentRecommendation] {
        let prompt = """
        As a financial advisor AI, provide investment recommendations for a client with this profile:
        
        Risk Tolerance: \(riskProfile.riskTolerance)
        Investment Horizon: \(riskProfile.investmentHorizon)
        Age: \(riskProfile.age)
        Annual Income: $\(riskProfile.income)
        Investment Goals: \(riskProfile.investmentGoals.joined(separator: ", "))
        
        Please recommend 5 specific investments (stocks, ETFs, or funds) with:
        - Symbol/ticker
        - Company/fund name
        - Recommendation type (BUY/HOLD/SELL)
        - Expected annual return
        - Risk level (Low/Medium/High)
        - Brief reasoning
        
        Focus on diversified, well-researched recommendations.
        """
        
        let aiResponse = try await generateResponse(prompt: prompt)
        return parseInvestmentRecommendations(from: aiResponse.text)
    }

    // MARK: - Quest Generation
    func generateDailyQuest(userLevel: Int, preferences: [String]) async throws -> Quest {
        let prompt = """
        Generate a daily financial quest for a user at level \(userLevel) with interests in: \(preferences.joined(separator: ", ")).
        The quest should be educational, achievable in one day, and appropriate for their level.
        Include a clear title, description, and reward points (50-200 points).
        """

        let response = try await generateResponse(prompt: prompt)

        // For now, return a mock quest - in a real implementation, you'd parse the AI response
        return Quest(
            title: "Learn About Dividend Investing",
            description: "Research and understand how dividend-paying stocks can provide passive income. Find 3 dividend aristocrats and note their yield percentages.",
            category: .investing,
            difficulty: .beginner,
            xpReward: 100,
            estimatedTime: 30,
            tasks: [
                QuestTask(
                    title: "Research dividend aristocrats",
                    description: "Find 3 dividend aristocrat stocks",
                    type: .research,
                    xpReward: 50,
                    order: 1
                ),
                QuestTask(
                    title: "Understand dividend yield",
                    description: "Learn how dividend yield is calculated",
                    type: .reading,
                    xpReward: 50,
                    order: 2
                )
            ]
        )
    }

    // MARK: - Generate Market Analysis
    func generateMarketAnalysis(marketData: MarketData) async throws -> String {
        let marketDescription = createMarketDescription(marketData)
        let prompt = """
        As a financial market analyst AI, provide a comprehensive market analysis based on this data:
        
        \(marketDescription)
        
        Please provide:
        1. Overall market sentiment
        2. Key trends and patterns
        3. Sector analysis
        4. Short-term outlook (1-3 months)
        5. Investment opportunities
        
        Keep the analysis professional but accessible to retail investors.
        """
        
        let response = try await generateResponse(prompt: prompt)
        return response.text
    }
    
    // MARK: - Generate Trading Insights
    func generateTradingInsights(symbol: String, historicalData: [HistoricalDataPoint]) async throws -> String {
        let dataDescription = createHistoricalDataDescription(symbol: symbol, data: historicalData)
        let prompt = """
        As a technical analysis AI, analyze this stock data and provide trading insights:
        
        \(dataDescription)
        
        Please provide:
        1. Technical analysis summary
        2. Support and resistance levels
        3. Trend analysis
        4. Entry/exit recommendations
        5. Risk management suggestions
        
        Focus on actionable insights for swing trading.
        """
        
        let response = try await generateResponse(prompt: prompt)
        return response.text
    }

    // MARK: - Additional AI Methods
    func generateChatResponse(message: String, context: [String] = []) async throws -> String {
        let contextString = context.isEmpty ? "" : "Context: \(context.joined(separator: ", "))\n\n"
        let prompt = "\(contextString)User message: \(message)\n\nProvide a helpful financial assistant response:"
        let response = try await generateResponse(prompt: prompt)
        return response.text
    }

    func summarizeContent(_ content: String, maxLength: Int = 100) async throws -> String {
        let prompt = "Summarize this financial content in \(maxLength) words or less: \(content)"
        let response = try await generateResponse(prompt: prompt)
        return response.text
    }

    func generateTags(for content: String, interests: [String]) async throws -> [String] {
        let prompt = "Generate 3-5 relevant tags for this content based on interests \(interests.joined(separator: ", ")): \(content)"
        let response = try await generateResponse(prompt: prompt)
        // Simple parsing - in production, use more sophisticated parsing
        return Array(response.text.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespaces) }.prefix(5))
    }

    func categorizeContent(_ content: String) async throws -> String {
        let prompt = "Categorize this financial content into one of: stocks, crypto, news, education, market-analysis: \(content)"
        let response = try await generateResponse(prompt: prompt)
        return response.text.trimmingCharacters(in: .whitespaces)
    }

    func calculateVibeScore(_ content: String, for preferences: UserPreferences) async throws -> Double {
        let prompt = "Rate this content from 0-10 based on relevance to user preferences \(preferences.interests.joined(separator: ", ")): \(content)"
        let response = try await generateResponse(prompt: prompt)
        // Extract number from response
        let numbers = response.text.components(separatedBy: CharacterSet.decimalDigits.inverted).compactMap { Double($0) }
        return numbers.first ?? 5.0
    }

    func generateInvestmentSuggestion(based content: String, for preferences: UserPreferences) async throws -> String {
        let prompt = "Based on this content and user preferences (risk: \(preferences.riskTolerance), interests: \(preferences.interests.joined(separator: ", "))), suggest a relevant investment action: \(content)"
        let response = try await generateResponse(prompt: prompt)
        return response.text
    }

    // MARK: - Helper Methods
    private func extractSuggestions(from text: String) -> [String] {
        // Simple extraction - in production, use more sophisticated NLP
        let lines = text.components(separatedBy: .newlines)
        return lines.filter { line in
            line.contains("recommend") || line.contains("suggest") || line.contains("consider")
        }.prefix(3).map { String($0) }
    }
    
    private func createPortfolioDescription(_ portfolio: Portfolio) -> String {
        var description = "Total Value: $\(portfolio.totalValue)\n"
        description += "Cash: $\(portfolio.cash)\n"
        description += "Positions:\n"
        
        for position in portfolio.positions {
            description += "- \(position.symbol): \(position.quantity) shares, Value: $\(position.marketValue), P&L: \(position.unrealizedPLPercent)%\n"
        }
        
        return description
    }
    
    private func calculateTotalReturn(_ portfolio: Portfolio) -> Double {
        return portfolio.positions.reduce(0) { $0 + $1.unrealizedPL }
    }
    
    private func extractRiskScore(from text: String) -> Double {
        // Simple regex to find risk score - in production, use more sophisticated parsing
        let pattern = #"risk.*?(\d+(?:\.\d+)?)"#
        if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive),
           let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)),
           let range = Range(match.range(at: 1), in: text) {
            return Double(text[range]) ?? 5.0
        }
        return 5.0 // Default moderate risk
    }
    
    private func extractDiversificationScore(from text: String) -> Double {
        let pattern = #"diversif.*?(\d+(?:\.\d+)?)"#
        if let regex = try? NSRegularExpression(pattern: pattern, options: .caseInsensitive),
           let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)),
           let range = Range(match.range(at: 1), in: text) {
            return Double(text[range]) ?? 5.0
        }
        return 5.0 // Default moderate diversification
    }
    
    private func extractRecommendations(from text: String) -> [String] {
        let lines = text.components(separatedBy: .newlines)
        return lines.filter { line in
            line.contains("1.") || line.contains("2.") || line.contains("3.") ||
            line.contains("•") || line.contains("-")
        }.prefix(3).map { String($0.trimmingCharacters(in: .whitespaces)) }
    }
    
    private func parseInvestmentRecommendations(from text: String) -> [InvestmentRecommendation] {
        // Simplified parsing - in production, use more sophisticated NLP
        let lines = text.components(separatedBy: .newlines)
        var recommendations: [InvestmentRecommendation] = []
        
        for line in lines {
            if line.contains("BUY") || line.contains("HOLD") || line.contains("SELL") {
                // Extract basic info - this is a simplified implementation
                let recommendation = InvestmentRecommendation(
                    symbol: extractSymbol(from: line) ?? "UNKNOWN",
                    name: extractCompanyName(from: line) ?? "Unknown Company",
                    recommendationType: extractRecommendationType(from: line) ?? "HOLD",
                    confidence: 0.8,
                    expectedReturn: extractExpectedReturn(from: line) ?? 8.0,
                    riskLevel: extractRiskLevel(from: line) ?? "Medium",
                    reasoning: line
                )
                recommendations.append(recommendation)
            }
        }
        
        return recommendations
    }
    
    private func extractSymbol(from text: String) -> String? {
        let pattern = #"([A-Z]{1,5})"#
        if let regex = try? NSRegularExpression(pattern: pattern),
           let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)),
           let range = Range(match.range(at: 1), in: text) {
            return String(text[range])
        }
        return nil
    }
    
    private func extractCompanyName(from text: String) -> String? {
        // Simplified extraction
        return "Company Name" // In production, use proper NLP
    }
    
    private func extractRecommendationType(from text: String) -> String? {
        if text.uppercased().contains("BUY") { return "BUY" }
        if text.uppercased().contains("SELL") { return "SELL" }
        return "HOLD"
    }
    
    private func extractExpectedReturn(from text: String) -> Double? {
        let pattern = #"(\d+(?:\.\d+)?)%"#
        if let regex = try? NSRegularExpression(pattern: pattern),
           let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)),
           let range = Range(match.range(at: 1), in: text) {
            return Double(text[range])
        }
        return nil
    }
    
    private func extractRiskLevel(from text: String) -> String? {
        if text.lowercased().contains("low") { return "Low" }
        if text.lowercased().contains("high") { return "High" }
        return "Medium"
    }
    
    private func createMarketDescription(_ marketData: MarketData) -> String {
        var description = "Market Indices:\n"
        for index in marketData.indices {
            description += "- \(index.name): \(index.value) (\(index.changePercent)%)\n"
        }
        
        description += "\nTop Gainers:\n"
        for gainer in marketData.topGainers.prefix(3) {
            description += "- \(gainer.symbol): +\(gainer.changePercent)%\n"
        }
        
        description += "\nTop Losers:\n"
        for loser in marketData.topLosers.prefix(3) {
            description += "- \(loser.symbol): \(loser.changePercent)%\n"
        }
        
        return description
    }
    
    private func createHistoricalDataDescription(symbol: String, data: [HistoricalDataPoint]) -> String {
        guard !data.isEmpty else { return "No historical data available" }
        
        let latest = data.last!
        let oldest = data.first!
        let totalReturn = ((latest.close - oldest.open) / oldest.open) * 100
        
        return """
        Symbol: \(symbol)
        Period: \(data.count) data points
        Starting Price: $\(oldest.open)
        Current Price: $\(latest.close)
        Total Return: \(totalReturn)%
        Highest: $\(data.max(by: { $0.high < $1.high })?.high ?? 0)
        Lowest: $\(data.min(by: { $0.low < $1.low })?.low ?? 0)
        """
    }
}

// MARK: - Gemini API Models
struct GeminiRequest: Codable {
    let contents: [GeminiContent]
}

struct GeminiContent: Codable {
    let parts: [GeminiPart]
}

struct GeminiPart: Codable {
    let text: String
}

struct GeminiResponse: Codable {
    let candidates: [GeminiCandidate]?
}

struct GeminiCandidate: Codable {
    let content: GeminiContent?
    let finishReason: String?
}

// MARK: - AI Service Manager
class AIServiceManager: ObservableObject {
    static let shared = AIServiceManager()
    
    private let geminiService = GeminiAIService()
    @Published var isLoading = false
    @Published var error: APIError?
    
    private init() {}
    
    func getChatResponse(message: String) async -> String {
        isLoading = true
        error = nil

        do {
            let response = try await geminiService.generateResponse(prompt: message)
            DispatchQueue.main.async {
                self.isLoading = false
            }
            return response.text
        } catch let apiError as APIError {
            DispatchQueue.main.async {
                self.error = apiError
                self.isLoading = false
            }
            return "I'm sorry, I'm having trouble processing your request right now. Please try again later."
        } catch {
            DispatchQueue.main.async {
                self.error = APIError(code: "UNKNOWN", message: error.localizedDescription, details: nil)
                self.isLoading = false
            }
            return "I'm experiencing technical difficulties. Please try again."
        }
    }

    // MARK: - Content Generation for Feed

    func summarizeContent(_ content: String, maxLength: Int = 200) async throws -> String {
        let prompt = """
        Summarize the following financial content in a Gen Z-friendly tone.
        Keep it under \(maxLength) characters and make it engaging:

        \(content)
        """

        let response = try await geminiService.generateResponse(prompt: prompt)
        return response.text
    }

    func generateTags(for content: String, interests: [String]) async throws -> [String] {
        let prompt = """
        Generate 3-5 relevant hashtags for this financial content based on these user interests: \(interests.joined(separator: ", "))

        Content: \(content)

        Return only the hashtags without the # symbol, separated by commas.
        """

        let response = try await geminiService.generateResponse(prompt: prompt)
        return response.text.components(separatedBy: ",").map { $0.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines) }
    }

    func categorizeContent(_ content: String) async throws -> FeedCategory {
        let prompt = """
        Categorize this financial content into one of these categories:
        - stocks
        - crypto
        - news
        - education
        - investing

        Content: \(content)

        Return only the category name.
        """

        let response = try await geminiService.generateResponse(prompt: prompt)
        let categoryString = response.text.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines).lowercased()

        switch categoryString {
        case "stocks": return .stocks
        case "crypto": return .crypto
        case "news": return .news
        case "education": return .education
        case "investing": return .stocks
        default: return .news
        }
    }

    func calculateVibeScore(_ content: String, for preferences: UserPreferences) async throws -> Int {
        let prompt = """
        Rate how "viby" and engaging this financial content would be for a Gen Z user with these interests: \(preferences.interests.joined(separator: ", "))

        Content: \(content)

        Return a score from 1-100 where:
        - 80-100: Super viby, trending, exciting
        - 60-79: Pretty good, relevant
        - 40-59: Okay, somewhat interesting
        - 20-39: Meh, not very engaging
        - 1-19: Boring, not viby at all

        Return only the number.
        """

        let response = try await geminiService.generateResponse(prompt: prompt)
        return Int(response.text.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)) ?? 50
    }

    func generateInvestmentSuggestion(based content: String, for preferences: UserPreferences) async throws -> InvestmentSuggestion? {
        let prompt = """
        Based on this financial content and user preferences, suggest a micro-investment opportunity.
        User interests: \(preferences.interests.joined(separator: ", "))
        User goals: \(preferences.goals.joined(separator: ", "))

        Content: \(content)

        If there's a relevant investment opportunity, respond with:
        Symbol: [STOCK_SYMBOL]
        Amount: [1-50]
        Description: [Brief explanation why this is a good micro-investment]

        If no relevant investment opportunity, respond with: NONE
        """

        let response = try await geminiService.generateResponse(prompt: prompt)
        let text = response.text.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)

        if text == "NONE" {
            return nil
        }

        // Parse the response
        let lines = text.components(separatedBy: "\n")
        var symbol = ""
        var amount = 10.0
        var description = ""

        for line in lines {
            if line.hasPrefix("Symbol:") {
                symbol = line.replacingOccurrences(of: "Symbol:", with: "").trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
            } else if line.hasPrefix("Amount:") {
                let amountString = line.replacingOccurrences(of: "Amount:", with: "").trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
                amount = Double(amountString) ?? 10.0
            } else if line.hasPrefix("Description:") {
                description = line.replacingOccurrences(of: "Description:", with: "").trimmingCharacters(in: CharacterSet.whitespacesAndNewlines)
            }
        }

        if !symbol.isEmpty && !description.isEmpty {
            return InvestmentSuggestion(
                amount: amount,
                asset: "Investment Opportunity",
                symbol: symbol,
                reasoning: description,
                riskLevel: .moderate,
                potentialReturn: "5-15% annually"
            )
        }

        return nil
    }
    
    func getPortfolioAnalysis(portfolio: Portfolio) async -> PortfolioAnalysis? {
        isLoading = true
        error = nil
        
        do {
            let analysis = try await geminiService.analyzePortfolio(portfolio: portfolio)
            DispatchQueue.main.async {
                self.isLoading = false
            }
            return analysis
        } catch let apiError as APIError {
            DispatchQueue.main.async {
                self.error = apiError
                self.isLoading = false
            }
            return nil
        } catch {
            DispatchQueue.main.async {
                self.error = APIError(code: "UNKNOWN", message: error.localizedDescription, details: nil)
                self.isLoading = false
            }
            return nil
        }
    }
}
