//
//  NewsService.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - News Service
class NewsService {
    static let shared = NewsService()

    private let newsAPIKey = "********************************" // Your NewsAPI key
    private let session = URLSession.shared

    private init() {}

    // MARK: - News API Integration

    func fetchNews(query: String, limit: Int = 20) async throws -> [NewsArticle] {
        let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let url = URL(string: "https://newsapi.org/v2/everything?q=\(encodedQuery)&language=en&sortBy=publishedAt&pageSize=\(limit)&apiKey=\(newsAPIKey)")!

        var request = URLRequest(url: url)
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NewsError.requestFailed
        }

        let newsResponse = try JSONDecoder().decode(NewsAPIResponse.self, from: data)

        return newsResponse.articles.compactMap { article in
            guard let url = article.url,
                  let publishedAt = article.publishedAt else { return nil }

            return NewsArticle(
                title: article.title,
                content: article.description ?? article.content ?? "",
                author: article.author,
                url: url,
                publishedAt: publishedAt
            )
        }
    }

    func fetchFinanceNews() async throws -> [NewsArticle] {
        return try await fetchNews(query: "finance OR investing OR stocks OR cryptocurrency OR market")
    }

    func fetchTrendingNews() async throws -> [NewsArticle] {
        return try await fetchNews(query: "trending stocks OR viral investments OR meme stocks")
    }

    func fetchNewsByCategory(_ category: String) async throws -> [NewsArticle] {
        let query = getCategoryQuery(for: category)
        return try await fetchNews(query: query)
    }

    private func getCategoryQuery(for category: String) -> String {
        switch category.lowercased() {
        case "stocks":
            return "stock market OR equity OR shares"
        case "crypto":
            return "cryptocurrency OR bitcoin OR ethereum OR crypto"
        case "etfs":
            return "ETF OR exchange traded fund"
        case "gaming":
            return "gaming stocks OR esports investing OR video game companies"
        case "sustainability":
            return "ESG investing OR sustainable finance OR green energy stocks"
        case "tech":
            return "technology stocks OR tech companies OR innovation"
        case "lifestyle":
            return "lifestyle investing OR consumer brands OR retail stocks"
        default:
            return "finance OR investing"
        }
    }

    // MARK: - Social Media Integration (Placeholder - Reddit skipped for now)

    func fetchSocialMedia(query: String, limit: Int = 10) async throws -> [SocialPost] {
        // Placeholder - return empty array for now
        // TODO: Implement Reddit API or other social media source later
        return []
    }

    func fetchFinancialPosts() async throws -> [SocialPost] {
        // Placeholder - return empty array for now
        return []
    }

    func fetchTrendingFinancePosts() async throws -> [SocialPost] {
        // Placeholder - return empty array for now
        return []
    }

    // MARK: - Content Filtering

    func filterContentForAge(articles: [NewsArticle]) -> [NewsArticle] {
        // Filter out inappropriate content for Gen Z audience
        let inappropriateKeywords = ["adult", "gambling", "explicit"]

        return articles.filter { article in
            let content = (article.title + " " + article.content).lowercased()
            return !inappropriateKeywords.contains { content.contains($0) }
        }
    }

    func filterRelevantContent(articles: [NewsArticle], for interests: [String]) -> [NewsArticle] {
        guard !interests.isEmpty else { return articles }

        return articles.filter { article in
            let content = (article.title + " " + article.content).lowercased()
            return interests.contains { interest in
                content.contains(interest.lowercased())
            }
        }
    }

    // MARK: - Caching

    private var newsCache: [String: (articles: [NewsArticle], timestamp: Date)] = [:]
    private let cacheTimeout: TimeInterval = 300 // 5 minutes

    func getCachedNews(for query: String) -> [NewsArticle]? {
        guard let cached = newsCache[query],
              Date().timeIntervalSince(cached.timestamp) < cacheTimeout else {
            return nil
        }
        return cached.articles
    }

    func cacheNews(_ articles: [NewsArticle], for query: String) {
        newsCache[query] = (articles, Date())
    }

    func clearCache() {
        newsCache.removeAll()
    }
}

// MARK: - API Response Models

struct NewsAPIResponse: Codable {
    let status: String
    let totalResults: Int
    let articles: [NewsAPIArticle]
}

struct NewsAPIArticle: Codable {
    let source: NewsSource
    let author: String?
    let title: String
    let description: String?
    let url: String?
    let urlToImage: String?
    let publishedAt: Date?
    let content: String?

    private enum CodingKeys: String, CodingKey {
        case source, author, title, description, url, urlToImage, publishedAt, content
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        source = try container.decode(NewsSource.self, forKey: .source)
        author = try container.decodeIfPresent(String.self, forKey: .author)
        title = try container.decode(String.self, forKey: .title)
        description = try container.decodeIfPresent(String.self, forKey: .description)
        url = try container.decodeIfPresent(String.self, forKey: .url)
        urlToImage = try container.decodeIfPresent(String.self, forKey: .urlToImage)
        content = try container.decodeIfPresent(String.self, forKey: .content)

        // Parse date
        if let dateString = try container.decodeIfPresent(String.self, forKey: .publishedAt) {
            let formatter = ISO8601DateFormatter()
            publishedAt = formatter.date(from: dateString)
        } else {
            publishedAt = nil
        }
    }
}

struct NewsSource: Codable {
    let id: String?
    let name: String
}

struct TwitterAPIResponse: Codable {
    let data: [Tweet]?
    let meta: TwitterMeta?
}

struct Tweet: Codable {
    let id: String
    let text: String
    let authorId: String
    let createdAt: Date?

    private enum CodingKeys: String, CodingKey {
        case id, text
        case authorId = "author_id"
        case createdAt = "created_at"
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        text = try container.decode(String.self, forKey: .text)
        authorId = try container.decode(String.self, forKey: .authorId)

        if let dateString = try container.decodeIfPresent(String.self, forKey: .createdAt) {
            let formatter = ISO8601DateFormatter()
            createdAt = formatter.date(from: dateString)
        } else {
            createdAt = nil
        }
    }
}

struct TwitterMeta: Codable {
    let resultCount: Int

    private enum CodingKeys: String, CodingKey {
        case resultCount = "result_count"
    }
}

// MARK: - Reddit API Models
struct RedditAPIResponse: Codable {
    let data: RedditData
}

struct RedditData: Codable {
    let children: [RedditPost]
}

struct RedditPost: Codable {
    let data: RedditPostData
}

struct RedditPostData: Codable {
    let title: String
    let author: String
    let selftext: String?
    let permalink: String
    let createdUtc: Double

    private enum CodingKeys: String, CodingKey {
        case title, author, selftext, permalink
        case createdUtc = "created_utc"
    }
}

// MARK: - News Errors
enum NewsError: LocalizedError {
    case requestFailed
    case invalidResponse
    case rateLimitExceeded
    case socialMediaFailed
    case noResults

    var errorDescription: String? {
        switch self {
        case .requestFailed:
            return "Failed to fetch news"
        case .invalidResponse:
            return "Invalid news response"
        case .rateLimitExceeded:
            return "News API rate limit exceeded"
        case .socialMediaFailed:
            return "Failed to fetch social media content"
        case .noResults:
            return "No news results found"
        }
    }
}
