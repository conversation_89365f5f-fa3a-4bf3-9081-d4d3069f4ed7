//
//  AlpacaService.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

class AlpacaService {
    static let shared = AlpacaService()
    
    private var apiKey: String = ""
    private var secretKey: String = ""
    private var isPaper: Bool = true
    private let session = URLSession.shared
    
    private var baseURL: String {
        return isPaper ? "https://paper-api.alpaca.markets" : "https://api.alpaca.markets"
    }
    
    private init() {}
    
    // MARK: - Configuration
    
    func configure(apiKey: String, secretKey: String, isPaper: Bool = true) {
        self.apiKey = apiKey
        self.secretKey = secretKey
        self.isPaper = isPaper
    }
    
    func disconnect() {
        self.apiKey = ""
        self.secretKey = ""
        self.isPaper = true
    }
    
    // MARK: - Account Information
    
    func getAccountInfo() async throws -> AlpacaAccountInfo {
        let url = URL(string: "\(baseURL)/v2/account")!
        var request = URLRequest(url: url)
        request.setValue(apiKey, forHTTPHeaderField: "APCA-API-KEY-ID")
        request.setValue(secretKey, forHTTPHeaderField: "APCA-API-SECRET-KEY")
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw AlpacaError.accountInfoFailed
        }
        
        let accountResponse = try JSONDecoder().decode(AlpacaAccountResponse.self, from: data)
        
        return AlpacaAccountInfo(
            id: accountResponse.id,
            accountNumber: accountResponse.accountNumber,
            status: accountResponse.status,
            currency: accountResponse.currency,
            buyingPower: Double(accountResponse.buyingPower) ?? 0,
            cash: Double(accountResponse.cash) ?? 0,
            portfolioValue: Double(accountResponse.portfolioValue) ?? 0,
            equity: Double(accountResponse.equity) ?? 0,
            lastEquity: Double(accountResponse.lastEquity) ?? 0,
            dayChange: (Double(accountResponse.equity) ?? 0) - (Double(accountResponse.lastEquity) ?? 0),
            dayChangePercent: calculateDayChangePercent(
                current: Double(accountResponse.equity) ?? 0,
                previous: Double(accountResponse.lastEquity) ?? 0
            ),
            patternDayTrader: accountResponse.patternDayTrader,
            tradingBlocked: accountResponse.tradingBlocked,
            transfersBlocked: accountResponse.transfersBlocked
        )
    }
    
    // MARK: - Positions
    
    func getPositions() async throws -> [AlpacaPosition] {
        let url = URL(string: "\(baseURL)/v2/positions")!
        var request = URLRequest(url: url)
        request.setValue(apiKey, forHTTPHeaderField: "APCA-API-KEY-ID")
        request.setValue(secretKey, forHTTPHeaderField: "APCA-API-SECRET-KEY")
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw AlpacaError.positionsFailed
        }
        
        let positions = try JSONDecoder().decode([AlpacaPositionResponse].self, from: data)
        
        return positions.map { position in
            AlpacaPosition(
                symbol: position.symbol,
                qty: Double(position.qty) ?? 0,
                marketValue: Double(position.marketValue) ?? 0,
                costBasis: Double(position.costBasis) ?? 0,
                unrealizedPL: Double(position.unrealizedPL) ?? 0,
                unrealizedPLPercent: Double(position.unrealizedPLPercent) ?? 0,
                currentPrice: Double(position.currentPrice) ?? 0,
                side: position.side
            )
        }
    }
    
    func getPosition(symbol: String) async throws -> AlpacaPosition? {
        let url = URL(string: "\(baseURL)/v2/positions/\(symbol)")!
        var request = URLRequest(url: url)
        request.setValue(apiKey, forHTTPHeaderField: "APCA-API-KEY-ID")
        request.setValue(secretKey, forHTTPHeaderField: "APCA-API-SECRET-KEY")
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw AlpacaError.positionsFailed
        }
        
        if httpResponse.statusCode == 404 {
            return nil // No position found
        }
        
        guard httpResponse.statusCode == 200 else {
            throw AlpacaError.positionsFailed
        }
        
        let position = try JSONDecoder().decode(AlpacaPositionResponse.self, from: data)
        
        return AlpacaPosition(
            symbol: position.symbol,
            qty: Double(position.qty) ?? 0,
            marketValue: Double(position.marketValue) ?? 0,
            costBasis: Double(position.costBasis) ?? 0,
            unrealizedPL: Double(position.unrealizedPL) ?? 0,
            unrealizedPLPercent: Double(position.unrealizedPLPercent) ?? 0,
            currentPrice: Double(position.currentPrice) ?? 0,
            side: position.side
        )
    }
    
    // MARK: - Orders
    
    func placeOrder(symbol: String, qty: Double, side: OrderSide, type: OrderType, timeInForce: TimeInForce) async throws -> RealOrder {
        let url = URL(string: "\(baseURL)/v2/orders")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "APCA-API-KEY-ID")
        request.setValue(secretKey, forHTTPHeaderField: "APCA-API-SECRET-KEY")
        
        let orderRequest = AlpacaOrderRequest(
            symbol: symbol,
            qty: qty,
            side: side.rawValue,
            type: type.rawValue,
            timeInForce: timeInForce.rawValue
        )
        
        request.httpBody = try JSONEncoder().encode(orderRequest)
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 201 else {
            throw AlpacaError.orderFailed
        }
        
        let orderResponse = try JSONDecoder().decode(AlpacaOrderResponse.self, from: data)
        
        return RealOrder(
            id: orderResponse.id,
            symbol: symbol,
            qty: qty,
            side: side,
            type: type,
            timeInForce: timeInForce,
            status: orderResponse.status,
            filledQty: Double(orderResponse.filledQty ?? "0") ?? 0,
            filledAvgPrice: Double(orderResponse.filledAvgPrice ?? "0"),
            createdAt: parseDate(orderResponse.createdAt),
            updatedAt: parseDate(orderResponse.updatedAt),
            filledAt: orderResponse.filledAt != nil ? parseDate(orderResponse.filledAt!) : nil
        )
    }
    
    func placeLimitOrder(symbol: String, qty: Double, side: OrderSide, limitPrice: Double, timeInForce: TimeInForce) async throws -> RealOrder {
        let url = URL(string: "\(baseURL)/v2/orders")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(apiKey, forHTTPHeaderField: "APCA-API-KEY-ID")
        request.setValue(secretKey, forHTTPHeaderField: "APCA-API-SECRET-KEY")
        
        let orderRequest = AlpacaLimitOrderRequest(
            symbol: symbol,
            qty: qty,
            side: side.rawValue,
            type: "limit",
            timeInForce: timeInForce.rawValue,
            limitPrice: limitPrice
        )
        
        request.httpBody = try JSONEncoder().encode(orderRequest)
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 201 else {
            throw AlpacaError.orderFailed
        }
        
        let orderResponse = try JSONDecoder().decode(AlpacaOrderResponse.self, from: data)
        
        return RealOrder(
            id: orderResponse.id,
            symbol: symbol,
            qty: qty,
            side: side,
            type: .limit,
            timeInForce: timeInForce,
            status: orderResponse.status,
            filledQty: Double(orderResponse.filledQty ?? "0") ?? 0,
            filledAvgPrice: Double(orderResponse.filledAvgPrice ?? "0"),
            limitPrice: limitPrice,
            createdAt: parseDate(orderResponse.createdAt),
            updatedAt: parseDate(orderResponse.updatedAt),
            filledAt: orderResponse.filledAt != nil ? parseDate(orderResponse.filledAt!) : nil
        )
    }
    
    func getOrders(status: OrderStatus = .all, limit: Int = 50) async throws -> [RealOrder] {
        var urlComponents = URLComponents(string: "\(baseURL)/v2/orders")!
        urlComponents.queryItems = [
            URLQueryItem(name: "status", value: status.rawValue),
            URLQueryItem(name: "limit", value: String(limit)),
            URLQueryItem(name: "direction", value: "desc")
        ]
        
        guard let url = urlComponents.url else {
            throw AlpacaError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.setValue(apiKey, forHTTPHeaderField: "APCA-API-KEY-ID")
        request.setValue(secretKey, forHTTPHeaderField: "APCA-API-SECRET-KEY")
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw AlpacaError.ordersFailed
        }
        
        let ordersResponse = try JSONDecoder().decode([AlpacaOrderResponse].self, from: data)
        
        return ordersResponse.map { order in
            RealOrder(
                id: order.id,
                symbol: order.symbol,
                qty: Double(order.qty) ?? 0,
                side: OrderSide(rawValue: order.side) ?? .buy,
                type: OrderType(rawValue: order.type) ?? .market,
                timeInForce: TimeInForce(rawValue: order.timeInForce) ?? .gtc,
                status: order.status,
                filledQty: Double(order.filledQty ?? "0") ?? 0,
                filledAvgPrice: Double(order.filledAvgPrice ?? "0"),
                limitPrice: Double(order.limitPrice ?? "0"),
                createdAt: parseDate(order.createdAt),
                updatedAt: parseDate(order.updatedAt),
                filledAt: order.filledAt != nil ? parseDate(order.filledAt!) : nil
            )
        }
    }
    
    func cancelOrder(_ orderId: String) async throws {
        let url = URL(string: "\(baseURL)/v2/orders/\(orderId)")!
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.setValue(apiKey, forHTTPHeaderField: "APCA-API-KEY-ID")
        request.setValue(secretKey, forHTTPHeaderField: "APCA-API-SECRET-KEY")
        
        let (_, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 204 else {
            throw AlpacaError.cancelOrderFailed
        }
    }
    
    // MARK: - Market Data
    
    func getLatestQuote(symbol: String) async throws -> AlpacaQuote {
        let url = URL(string: "\(baseURL)/v2/stocks/\(symbol)/quotes/latest")!
        var request = URLRequest(url: url)
        request.setValue(apiKey, forHTTPHeaderField: "APCA-API-KEY-ID")
        request.setValue(secretKey, forHTTPHeaderField: "APCA-API-SECRET-KEY")
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw AlpacaError.quoteFailed
        }
        
        let quoteResponse = try JSONDecoder().decode(AlpacaQuoteResponse.self, from: data)
        
        return AlpacaQuote(
            symbol: symbol,
            bidPrice: quoteResponse.quote.bidPrice,
            bidSize: quoteResponse.quote.bidSize,
            askPrice: quoteResponse.quote.askPrice,
            askSize: quoteResponse.quote.askSize,
            timestamp: parseDate(quoteResponse.quote.timestamp)
        )
    }
    
    // MARK: - Helper Methods
    
    private func calculateDayChangePercent(current: Double, previous: Double) -> Double {
        guard previous > 0 else { return 0 }
        return ((current - previous) / previous) * 100
    }
    
    private func parseDate(_ dateString: String) -> Date {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        return formatter.date(from: dateString) ?? Date()
    }
}

// MARK: - Alpaca Errors
enum AlpacaError: Error, LocalizedError {
    case invalidURL
    case accountInfoFailed
    case positionsFailed
    case orderFailed
    case ordersFailed
    case cancelOrderFailed
    case quoteFailed
    case unauthorized
    case rateLimited
    case serverError
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .accountInfoFailed:
            return "Failed to fetch account information"
        case .positionsFailed:
            return "Failed to fetch positions"
        case .orderFailed:
            return "Failed to place order"
        case .ordersFailed:
            return "Failed to fetch orders"
        case .cancelOrderFailed:
            return "Failed to cancel order"
        case .quoteFailed:
            return "Failed to fetch quote"
        case .unauthorized:
            return "Unauthorized access - check API credentials"
        case .rateLimited:
            return "Rate limit exceeded - please try again later"
        case .serverError:
            return "Server error - please try again later"
        }
    }
}
