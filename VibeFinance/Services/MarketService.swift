//
//  MarketService.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

// MARK: - Market Service
class MarketService {
    static let shared = MarketService()

    private let polygonAPIKey = "km3MBsQp1wgDfetdB_jcW8nQommpcVtY"
    private let alpacaAPIKey = "your-alpaca-api-key"
    private let alpacaSecretKey = "your-alpaca-secret-key"
    private let session = URLSession.shared

    // Performance optimization components
    private let cacheManager = CacheManager.shared
    private let networkOptimizer = NetworkOptimizer.shared

    private init() {}

    // MARK: - Performance Optimized Methods

    func getStockData(symbol: String) async -> StockPrice? {
        // Check cache first
        if let cachedData = getCachedQuote(for: symbol) {
            return cachedData
        }

        do {
            let stockPrice = try await getQuote(symbol: symbol)
            cacheQuote(stockPrice, for: symbol)
            return stockPrice
        } catch {
            DevelopmentConfig.log("Failed to fetch stock data for \(symbol): \(error)", category: "MARKET")
            return nil
        }
    }

    // MARK: - Real-time Market Data (Polygon.io)

    func getQuote(symbol: String) async throws -> StockPrice {
        let url = URL(string: "https://api.polygon.io/v2/last/trade/\(symbol)?apikey=\(polygonAPIKey)")!

        let (data, response) = try await session.data(from: url)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw MarketError.requestFailed
        }

        let polygonResponse = try JSONDecoder().decode(PolygonTradeResponse.self, from: data)

        // Get additional data
        let details = try await getStockDetails(symbol: symbol)
        let previousClose = try await getPreviousClose(symbol: symbol)

        let change = polygonResponse.results.price - previousClose
        let changePercent = (change / previousClose) * 100

        return StockPrice(
            symbol: symbol,
            price: polygonResponse.results.price,
            change: change,
            changePercent: changePercent,
            timestamp: Date()
        )
    }

    func searchStocks(query: String) async throws -> [StockPrice] {
        let encodedQuery = query.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let url = URL(string: "https://api.polygon.io/v3/reference/tickers?search=\(encodedQuery)&active=true&limit=10&apikey=\(polygonAPIKey)")!

        let (data, response) = try await session.data(from: url)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw MarketError.searchFailed
        }

        let searchResponse = try JSONDecoder().decode(PolygonSearchResponse.self, from: data)

        var results: [StockPrice] = []

        for ticker in searchResponse.results.prefix(5) {
            do {
                let stockPrice = try await getQuote(symbol: ticker.ticker)
                results.append(stockPrice)
            } catch {
                // Skip failed quotes
                continue
            }
        }

        return results
    }

    func getStockDetails(symbol: String) async throws -> StockDetails {
        let url = URL(string: "https://api.polygon.io/v3/reference/tickers/\(symbol)?apikey=\(polygonAPIKey)")!

        let (data, response) = try await session.data(from: url)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw MarketError.detailsFailed
        }

        let detailsResponse = try JSONDecoder().decode(PolygonDetailsResponse.self, from: data)

        return StockDetails(
            name: detailsResponse.results.name,
            marketCap: detailsResponse.results.marketCap,
            peRatio: nil, // Would need additional API call
            dividendYield: nil, // Would need additional API call
            high52Week: nil, // Would need additional API call
            low52Week: nil // Would need additional API call
        )
    }

    func getPreviousClose(symbol: String) async throws -> Double {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()
        let dateString = formatter.string(from: yesterday)

        let url = URL(string: "https://api.polygon.io/v1/open-close/\(symbol)/\(dateString)?adjusted=true&apikey=\(polygonAPIKey)")!

        let (data, response) = try await session.data(from: url)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw MarketError.previousCloseFailed
        }

        let closeResponse = try JSONDecoder().decode(PolygonCloseResponse.self, from: data)
        return closeResponse.close
    }

    // MARK: - Market Trends

    func getTrendingStocks() async throws -> [StockPrice] {
        // Get popular stocks for Gen Z
        let popularSymbols = ["AAPL", "TSLA", "NVDA", "AMZN", "GOOGL", "META", "MSFT", "NFLX", "AMD", "PLTR"]

        var trendingStocks: [StockPrice] = []

        for symbol in popularSymbols.prefix(5) {
            do {
                let stockPrice = try await getQuote(symbol: symbol)
                trendingStocks.append(stockPrice)
            } catch {
                continue
            }
        }

        // Sort by change percentage (biggest movers first)
        return trendingStocks.sorted { abs($0.changePercent) > abs($1.changePercent) }
    }

    func getGamingStocks() async throws -> [StockPrice] {
        let gamingSymbols = ["ATVI", "EA", "TTWO", "RBLX", "U", "GAMR"]
        return try await getStocksForSymbols(gamingSymbols)
    }

    func getSustainabilityStocks() async throws -> [StockPrice] {
        let esgSymbols = ["TSLA", "ENPH", "SEDG", "ICLN", "ESG"]
        return try await getStocksForSymbols(esgSymbols)
    }

    func getTechStocks() async throws -> [StockPrice] {
        let techSymbols = ["AAPL", "GOOGL", "MSFT", "NVDA", "AMD", "CRM"]
        return try await getStocksForSymbols(techSymbols)
    }

    private func getStocksForSymbols(_ symbols: [String]) async throws -> [StockPrice] {
        var stocks: [StockPrice] = []

        for symbol in symbols {
            do {
                let stockPrice = try await getQuote(symbol: symbol)
                stocks.append(stockPrice)
            } catch {
                continue
            }
        }

        return stocks
    }

    // MARK: - Real Trading (Alpaca - Pro tier only)

    func placeOrder(symbol: String, quantity: Double, side: OrderSide, type: OrderType = .market) async throws -> Order {
        let url = URL(string: "https://paper-api.alpaca.markets/v2/orders")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(alpacaAPIKey, forHTTPHeaderField: "APCA-API-KEY-ID")
        request.setValue(alpacaSecretKey, forHTTPHeaderField: "APCA-API-SECRET-KEY")

        let orderRequest = AlpacaOrderRequest(
            symbol: symbol,
            qty: quantity,
            side: side.rawValue,
            type: type.rawValue,
            timeInForce: "gtc"
        )

        request.httpBody = try JSONEncoder().encode(orderRequest)

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 201 else {
            throw MarketError.orderFailed
        }

        let orderResponse = try JSONDecoder().decode(AlpacaOrderResponse.self, from: data)

        return Order(
            orderId: orderResponse.id,
            symbol: symbol,
            quantity: quantity,
            side: side.rawValue,
            status: orderResponse.status,
            orderType: type.rawValue,
            createdAt: orderResponse.createdAt
        )
    }

    func getAccountInfo() async throws -> AccountInfo {
        let url = URL(string: "https://paper-api.alpaca.markets/v2/account")!
        var request = URLRequest(url: url)
        request.setValue(alpacaAPIKey, forHTTPHeaderField: "APCA-API-KEY-ID")
        request.setValue(alpacaSecretKey, forHTTPHeaderField: "APCA-API-SECRET-KEY")

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw MarketError.accountInfoFailed
        }

        let accountResponse = try JSONDecoder().decode(AlpacaAccountResponse.self, from: data)

        return AccountInfo(
            buyingPower: Double(accountResponse.buyingPower) ?? 0,
            cash: Double(accountResponse.cash) ?? 0,
            portfolioValue: Double(accountResponse.portfolioValue) ?? 0
        )
    }

    // MARK: - Enhanced Caching

    private var quoteCache: [String: (data: StockPrice, timestamp: Date)] = [:]
    private let cacheTimeout: TimeInterval = 60 // 1 minute
    private let cacheManager = CacheManager.shared
    private let networkOptimizer = NetworkOptimizer.shared

    func getCachedQuote(for symbol: String) -> StockPrice? {
        // Check enhanced cache first
        if let cachedData: StockPrice = cacheManager.get(StockPrice.self, forKey: "stock_\(symbol)") {
            return cachedData
        }

        // Fallback to legacy cache
        guard let cached = quoteCache[symbol],
              Date().timeIntervalSince(cached.timestamp) < cacheTimeout else {
            return nil
        }
        return cached.data
    }

    func cacheQuote(_ data: StockPrice, for symbol: String) {
        // Cache in enhanced cache manager
        cacheManager.set(data, forKey: "stock_\(symbol)", priority: .high, duration: cacheTimeout)

        // Keep legacy cache for compatibility
        quoteCache[symbol] = (data, Date())
    }

    func clearCache() {
        quoteCache.removeAll()
        // Clear enhanced cache entries for stocks
        // Note: In a real implementation, you'd track stock cache keys
    }
}

// MARK: - API Response Models

struct PolygonTradeResponse: Codable {
    let results: PolygonTrade
}

struct PolygonTrade: Codable {
    let price: Double
    let size: Int
    let timestamp: Int64

    private enum CodingKeys: String, CodingKey {
        case price = "p"
        case size = "s"
        case timestamp = "t"
    }
}

struct PolygonSearchResponse: Codable {
    let results: [PolygonTicker]
}

struct PolygonTicker: Codable {
    let ticker: String
    let name: String
}

struct PolygonDetailsResponse: Codable {
    let results: PolygonDetails
}

struct PolygonDetails: Codable {
    let name: String
    let marketCap: Double?

    private enum CodingKeys: String, CodingKey {
        case name
        case marketCap = "market_cap"
    }
}

struct PolygonCloseResponse: Codable {
    let close: Double
}

struct AlpacaOrderRequest: Codable {
    let symbol: String
    let qty: Double
    let side: String
    let type: String
    let timeInForce: String

    private enum CodingKeys: String, CodingKey {
        case symbol, qty, side, type
        case timeInForce = "time_in_force"
    }
}

struct AlpacaOrderResponse: Codable {
    let id: String
    let status: String
    let filledAvgPrice: Double?
    let createdAt: Date

    private enum CodingKeys: String, CodingKey {
        case id, status
        case filledAvgPrice = "filled_avg_price"
        case createdAt = "created_at"
    }
}

struct AlpacaAccountResponse: Codable {
    let buyingPower: String
    let cash: String
    let portfolioValue: String

    private enum CodingKeys: String, CodingKey {
        case buyingPower = "buying_power"
        case cash
        case portfolioValue = "portfolio_value"
    }
}

// MARK: - Trading Models

struct StockDetails {
    let name: String
    let marketCap: Double?
    let peRatio: Double?
    let dividendYield: Double?
    let high52Week: Double?
    let low52Week: Double?
}

// Order is now defined in DataModels.swift

struct AccountInfo {
    let buyingPower: Double
    let cash: Double
    let portfolioValue: Double
}

enum OrderSide: String, CaseIterable {
    case buy = "buy"
    case sell = "sell"
}

enum OrderType: String, CaseIterable {
    case market = "market"
    case limit = "limit"
}

enum OrderStatus: String, CaseIterable {
    case pending = "pending_new"
    case filled = "filled"
    case cancelled = "cancelled"
    case rejected = "rejected"
}

// MARK: - Market Errors
enum MarketError: LocalizedError {
    case requestFailed
    case searchFailed
    case detailsFailed
    case previousCloseFailed
    case orderFailed
    case accountInfoFailed
    case invalidSymbol
    case rateLimitExceeded

    var errorDescription: String? {
        switch self {
        case .requestFailed:
            return "Market data request failed"
        case .searchFailed:
            return "Stock search failed"
        case .detailsFailed:
            return "Failed to get stock details"
        case .previousCloseFailed:
            return "Failed to get previous close price"
        case .orderFailed:
            return "Order placement failed"
        case .accountInfoFailed:
            return "Failed to get account information"
        case .invalidSymbol:
            return "Invalid stock symbol"
        case .rateLimitExceeded:
            return "Market data rate limit exceeded"
        }
    }
}
