//
//  DevelopmentConfig.swift
//  WealthVibe - Vibe Finance iOS App
//
//  Created by MAGESH DHANASEKARAN on 5/24/25.
//

import Foundation

struct DevelopmentConfig {
    
    // MARK: - Development Flags

    /// Enable mock authentication for development (DISABLED FOR PRODUCTION)
    static let enableMockAuth = false

    /// Enable real API calls instead of mock data
    static let enableRealAPIs = true

    /// Skip onboarding in development
    static let skipOnboarding = false

    /// Enable debug logging
    static let enableDebugLogging = true

    /// Show developer tools in UI (DISABLED FOR PRODUCTION)
    static let showDeveloperTools = false

    /// Enable network request logging
    static let enableNetworkLogging = true

    /// Enable real-time market data updates
    static let enableRealTimeUpdates = true
    
    // MARK: - Mock Data Configuration
    
    /// Default mock user for quick login
    static let defaultMockUser = MockUser(
        id: UUID(),
        email: "<EMAIL>",
        username: "<PERSON><PERSON><PERSON>",
        firstName: "Dev",
        lastName: "User"
    )
    
    /// Available mock users for testing
    static let mockUsers: [MockUser] = [
        MockUser(
            id: UUID(),
            email: "<EMAIL>",
            username: "<PERSON><PERSON><PERSON>",
            firstName: "Dev",
            lastName: "User"
        ),
        MockUser(
            id: UUID(),
            email: "<EMAIL>",
            username: "TestUser",
            firstName: "Test",
            lastName: "User"
        ),
        MockUser(
            id: UUID(),
            email: "<EMAIL>",
            username: "DemoUser",
            firstName: "Demo",
            lastName: "User"
        ),
        MockUser(
            id: UUID(),
            email: "<EMAIL>",
            username: "InvestorPro",
            firstName: "Investor",
            lastName: "Pro"
        )
    ]
    
    // MARK: - API Configuration
    
    /// Use mock API responses
    static let useMockAPI = false
    
    /// API request timeout for development
    static let apiTimeout: TimeInterval = 30.0
    
    /// Enable API response caching
    static let enableAPICache = true
    
    // MARK: - UI Configuration
    
    /// Show component boundaries for debugging
    static let showComponentBounds = false
    
    /// Enable UI animations in development
    static let enableAnimations = true
    
    /// Show performance metrics
    static let showPerformanceMetrics = true

    /// Enable performance monitoring
    static let enablePerformanceMonitoring = true

    /// Enable cache debugging
    static let enableCacheDebugging = true

    /// Enable network optimization
    static let enableNetworkOptimization = true
    
    // MARK: - Feature Flags
    
    /// Enable experimental features
    static let enableExperimentalFeatures = true
    
    /// Enable Magic MCP integration
    static let enableMagicMCP = true
    
    /// Enable advanced debugging tools
    static let enableAdvancedDebugging = true
    
    // MARK: - Helper Methods
    
    /// Check if running in development mode
    static var isDevelopmentMode: Bool {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }
    
    /// Check if running in simulator
    static var isSimulator: Bool {
        #if targetEnvironment(simulator)
        return true
        #else
        return false
        #endif
    }
    
    /// Get current build configuration
    static var buildConfiguration: String {
        #if DEBUG
        return "Debug"
        #else
        return "Release"
        #endif
    }
    
    /// Print development info
    static func printDevelopmentInfo() {
        guard isDevelopmentMode else { return }
        
        print("🔧 Development Mode Enabled")
        print("📱 Simulator: \(isSimulator)")
        print("🏗️ Build: \(buildConfiguration)")
        print("🔐 Mock Auth: \(enableMockAuth)")
        print("🎨 Magic MCP: \(enableMagicMCP)")
        print("🐛 Debug Logging: \(enableDebugLogging)")
    }
}

// MARK: - Mock User Model

struct MockUser {
    let id: UUID
    let email: String
    let username: String
    let firstName: String
    let lastName: String
    
    var fullName: String {
        return "\(firstName) \(lastName)"
    }
    
    /// Convert to User model
    func toUser() -> User {
        return User(
            id: id,
            email: email,
            username: username
        )
    }
}

// MARK: - Development Utilities

extension DevelopmentConfig {
    
    /// Log development message
    static func log(_ message: String, category: String = "DEV") {
        guard isDevelopmentMode && enableDebugLogging else { return }
        print("[\(category)] \(message)")
    }
    
    /// Log network request
    static func logNetworkRequest(_ request: URLRequest) {
        guard isDevelopmentMode && enableNetworkLogging else { return }
        print("🌐 Network Request: \(request.httpMethod ?? "GET") \(request.url?.absoluteString ?? "Unknown URL")")
    }
    
    /// Log network response
    static func logNetworkResponse(_ response: URLResponse?, data: Data?) {
        guard isDevelopmentMode && enableNetworkLogging else { return }
        
        if let httpResponse = response as? HTTPURLResponse {
            print("🌐 Network Response: \(httpResponse.statusCode)")
            
            if let data = data, let responseString = String(data: data, encoding: .utf8) {
                print("📄 Response Data: \(responseString.prefix(200))...")
            }
        }
    }
    
    /// Show development alert
    static func showDevelopmentAlert(title: String, message: String) {
        guard isDevelopmentMode else { return }
        
        DispatchQueue.main.async {
            // This would typically show a system alert
            // For now, we'll just log it
            log("Alert - \(title): \(message)", category: "ALERT")
        }
    }
}
