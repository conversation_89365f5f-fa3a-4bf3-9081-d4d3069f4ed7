<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>A96A0DD6-9622-4A25-AE0E-4AA879D48182</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>A96A0DD6-9622-4A25-AE0E-4AA879D48182.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>VibeFinance project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>VibeFinance</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project VibeFinance with scheme VibeFinance</string>
			<key>timeStartedRecording</key>
			<real>772847645.61575198</real>
			<key>timeStoppedRecording</key>
			<real>772847645.93562698</real>
			<key>title</key>
			<string>Cleaning project VibeFinance with scheme VibeFinance</string>
			<key>uniqueIdentifier</key>
			<string>A96A0DD6-9622-4A25-AE0E-4AA879D48182</string>
		</dict>
		<key>F01E3B61-ECAF-4960-91AB-9A29688299A4</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>F01E3B61-ECAF-4960-91AB-9A29688299A4.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>VibeFinance project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>VibeFinance</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project VibeFinance with scheme VibeFinance</string>
			<key>timeStartedRecording</key>
			<real>772842296.01089299</real>
			<key>timeStoppedRecording</key>
			<real>772842296.291152</real>
			<key>title</key>
			<string>Cleaning project VibeFinance with scheme VibeFinance</string>
			<key>uniqueIdentifier</key>
			<string>F01E3B61-ECAF-4960-91AB-9A29688299A4</string>
		</dict>
	</dict>
</dict>
</plist>
