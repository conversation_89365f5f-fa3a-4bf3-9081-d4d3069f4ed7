<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>********-7D7C-4D13-B236-88D7B4E30C9B</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>********-7D7C-4D13-B236-88D7B4E30C9B.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>W</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>4</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>VibeFinance project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>VibeFinance</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Building project VibeFinance with scheme VibeFinance</string>
			<key>timeStartedRecording</key>
			<real>772841460.36168301</real>
			<key>timeStoppedRecording</key>
			<real>772841464.69047797</real>
			<key>title</key>
			<string>Building project VibeFinance with scheme VibeFinance</string>
			<key>uniqueIdentifier</key>
			<string>********-7D7C-4D13-B236-88D7B4E30C9B</string>
		</dict>
		<key>F01E3B61-ECAF-4960-91AB-9A29688299A4</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>F01E3B61-ECAF-4960-91AB-9A29688299A4.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>VibeFinance project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>VibeFinance</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning project VibeFinance with scheme VibeFinance</string>
			<key>timeStartedRecording</key>
			<real>772842296.01089299</real>
			<key>timeStoppedRecording</key>
			<real>772842296.291152</real>
			<key>title</key>
			<string>Cleaning project VibeFinance with scheme VibeFinance</string>
			<key>uniqueIdentifier</key>
			<string>F01E3B61-ECAF-4960-91AB-9A29688299A4</string>
		</dict>
	</dict>
</dict>
</plist>
