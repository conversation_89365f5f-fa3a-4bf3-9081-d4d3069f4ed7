{"buildConfigurations": [{"buildSettings": {"CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.md.VibeFinanceUITests", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "TEST_TARGET_NAME": "VibeFinance"}, "guid": "328d9e880d321015876a82f6be8a672b48b73e6f52dd2d89d1a21aef5372af0e", "name": "Debug"}, {"buildSettings": {"CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "GENERATE_INFOPLIST_FILE": "YES", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "com.md.VibeFinanceUITests", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "NO", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "TEST_TARGET_NAME": "VibeFinance"}, "guid": "328d9e880d321015876a82f6be8a672bd896ae036ee38b21c04df7683edf7552", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "328d9e880d321015876a82f6be8a672bc0c72960635203a1bc9e62084755e8be", "guid": "328d9e880d321015876a82f6be8a672b0b8b18334a644761ae8833bb11faa59e"}, {"fileReference": "328d9e880d321015876a82f6be8a672b0d562813cd81fd42e0e1c543022ef85a", "guid": "328d9e880d321015876a82f6be8a672b0405c833cf70a2b317d30636f398a193"}], "guid": "328d9e880d321015876a82f6be8a672bfdf3b2a8cee271ac6fe156a40b086ddc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "328d9e880d321015876a82f6be8a672b8b406e67c30729fa2c6af154754e1870", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "328d9e880d321015876a82f6be8a672b02cb57eba0c67fd1a78a3c6afa4a4e77", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "328d9e880d321015876a82f6be8a672bc87c97ee76012153448311260fe6f91f", "name": "VibeFinance"}], "guid": "328d9e880d321015876a82f6be8a672b65ba596a82fb0c76f54dafe4d0b359a9", "name": "VibeFinanceUITests", "performanceTestsBaselinesPath": "/Users/<USER>/Documents/VibeFinance/VibeFinance.xcodeproj/xcshareddata/xcbaselines/7CDCD1982DE21DA600693D4C.xcbaseline", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "328d9e880d321015876a82f6be8a672b197e9c8ea8a39a095e824adc04550b3c", "name": "VibeFinanceUITests.xctest", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle.ui-testing", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}