{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "QNW477Q52S", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "328d9e880d321015876a82f6be8a672bcd43c4c5bf92c38e6a455c8649ddbfb5", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "QNW477Q52S", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "328d9e880d321015876a82f6be8a672b8a87e839cd2d2d9fb3dae8fb321ce5bc", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672b6cf767a8a3b0a3e34a70ccc3a9b8d082", "path": "mcp.json", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672b7e5f09b0c0cc4303f915b38b1c22f1f0", "path": "mcp-with-keys.json", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b9287a90a2358fa4ded63dc487a3a5aa1", "name": ".cursor", "path": ".cursor", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672beb6dd4694e178b659a20e3f5c56afc4e", "path": "DevelopmentConfig.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b8b9824a0dd233d1bc951e8d72507e816", "name": "Config", "path": "Config", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b996d83edfffb33e7a5fd92e984af4780", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "328d9e880d321015876a82f6be8a672b23bc9c5a3a342790c78ae64e09741444", "path": "fix_auth_issues.sql", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "328d9e880d321015876a82f6be8a672b9f12952188dcd6dbce7c2f9492783069", "path": "setup.sql", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bf9442578ab67b46306ad87794713e57a", "name": "Database", "path": "Database", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b20f04dbde4b77176484d79f40ad71033", "path": "ADVANCED_ANALYTICS_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b8a667e640da46c7aa7a4e99feaa83934", "path": "API_INTEGRATIONS.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b83d138c514a2bc8cc20a0b5c571f2242", "path": "APPLE_INTELLIGENCE_REVAMP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b0cdf4db5133b8bc52088cc421f18171d", "path": "BUILD_SUCCESS_FINAL.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b17d1a2a31e0933b5da2ef053af4b5770", "path": "DEVELOPMENT_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b87d4c5bade928b1d1c3f0eb0980bc50d", "path": "FEATURES_OVERVIEW.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bcc73797bd4eda6c14d4925124a6b1786", "path": "FINAL_IMPLEMENTATION_SUMMARY.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b330787d167c6ae4ac06c804fd31fa94a", "path": "IMPLEMENTATION_COMPLETE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b806bc2d34c0672015d122abb47f34978", "path": "MAGIC_MCP_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b691efe14f08a9e4b0b42020c56f84c67", "path": "MCP_QUICK_REFERENCE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672beb107915fe58956f45c937474b32df08", "path": "MCP_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bce2989f2c63f30a74f84a87361028c6f", "path": "MOCK_AUTH_GUIDE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b58fe39aba80a10cfe12367a555d7e2c7", "path": "PAYMENT_PROCESSING_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b62756f7079e0347820553b59f1140c97", "path": "PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b0649c2f2ab56717850606cf67f10e54d", "path": "PROJECT_STATUS.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b41e3fe18323ff80268ff2ae2a84b9cad", "path": "REAL_TRADING_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bcc59da167dde17bad9ecc5c75fb5368d", "path": "SOLUTION_SUMMARY.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b49964b5d835c5464e4548d61b6459d64", "path": "TECHNICAL_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b901f3e78fc66f5d875144e339fc4d404", "path": "USER_GUIDE.md", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b612ea04739494fab9f240b2fe4ab41b4", "name": "docs", "path": "docs", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672bb61c28aed2292cae4c057265da8a290f", "name": "Intents", "path": "Intents", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bd4572fc3276b8533ac2c9e1b313e9e0e", "path": "AnalyticsManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b03812aa3ea0ca9ede623844de1438c3a", "path": "AuthManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6b6e01278ff8f1681f291d8b0001be1f", "path": "CacheManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b42e3cbde1005be7fa0e34d57239f18a3", "path": "ChatManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9e8a4f972ed723ac8fba7d463e56ce50", "path": "FeedManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b91569641171cade7515d4819c40cf887", "path": "ImageCacheManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b23ddb10b68687e7415735566a7248d48", "path": "NetworkOptimizer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b09d528dca69bddab91de63d1568507a7", "path": "NotificationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bbcda7ecfe2f091f928c0357f7dcb654f", "path": "PaymentManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf5d8ea69ce3c9ce04eda55cf0cbd2c7a", "path": "PerformanceManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672baf1f3ec636ec848e46f216aab04e37d3", "path": "QuestManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7e4ba612761dbb15378c2c8c386e0eea", "path": "RealTradingManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b69c70a98a9deca17d74dab1249102a0a", "path": "SimulatorManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bba7939ec2436164b399c5c85b4f034df", "path": "SquadManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7f61433e38896e416dd05a15d6bf4dd5", "path": "SubscriptionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672be55f9801cf7cb700e01ae692af65579a", "path": "UserManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b501749687bdf9ee431b2b4ee5315cd86", "name": "Managers", "path": "Managers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba874cf3a56cae35c13fddac2dcc35f1b", "path": "AnalyticsModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b57c0b0ec2b36d982013bd4177ec04aa0", "path": "Chat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bd19bb795b7e86264c035f99af475fe85", "path": "DataModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b5bf1a260d7bd2c6b2555ae918125401b", "path": "Feed.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc50c5ebe7fc1ac92964812116e3cd45f", "path": "PaymentModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b345f9172691178955514b4139db5dc12", "path": "<PERSON>.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bcce722014be2862e3cc973ca01f894d7", "path": "RealTradingModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b26822f7f48ffb3b43b59a97bf36e3c66", "path": "Simulator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b03bf8f3e262e9e66e4460dc407ca7e9e", "path": "Squad.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b1f9416f2457dd41faa0f46982d6810e3", "path": "User.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b3361f9d7660cdd961dc230da0cb3e632", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "328d9e880d321015876a82f6be8a672be3ad486fe0768faa662e03a4fcd64bca", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bcfea17a3613fd252e7d678d51af3948a", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "328d9e880d321015876a82f6be8a672be644b8258516350a4f29b5e0f0790fd6", "path": "setup-elevenlabs-mcp.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "328d9e880d321015876a82f6be8a672bba61717ff7508cbaf9f3ea47338b6e89", "path": "setup-magic-mcp.sh", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b2ae73c756ffe20814bb6bf860a66b58f", "name": "scripts", "path": "scripts", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b5f0ed182e352f774dcbbd4fd2fe2d58b", "path": "AlpacaService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf2e82c454da5daedc47a0c3cc34cd8de", "path": "APIConfiguration.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6fb8e5f0731a3258af6d84d8633fecdf", "path": "APITester.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6fe05bd20ed7d888d8c71db2f6b9786e", "path": "APITestService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672be2f053751ea672a214812cd1ccc13a71", "path": "AuthTestHelper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb3ade82b467a8fa0c91f1a9eb34b65e9", "path": "GeminiAIService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b1ddf86bde95862f01b3bf22e10cd1a84", "path": "MarketService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b131eed968a859538a91c0d5842e3f938", "path": "NewsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b1ac5e4fbfadd60929510b94827e8950a", "path": "PolygonStockService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bdb97a697ac292eb526e274fcee8a8cdb", "path": "SupabaseService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b935f79ec3a01b368211377bcd1ea8c97", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672ba06f0d1f67bc3eaf7aeb136b04925d66", "name": "Tests", "path": "Tests", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bacb465a782a8428bfce1b8990f6634f1", "path": "AnalyticsComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b143f54fc630d03727020e5cae15e226a", "path": "ChatComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b63dc988cf5f7cf78c137a46cfcc08a59", "path": "FeedComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b5b2b1086cc49163f7d9b96b49a63115d", "path": "OptimizedAsyncImage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b70e936f363a2c4d58dcdf135d6608dfc", "path": "PaymentComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b372bced897b853bb4df5053448c3843e", "path": "PortfolioComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672baa65045f337cea6c4e6a4c891eabcc43", "path": "QuestComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b962a88c70bf0bc0e76611847083c1389", "path": "RealTradingComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b56ab0952a6eea6a00b9e40a81ee9668d", "path": "SimulatorComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b96cddf7113dda67e9ca040939540de38", "path": "SquadComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b2128ced01341c2fbf8f433c134f95137", "path": "UIPolishComponents.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bf49b3df049ab780939a087e4056a5e2b", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7e6774ccfd74df3fdf4b51dc423c7c2d", "path": "AdvancedAnalyticsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b12c73df880ff78522ae20d90552e1fe5", "path": "AuthView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672baa0ef12aa976fb3ebcb073af077fe45b", "path": "BillingHistoryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9276c5dd4a984c120284428be5d8d1f8", "path": "BrokerageConnectionSheet.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b8f4fc45f33695ef3006d7ac65ccc6fc5", "path": "BuffettChatView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b578bbba252f163be8d35dff1b3398309", "path": "BuffettInspiredFeedView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc0208bbaf15b6681895541b8b1609e47", "path": "BuffettQuestsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc560d3101da7ab5fcf8723a87f384926", "path": "BuffettSimulatorView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba684b9a7c793235580888af3a7676d13", "path": "CreateInvestmentProposalView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b2dac938a71e4ea6580758ce80cb367d0", "path": "CreateSquadView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b2ffb80fbddf3527c3eb3aad9c885c22b", "path": "DashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b03edef5e03ca0b11a89ba80b31be326e", "path": "DeveloperSettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9cf0a224a7eafdcfae546adab7a5523f", "path": "DevelopmentDemoView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b969681e4f89f25928d944dd6b513ac01", "path": "EnhancedOnboardingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b0e60d1dcc4e9a0e7dc18863a2b6351cc", "path": "EnhancedSubscriptionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9544af00ef05aed23eb77f3a07086613", "path": "FeedFiltersView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b0e6eaadbdcfe14adb45ea12ee85b6337", "path": "MainTabView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3bd292b56f50cbaf99862facb2b52ec0", "path": "OnboardingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3cc5f33846e76f9c8eda5da9ef63896c", "path": "PerformanceMonitorView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba4f18e371b98b1f926078707354eddc3", "path": "PreferencesSetupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb69dd4247a2e48941b53b85a59949025", "path": "QuestCompletionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b125640a5edc6621bbcaca16f86de0fbe", "path": "QuestDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b4b5ac1ef39ba70ce8f4d2e3d25bef70f", "path": "RealTradingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3b8ca3abe32e35ce61b4cfe0346687ca", "path": "RevenueDashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b5c48d1b7781cd545ebf0a3fe8a4467c7", "path": "SimulatorTradeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6734864c67c271d8391b77cbba978598", "path": "SimulatorViews.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba96356f86e40df677818144e9b3bda74", "path": "SquadChatView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b058f89d56953e296e56faa24816b2b9f", "path": "SubscriptionManagementView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b2408a38c6dbbc6d09c04d78bef4799db", "path": "TestingView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b309f6ed18a5ec41f2fea3df53ea884a7", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "328d9e880d321015876a82f6be8a672b64cd7ae380038cdabcc74f1a3f1b7454", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb85e7e59e398e4ecaf376b881f2eb386", "path": "BuildTest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b92c020ca74e2a1935002329d5551c759", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "328d9e880d321015876a82f6be8a672b7913733cfce7131cf8cab061b085d128", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672b6bfefdb07f7f148a8d78ab3e21a35f82", "path": "package.json", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b6a92e8b8ac261932f1f41ed76b2c21c7", "path": "README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "328d9e880d321015876a82f6be8a672b0559fdd30e6d8ec9a40f47f7f7ab4198", "path": "VibeFinance.entitlements", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b174819ffb850f8d860bb4879ce2b659f", "path": "VibeFinanceApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b8d1c7c8a1e011d37bedab41300e35de6", "name": "VibeFinance", "path": "VibeFinance", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb355153be47c54dcf3a40c93cc8c68a7", "path": "VibeFinanceTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b51c43799e3887e54f271bfadcd92f5c4", "name": "VibeFinanceTests", "path": "VibeFinanceTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b0d562813cd81fd42e0e1c543022ef85a", "path": "VibeFinanceUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc0c72960635203a1bc9e62084755e8be", "path": "VibeFinanceUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b95439235a1d8a36bf0ce4ee32dd35c1b", "name": "VibeFinanceUITests", "path": "VibeFinanceUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b069c8f2583608d3cd9f06f1eea255401", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "328d9e880d321015876a82f6be8a672b30db233506c5381ba2ddc9729ffd1212", "name": "VibeFinance", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "328d9e880d321015876a82f6be8a672b", "path": "/Users/<USER>/Documents/VibeFinance/VibeFinance.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/VibeFinance", "targets": ["TARGET@v11_hash=87997608c907ddc4743865f7e9c765b0", "TARGET@v11_hash=e20383aa511d8b95461771a07fddbaef", "TARGET@v11_hash=8538120e266fcae0b1479d42d817c9bc"]}