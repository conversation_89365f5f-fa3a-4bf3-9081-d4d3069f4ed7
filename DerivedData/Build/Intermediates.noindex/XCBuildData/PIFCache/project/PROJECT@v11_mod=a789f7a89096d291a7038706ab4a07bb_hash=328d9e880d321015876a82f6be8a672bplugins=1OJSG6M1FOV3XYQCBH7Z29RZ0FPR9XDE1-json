{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "QNW477Q52S", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "328d9e880d321015876a82f6be8a672bcd43c4c5bf92c38e6a455c8649ddbfb5", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "QNW477Q52S", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "328d9e880d321015876a82f6be8a672b8a87e839cd2d2d9fb3dae8fb321ce5bc", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672babf337657352377e84d063e767b65080", "path": "mcp.json", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672b1491b289700a5104172b654cf4d001fa", "path": "mcp-with-keys.json", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672be6feea5d622bb2e628707fbd5fffe079", "name": ".cursor", "path": ".cursor", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b06364a9c4555ec452e24e356922f8f44", "path": "DevelopmentConfig.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672be0f9c3ee41f92b2259dd35933984dc16", "name": "Config", "path": "Config", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b11ff6fd4f498d62a4709fd09350782db", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "328d9e880d321015876a82f6be8a672bccd38348067c81eb802aeafd7390733d", "path": "fix_auth_issues.sql", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "328d9e880d321015876a82f6be8a672bf1b0828cec7eb29f1e726bcb501d6936", "path": "setup.sql", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b62aafad66696679565664bed930a10e8", "name": "Database", "path": "Database", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b3a7bf62f98262676ae9a1b1a729f3f89", "path": "ADVANCED_ANALYTICS_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b852d140308d2b9429e6cda037148672c", "path": "API_INTEGRATIONS.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bd9acedff1ace2d66f5289e413d555827", "path": "APPLE_INTELLIGENCE_REVAMP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b7b61856d661f4dc2f102fce8db76c4ee", "path": "BUILD_SUCCESS_FINAL.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b8515ff54d724102ca6089ad4732c82ee", "path": "DEVELOPMENT_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672baf248ec9494033ab492723f2cf74c5cf", "path": "FEATURES_OVERVIEW.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b804ad201929dc1e29d96890ae00e4a27", "path": "FINAL_IMPLEMENTATION_SUMMARY.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bc6650fcc83a54f0ef8c00078652eec40", "path": "IMPLEMENTATION_COMPLETE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b11615d4898d6645b2308b4d746f263bd", "path": "MAGIC_MCP_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b4a66a5bf96d4b6dbfda745126c156113", "path": "MCP_QUICK_REFERENCE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bd95d4786c7e2ae99f96b515fe41aa76f", "path": "MCP_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bd5b90ec82bc5da5a5f0fb400fd625637", "path": "MOCK_AUTH_GUIDE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bb0adaad272513a39bdf2c437a808fa45", "path": "PAYMENT_PROCESSING_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b74bc598dbcfa3ce4513202057a83c325", "path": "PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b16ffff4e0ca8399c7e73c81f8b35c1e6", "path": "PROJECT_STATUS.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672ba7da465ea3e079b5082524713aa60387", "path": "REAL_TRADING_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b6f720169515e0eb03c2b88c4a09f834f", "path": "SOLUTION_SUMMARY.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bdc0227ed7b1b08086549ccc0eae3e642", "path": "TECHNICAL_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bda0833bf0a6dfa0a1c8ace069f068450", "path": "USER_GUIDE.md", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b667ae1a25746895dd6cc2f084c815ba4", "name": "docs", "path": "docs", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b359385b33cab7039936c97dd4cfe941d", "name": "Intents", "path": "Intents", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9c3feffc2ed4b1cb76b435a34219e583", "path": "AnalyticsManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b8f73397aa5b71830378b29f13bd5968f", "path": "AuthManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf4c129458baac49e517feb85b993cfc0", "path": "CacheManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b73a5dd12a67acc155f951345d842a2f5", "path": "ChatManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b0bf1225bed4249d9888c5789a5b539a4", "path": "FeedManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b4b4642c93ee12e4f29f794982acf99ab", "path": "ImageCacheManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b8063d960b418138328eca6d95d5e38c9", "path": "NetworkOptimizer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b584e8477319fe36fa459b44f0113aff9", "path": "NotificationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b30cc776de0a3969b084a09dcf8cc976f", "path": "PaymentManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bd4d6e6e1da266db2157ed05c72c58dad", "path": "PerformanceManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b0f2ef156efb9e142485596721437c35c", "path": "QuestManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672befa41419f6c51f27a040c355ce83303f", "path": "RealTradingManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b8e6cec50f256589236bd30aeedd5ca37", "path": "SimulatorManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb0644451321d6c81119fe6ad6f83bd5c", "path": "SquadManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b805576d7a12351f52c1ab2e54eb120b9", "path": "SubscriptionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b11abeed4fe27d22af74f59cc03cc43ea", "path": "UserManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bbd3a1665a8f2699f895312ffa7fa0071", "name": "Managers", "path": "Managers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b00e85d69ce5df925a9bdd1478f174b37", "path": "AnalyticsModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba5341943fefac85da1cf7bf036f30e71", "path": "Chat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b11680d86a0b1707e5304e4c65c1b78c8", "path": "DataModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6531ee895a25f99d78a9fc1a97db1918", "path": "Feed.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bba5f55c08a705c248031fd53aa780588", "path": "PaymentModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b1f8e018f2f6425d0a9c223878069b55f", "path": "<PERSON>.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6561d01bc64bba2f9958bd77793384e3", "path": "RealTradingModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf582e704e34df8701f1ff70b57631eda", "path": "Simulator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bbb09df0e3ac309e6fc95e8d65d52a071", "path": "Squad.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bbfad8599a59bf93f16b99f7fdacc48da", "path": "User.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b1308588aa68605e6f78feb6f31fbf3a6", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "328d9e880d321015876a82f6be8a672b0f4f973acb556c5d1a10850201b0f984", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b494b8e8b1b9a7c16873b6a54c158edcc", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "328d9e880d321015876a82f6be8a672be3cda333e7f0049082d472db85151823", "path": "setup-elevenlabs-mcp.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "328d9e880d321015876a82f6be8a672b756a5f9fa466e57425e278529a6d43d1", "path": "setup-magic-mcp.sh", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b8b247e4bcf7dec07492c8f671d4df401", "name": "scripts", "path": "scripts", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b30ed17717ef7c0317c330ea1d4193b67", "path": "AlpacaService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b0e97b8dc2f44fc319e3fa980bfb559ff", "path": "APIConfiguration.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b647840cc1b0d27e8fc5fe3d0324f916d", "path": "APITester.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bd52e779cb323f3be81c2b232d18867a6", "path": "APITestService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf19af087da6fa4faf9fddc1aa133f30e", "path": "AuthTestHelper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b38b7c351e4b5475d4dd36edfec8530ad", "path": "GeminiAIService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b908c0efe3ea4baef59d9be859a3c3bc3", "path": "MarketService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf3c28de7f3d0508c9def0b3f0b51e8c8", "path": "NewsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bd361a4c91873d13d407afc25bfb935a3", "path": "PolygonStockService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b985d7c3003ea7127944f4cbd5ae12194", "path": "SupabaseService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b3da32e7c0a9ab296b24653454a5199f2", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b3be73ccd730f04377b3f8e6f9bf0ecca", "name": "Tests", "path": "Tests", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672be17226aa3984ed59f2b1b663e7e40a69", "path": "AnalyticsComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b562ebe1cd6e2d9991403851ffa353439", "path": "ChatComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bfd5d67d6a1ae9fa1442d54eeae2e745a", "path": "FeedComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b14a53fa890e5deb713338aa286953082", "path": "OptimizedAsyncImage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b2c82e31b46d7c93d00ae07497707d463", "path": "PaymentComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bff9c973e5e50ba2ebb177d98b8da4e5f", "path": "PortfolioComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b13bb7db4de385ca24f1a73cd10a3e223", "path": "QuestComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bfcc69c9912223961d361fcacdcb57707", "path": "RealTradingComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b0729962e71c3a207395d52c29e243373", "path": "SimulatorComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb1900b4c703aa2aea7326cbdd667dc5c", "path": "SquadComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf619da08fa7579a7a0e133c26eb07d6a", "path": "UIPolishComponents.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bd372a59e074c4bb1e6eddc7c8e78b96e", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b4d695ecc82fb69318bc13245c57f1548", "path": "AdvancedAnalyticsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc87dcc80b6c9bdf09bb477e172326b85", "path": "AuthView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7f475a73fc168feafb227ed8d7da7244", "path": "BillingHistoryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf88d49f441921fcb7d529ef313a2e92b", "path": "BrokerageConnectionSheet.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb4d99903761f18120d43ee5565092936", "path": "CreateInvestmentProposalView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b73e6e28760f3aaf127ae3a68cfa08ec2", "path": "CreateSquadView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9fee1c15093643688d1ddc8ed80b0b27", "path": "DeveloperSettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7120f81137cbb7511249886b62bb3762", "path": "DevelopmentDemoView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b84a7272d4d3085c41fa7cf82367e9cbf", "path": "EnhancedOnboardingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bd7dae879010529cc07ce96ee8791c1aa", "path": "EnhancedSubscriptionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b592a08d6d6bfc7de9dcd61f0ab035f06", "path": "FeedFiltersView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6c9d6162be485642186491b0300a4d82", "path": "MainTabView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba45662300dcf3a6a5cc2afffcc307b52", "path": "OnboardingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b986ebc304e9da5e7b2622346a9b57876", "path": "PerformanceMonitorView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b44c88c5b9f52f9c92f054f26a9ffe2a3", "path": "PreferencesSetupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3cf7b9d7019c069b0cda5f5163e16d0f", "path": "QuestCompletionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7269cfc86dc470d63dc6470d96ec6343", "path": "QuestDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bfc24b16a35799137520605923ad2e2ce", "path": "RealTradingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b91697589ebf8958eebf20d2aafcf49df", "path": "RevenueDashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b76d5513264efd6600fe5d1ff6a6bd257", "path": "SimulatorTradeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf02b3ada63698e06f1faa6cb47b92586", "path": "SimulatorViews.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6a41fdb6994d6fb7fa12c3f3de4c5c3c", "path": "SquadChatView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7b34a223a0a4ca8e59ea76b9b2a4afb4", "path": "SubscriptionManagementView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bfd4cda85f50cdac346fa57cc400cd559", "path": "TestingView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b257d13516791eb44123038aa5c6b968e", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "328d9e880d321015876a82f6be8a672ba7b2c1d2555a84450360822952bd2b85", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672be495a6284756d8d518efe23f3d063364", "path": "BuildTest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb159bad4bfc865854dce7eb9bfb843ea", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "328d9e880d321015876a82f6be8a672b1acd860de11566f1ae94140704d9ab8f", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672b942b8285c4f6f1a62248c33a4fe59798", "path": "package.json", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b9b996ab2b4becd8697c4acbc605c4d6b", "path": "README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "328d9e880d321015876a82f6be8a672bf69e86d19907b96f2d58354e7337c822", "path": "VibeFinance.entitlements", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bea027a53c103fbdb5dc165bc96f98f9d", "path": "VibeFinanceApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b8d1c7c8a1e011d37bedab41300e35de6", "name": "VibeFinance", "path": "VibeFinance", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9a28ffe5659b8c3b538c6c236c7018c4", "path": "VibeFinanceTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b51c43799e3887e54f271bfadcd92f5c4", "name": "VibeFinanceTests", "path": "VibeFinanceTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b638a1f48ccc03285470686624869a2e5", "path": "VibeFinanceUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf09aa01a6ec2984df37bade453b43a87", "path": "VibeFinanceUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b95439235a1d8a36bf0ce4ee32dd35c1b", "name": "VibeFinanceUITests", "path": "VibeFinanceUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b069c8f2583608d3cd9f06f1eea255401", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "328d9e880d321015876a82f6be8a672b30db233506c5381ba2ddc9729ffd1212", "name": "VibeFinance", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "328d9e880d321015876a82f6be8a672b", "path": "/Users/<USER>/Documents/VibeFinance/VibeFinance.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/VibeFinance", "targets": ["TARGET@v11_hash=e16ec92b44c4b83c8be00c2d26b1e816", "TARGET@v11_hash=eed838d686973e8049797cbfd7867e60", "TARGET@v11_hash=a7c0bbdc3a5df98f25a2602324bcd701"]}