{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "DEVELOPMENT_TEAM": "QNW477Q52S", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "328d9e880d321015876a82f6be8a672bcd43c4c5bf92c38e6a455c8649ddbfb5", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEVELOPMENT_TEAM": "QNW477Q52S", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "328d9e880d321015876a82f6be8a672b8a87e839cd2d2d9fb3dae8fb321ce5bc", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672bc12d14c023a1df77b8d23c14fa0c5fd3", "path": "mcp.json", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672b4ffb2b1611f79f8cb3a950499132c9cc", "path": "mcp-with-keys.json", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b82be64a31e037efaaa7085ece1ff9d36", "name": ".cursor", "path": ".cursor", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bff6563486e9eae6058ab054ec31de2ed", "path": "DevelopmentConfig.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b52d4a93177a7ada6d85973e5a6770644", "name": "Config", "path": "Config", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672bd3f8af4ff4aded5b400fe030ff8f211b", "name": "Core", "path": "Core", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "328d9e880d321015876a82f6be8a672b60008940b0ca887b574311782002a77b", "path": "fix_auth_issues.sql", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "328d9e880d321015876a82f6be8a672b5389983f8469078a2572f7f189f7fdbb", "path": "setup.sql", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bf45291540350d994d6d1db542c5bb373", "name": "Database", "path": "Database", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b555c251b387f901cf5aa066e57ad44f2", "path": "ADVANCED_ANALYTICS_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bfa1c34ceb0524069a6dce66d919d0839", "path": "API_INTEGRATIONS.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b714296e35542208cb61e903d4d4af747", "path": "APPLE_INTELLIGENCE_REVAMP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b1c5635aab44681d3cb0319d8c339a8b2", "path": "BUILD_SUCCESS_FINAL.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b674789caccf8b0d67ebb48707cc5c97e", "path": "DEVELOPMENT_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b4ef49ef64cd8fab7102cbc8ae288f0a3", "path": "FEATURES_OVERVIEW.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b2b0210154678565cf175f3d5ae01ddd7", "path": "FINAL_IMPLEMENTATION_SUMMARY.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b90a26e6560ea65762c873254a6a40bd9", "path": "IMPLEMENTATION_COMPLETE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bdffffd7ad91b0cb4815c55b0a228362d", "path": "MAGIC_MCP_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b693647f852185420981032736781f436", "path": "MCP_QUICK_REFERENCE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bf66db4dea7a27a5a0c0b221d83c8a42d", "path": "MCP_SETUP.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bc6cc88af0f5ac1499932e66248aa649a", "path": "MOCK_AUTH_GUIDE.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b1545900ea2666f04781460855f4d81e4", "path": "PAYMENT_PROCESSING_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b28c33cdbf92cf00fd4d0c88fe6bbe4e6", "path": "PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b9a1279ad9fe28c884929db4921d3cead", "path": "PROJECT_STATUS.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b13146bdc76d4da1b32aeed00d0a37f3c", "path": "REAL_TRADING_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b23e192bf2bbbed84860f8f9c3ad0bb2f", "path": "SOLUTION_SUMMARY.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b23578fa184036a2922abec479ed974e0", "path": "TECHNICAL_IMPLEMENTATION.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672bfec91fa258fc30e9b0919dcd37a3802f", "path": "USER_GUIDE.md", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672bad74d98bca898fdcf2e3876015f661d8", "name": "docs", "path": "docs", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b77767be1bfe4a445897ed47e89ef3269", "name": "Intents", "path": "Intents", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b562016a36b00da603bd03a613bd304ae", "path": "AnalyticsManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b931d334f922232a587dc73496a792b6f", "path": "AuthManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9264ce1f60b595241fa5f1a5b143745f", "path": "CacheManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b94a9be4a25585d774381842ec9c7c512", "path": "ChatManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bdba7dd019ffe0f775d4e335c68c40ff5", "path": "FeedManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bcb27dc1e81d388ba070b8d8c2564289f", "path": "ImageCacheManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672be3d7871192d960b22506deab770561fe", "path": "NetworkOptimizer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc50077399b0f007b9b8f0b2377efc327", "path": "NotificationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b59948272c6ae75c3cf2a821a36c53388", "path": "PaymentManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf78b3bfd80fc4ad8f45ba0bcf7d9bd46", "path": "PerformanceManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b718d6cda518ef66bc37295ba5b4f6fbd", "path": "QuestManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb03c0c90659aa5e3a9375fb0beccbdd4", "path": "RealTradingManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7036b897976cc9c3315e936712be945c", "path": "SimulatorManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb2b47902ce3470388d5a6a436d076cb4", "path": "SquadManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bfa28e33eb64f0ab55ef689145f0630fd", "path": "SubscriptionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bd5d9be9dee8c43bcd7e0868edc21c652", "path": "UserManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b82f402d1ae4779428c69cc4943e50457", "name": "Managers", "path": "Managers", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b553aebe4dfd63f159d8d2ecff618c66e", "path": "AnalyticsModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf5d799692a19ebb87ff068706430399c", "path": "Chat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b1040ed9fb85b5954bb90da86e2d29b3f", "path": "DataModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bbdc4d3b20c2b7c00890872d0795ac584", "path": "Feed.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b08c3ee265be18850ca906d3c3722a3ec", "path": "PaymentModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b0b53cf1601003b63f40a888affb8c02f", "path": "<PERSON>.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bbd437dc37131b4843c09217a83dffbf1", "path": "RealTradingModels.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b44f988fa34457e7fba4f77fa700bec7b", "path": "Simulator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf4b9924860f4ee7f7fa1398041195d8e", "path": "Squad.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b886ee1b7e3216a3df098ea22843f0b8f", "path": "User.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b0e1f0c239403d43c3fee5391fee767c6", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "folder.assetcatalog", "guid": "328d9e880d321015876a82f6be8a672b8ca846b622a227345fe7b58a36dd1226", "path": "Preview Assets.xcassets", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672baa1a169ca3f3801a73e36fee4f26536c", "name": "Preview Content", "path": "Preview Content", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.sh", "guid": "328d9e880d321015876a82f6be8a672b0612aa006fa08bce93b3b8430752fe7e", "path": "setup-elevenlabs-mcp.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "328d9e880d321015876a82f6be8a672b2b2885e36bd4bdf5590052dbedd34cd2", "path": "setup-magic-mcp.sh", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b87aa85174f0c90f81a6d49bebb7ba85f", "name": "scripts", "path": "scripts", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b609a3e37d7d1872b62da085127bf78ad", "path": "AlpacaService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bff39089f41b214aaea0bb4c3460b8d7b", "path": "APIConfiguration.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3cb82b48360bec965a9ae1acb1dec95e", "path": "APITester.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bbb61be018a659c2dcbf19b94ff6c64c1", "path": "APITestService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b8b79c6e45d40075dc1292934d3ca63f4", "path": "AuthTestHelper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b21527251560a0cb00db17b9f4692f62f", "path": "GeminiAIService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b716d2ed06471be7f5f96497e2633408d", "path": "MarketService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf5878f891c9915a6f6013a7ea7b45055", "path": "NewsService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b74fbfb3965af44e15b833330df821443", "path": "PolygonStockService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7d868e9c97187784c69413aa7f6d9590", "path": "SupabaseService.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b3eee238345eab7d2dc8802dfa9666655", "name": "Services", "path": "Services", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b4dcf3cf2dbf7037b7a28bd52a90cc9d7", "name": "Tests", "path": "Tests", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bceafe4f98faba718ff05824d280b19d8", "path": "AnalyticsComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672baff11aa391f493e0fe98f5a63c1f8cfc", "path": "ChatComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bdfbd4999c1a4d5db59325cb3385e4bac", "path": "FeedComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb3c2f3e804a5d250311981fc7ed2a9df", "path": "OptimizedAsyncImage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb1197dd68420c0d9d5953636ad0dcc4a", "path": "PaymentComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bcc3f7f80046b55caef977f9d2a27f171", "path": "PortfolioComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b5b1b4fb62f6657eea003c548cf154f21", "path": "QuestComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb73130ffd976f8ac38c6acf3137e33aa", "path": "RealTradingComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b538119a531f6378ad37219208cfe8218", "path": "SimulatorComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc5ac15ad656bc946fcc3dec3367f43d9", "path": "SquadComponents.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b161fba4d1620e859271c8ac33b1fe50a", "path": "UIPolishComponents.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672be85ade6e6c489bee8c65834a8511ce28", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bb93d01fdc469ab1888b5ed73a5f9e842", "path": "AdvancedAnalyticsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9ab1346326d903cbdab8b2cceaf3070d", "path": "AuthView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3313e25ac4b6205de4cffa612907df93", "path": "BillingHistoryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf9ce737126507d668cd18fdbb92f3782", "path": "BrokerageConnectionSheet.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b7f62bb15fb88c4d914098900607a70af", "path": "BuffettChatView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b5b356d4d68d39a60d947c7708e15486d", "path": "BuffettInspiredFeedView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf4ba9a63dd57715b800b463b6813b8b2", "path": "BuffettQuestsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b2ff4bd95fab2dec619f525bd4d51e673", "path": "BuffettSimulatorView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b73967f95080af377198a0b5f13778a8b", "path": "CreateInvestmentProposalView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b5cca8dd7d32c6798c76bc2a625a3001d", "path": "CreateSquadView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba57e1cf321b8bbc6321f5a135a6698c5", "path": "DashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bde764c364af84e752e2f7a3b9a77bb24", "path": "DeveloperSettingsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9f882287b2d411f99162d1d819dbeab7", "path": "DevelopmentDemoView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bc57814d13219ed184949689c324f47b9", "path": "EnhancedOnboardingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b1cbc50fdd1c59e9f7e84af51237db8df", "path": "EnhancedSubscriptionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bdce56c4b84b78a6164165411b38a8025", "path": "FeedFiltersView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bf5d35a519bc06bff28b17a21624b7bf1", "path": "MainTabView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b28276170dd67952158bf8934a40010e6", "path": "OnboardingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b65f8d611aa430288b559b84f85125379", "path": "PerformanceMonitorView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bbdc0b7695f7e04ea3cbab03748083d98", "path": "PreferencesSetupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b8aa154e71b1420ca1cf8ac954bfb55f9", "path": "QuestCompletionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b245857a856a0e427448f7b4f57d9f366", "path": "QuestDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bfb3892edf2b3643444ae490488e7ab71", "path": "RealTradingView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b6c17eb2f4c7647e264f90e9704885eea", "path": "RevenueDashboardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b9fb0b3e151ee99153b70accff842b063", "path": "SimulatorTradeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba050004c1bbf9a612b422332ddd3d88d", "path": "SimulatorViews.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672ba97772472029c381a6db4d40c2d94f74", "path": "SquadChatView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b1cc38d60659877be4823aae4d58287b2", "path": "SubscriptionManagementView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b373ddae612f2b9da3fc46419c23b18e3", "path": "TestingView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672ba1f6004b484be6f71ccd2ceb7864b498", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "folder.assetcatalog", "guid": "328d9e880d321015876a82f6be8a672b01cb7a2e2c2aa0c7b846fc86b7832831", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b3f0cd3b0571d784ee79b99a90bed0f2a", "path": "BuildTest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b2dc5dd762d8cb4f084bd3a00e49fee4c", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "328d9e880d321015876a82f6be8a672b2cfdab0690a0c3341a498f982ea14b58", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.json", "guid": "328d9e880d321015876a82f6be8a672b38bea58e3565446965c0e989caa41a6f", "path": "package.json", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "328d9e880d321015876a82f6be8a672b0a7bf1446cdbdd27bd0492d722710840", "path": "README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.entitlements", "guid": "328d9e880d321015876a82f6be8a672b4eeb30077e4db839ba39ee7621691a1e", "path": "VibeFinance.entitlements", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672b0c058c6a23b5c7df20d9bb5b65ba112f", "path": "VibeFinanceApp.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b8d1c7c8a1e011d37bedab41300e35de6", "name": "VibeFinance", "path": "VibeFinance", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672be2f4aca9195a71083249981156a501e2", "path": "VibeFinanceTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b51c43799e3887e54f271bfadcd92f5c4", "name": "VibeFinanceTests", "path": "VibeFinanceTests", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bbcade847237e378495ca608df10de6e2", "path": "VibeFinanceUITests.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "328d9e880d321015876a82f6be8a672bfa8d8a0b9a67a43db4d86c36c0b61cbb", "path": "VibeFinanceUITestsLaunchTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "328d9e880d321015876a82f6be8a672b95439235a1d8a36bf0ce4ee32dd35c1b", "name": "VibeFinanceUITests", "path": "VibeFinanceUITests", "sourceTree": "<group>", "type": "group"}, {"guid": "328d9e880d321015876a82f6be8a672b069c8f2583608d3cd9f06f1eea255401", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "328d9e880d321015876a82f6be8a672b30db233506c5381ba2ddc9729ffd1212", "name": "VibeFinance", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "328d9e880d321015876a82f6be8a672b", "path": "/Users/<USER>/Documents/VibeFinance/VibeFinance.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/VibeFinance", "targets": ["TARGET@v11_hash=4715b8a72bdd2e600513dcde01859cbc", "TARGET@v11_hash=0ba92d5dff679e729a2663e17bb7a2b3", "TARGET@v11_hash=6b9aedd5897d391908acd57a711b3008"]}