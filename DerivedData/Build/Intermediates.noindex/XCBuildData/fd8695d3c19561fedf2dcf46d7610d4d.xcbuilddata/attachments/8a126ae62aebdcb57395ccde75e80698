{"": {"diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-master.dia", "emit-module-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinance-master.swiftdeps"}, "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/BuildTest.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BuildTest~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Config/DevelopmentConfig.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentConfig~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/ContentView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ContentView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AnalyticsManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AuthManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/CacheManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CacheManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ChatManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/FeedManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ImageCacheManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ImageCacheManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NetworkOptimizer.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NetworkOptimizer~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NotificationManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NotificationManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PaymentManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PerformanceManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/QuestManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/RealTradingManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SimulatorManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SquadManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SubscriptionManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/UserManager.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UserManager~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/AnalyticsModels.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsModels~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Chat.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Chat~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/DataModels.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DataModels~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Feed.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Feed~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/PaymentModels.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentModels~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Quest.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Quest~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/RealTradingModels.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingModels~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Simulator.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Simulator~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Squad.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/Squad~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/User.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/User~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APIConfiguration.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APIConfiguration~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITestService.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITestService~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITester.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/APITester~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AlpacaService.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AlpacaService~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AuthTestHelper.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthTestHelper~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/GeminiAIService.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/GeminiAIService~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/MarketService.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MarketService~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/NewsService.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/NewsService~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/PolygonStockService.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PolygonStockService~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/SupabaseService.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SupabaseService~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinanceApp.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/VibeFinanceApp~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AdvancedAnalyticsView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AdvancedAnalyticsView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AuthView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AuthView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BillingHistoryView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BillingHistoryView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BrokerageConnectionSheet.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/BrokerageConnectionSheet~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/AnalyticsComponents.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/AnalyticsComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/ChatComponents.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/ChatComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/FeedComponents.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/OptimizedAsyncImage.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OptimizedAsyncImage~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PaymentComponents.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PaymentComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PortfolioComponents.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PortfolioComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/QuestComponents.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/RealTradingComponents.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SimulatorComponents.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SquadComponents.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/UIPolishComponents.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/UIPolishComponents~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateInvestmentProposalView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateInvestmentProposalView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateSquadView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/CreateSquadView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DeveloperSettingsView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DeveloperSettingsView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DevelopmentDemoView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/DevelopmentDemoView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedOnboardingView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedOnboardingView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedSubscriptionView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/EnhancedSubscriptionView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/FeedFiltersView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/FeedFiltersView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/MainTabView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/MainTabView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/OnboardingView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/OnboardingView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PerformanceMonitorView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PerformanceMonitorView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PreferencesSetupView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/PreferencesSetupView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestCompletionView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestCompletionView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestDetailView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/QuestDetailView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RealTradingView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RealTradingView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RevenueDashboardView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/RevenueDashboardView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorTradeView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorTradeView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorViews.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SimulatorViews~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SquadChatView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SquadChatView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SubscriptionManagementView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/SubscriptionManagementView~partial.swiftmodule"}, "/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/TestingView.swift": {"const-values": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.d", "diagnostics": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.dia", "index-unit-output-path": "/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.o", "llvm-bc": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.bc", "object": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.o", "swift-dependencies": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/Objects-normal/arm64/TestingView~partial.swiftmodule"}}