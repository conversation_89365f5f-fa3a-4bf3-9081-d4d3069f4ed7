{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "328d9e880d321015876a82f6be8a672bc87c97ee76012153448311260fe6f91f"}], "containerPath": "/Users/<USER>/Documents/VibeFinance/VibeFinance.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.5", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/Documents/VibeFinance/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Documents/VibeFinance/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Documents/VibeFinance/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone17,3", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "18.5", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone17,3", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "CLANG_COVERAGE_MAPPING": "YES", "CLANG_PROFILE_DATA_DIRECTORY": "/Users/<USER>/Documents/VibeFinance/DerivedData/Build/ProfileData", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "108", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES", "TARGET_DEVICE_IDENTIFIER": "65620888-F275-434A-B88C-638A151CC980", "TARGET_DEVICE_MODEL": "iPhone17,3", "TARGET_DEVICE_OS_VERSION": "18.5", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}