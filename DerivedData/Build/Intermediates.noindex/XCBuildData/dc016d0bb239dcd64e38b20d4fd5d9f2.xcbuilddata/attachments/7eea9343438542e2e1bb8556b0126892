/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/DataModels.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/VibeFinanceApp.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/QuestComponents.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettChatView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/OnboardingView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITestService.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PerformanceManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SquadChatView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Chat.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PortfolioComponents.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BrokerageConnectionSheet.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/MainTabView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APITester.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/PaymentManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DevelopmentDemoView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorViews.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/AnalyticsModels.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/PaymentComponents.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/User.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BillingHistoryView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/FeedFiltersView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/APIConfiguration.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NotificationManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DeveloperSettingsView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SimulatorTradeView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/OptimizedAsyncImage.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Squad.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AuthView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedSubscriptionView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/NetworkOptimizer.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/DashboardView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RevenueDashboardView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/SupabaseService.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AnalyticsManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/UserManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/FeedComponents.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Simulator.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/AdvancedAnalyticsView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/EnhancedOnboardingView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AlpacaService.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ImageCacheManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateSquadView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/RealTradingView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/PolygonStockService.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SubscriptionManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/ContentView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/ChatComponents.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/RealTradingModels.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/UIPolishComponents.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/FeedManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/CreateInvestmentProposalView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestDetailView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/NewsService.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SquadManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/BuildTest.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/AnalyticsComponents.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Quest.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SquadComponents.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/ChatManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettSimulatorView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/QuestCompletionView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/MarketService.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/SimulatorManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/PaymentModels.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/SimulatorComponents.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/CacheManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettQuestsView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PreferencesSetupView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/GeminiAIService.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/RealTradingManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Config/DevelopmentConfig.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/TestingView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Models/Feed.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/Components/RealTradingComponents.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/AuthManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/BuffettInspiredFeedView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/PerformanceMonitorView.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Services/AuthTestHelper.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Managers/QuestManager.swift
/Users/<USER>/Documents/VibeFinance/VibeFinance/Views/SubscriptionManagementView.swift
/Users/<USER>/Documents/VibeFinance/DerivedData/Build/Intermediates.noindex/VibeFinance.build/Debug-iphonesimulator/VibeFinance.build/DerivedSources/GeneratedAssetSymbols.swift
