<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ADVANCED_ANALYTICS_IMPLEMENTATION.md</key>
		<data>
		Xl/0nnHevmkFtGtQFkninedrCd8=
		</data>
		<key>API_INTEGRATIONS.md</key>
		<data>
		qgCvpMn3dEyCQmNf1z8FogJ0Wag=
		</data>
		<key>APPLE_INTELLIGENCE_REVAMP.md</key>
		<data>
		J7Gxt8t/atkBVDIx8oFVGo95dBw=
		</data>
		<key>BUILD_SUCCESS_FINAL.md</key>
		<data>
		q8OSGACTLfYouWbyfoAfK2qvRoQ=
		</data>
		<key>DEVELOPMENT_SETUP.md</key>
		<data>
		PsGYaxhtc5pArLDitvHTU92MAF8=
		</data>
		<key>FEATURES_OVERVIEW.md</key>
		<data>
		Qyk1yc2fcr7gBgY1luN21NYrh3Y=
		</data>
		<key>FINAL_IMPLEMENTATION_SUMMARY.md</key>
		<data>
		Q/dwvARmVCwkB9n000tpXk9UVDo=
		</data>
		<key>IMPLEMENTATION_COMPLETE.md</key>
		<data>
		Jj/0T61wn7KX8HnWATXiC7abl/Y=
		</data>
		<key>Info.plist</key>
		<data>
		+9K8cT1dCRoT+BTUatsFacEIxuM=
		</data>
		<key>MAGIC_MCP_SETUP.md</key>
		<data>
		6mKfTSHU17BtD54+LhYZWAkB9bY=
		</data>
		<key>MCP_QUICK_REFERENCE.md</key>
		<data>
		gyIKTcn4ZXNs/p3DcoZ7eiaRnHc=
		</data>
		<key>MCP_SETUP.md</key>
		<data>
		8I+uze9utaimJDrYmqYtksUDRoU=
		</data>
		<key>MOCK_AUTH_GUIDE.md</key>
		<data>
		YsPbsek8s1puCl3cdB2/82ZjEwU=
		</data>
		<key>PAYMENT_PROCESSING_IMPLEMENTATION.md</key>
		<data>
		OxntEIwYiWH/ZTfb+XSaGkZO+HY=
		</data>
		<key>PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md</key>
		<data>
		8ouVLL0obS45fF9SWjN3BFEZn4s=
		</data>
		<key>PROJECT_STATUS.md</key>
		<data>
		9FQjX0WMYRlJcdvkySwW3ixscIY=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>README.md</key>
		<data>
		kNUY0XMzsUbUHhdmQ7ZOIxLVtjI=
		</data>
		<key>REAL_TRADING_IMPLEMENTATION.md</key>
		<data>
		RfRVZEI7wdy15dzgFW2GPrkcytM=
		</data>
		<key>SOLUTION_SUMMARY.md</key>
		<data>
		CslTljakGBtVcoAGUnioP9+LYj4=
		</data>
		<key>TECHNICAL_IMPLEMENTATION.md</key>
		<data>
		CFfJdb35QToib2SrqGs1Xj6Eb6Y=
		</data>
		<key>USER_GUIDE.md</key>
		<data>
		Nnc2Efvc4oAlEWv2GApgT2bjgKk=
		</data>
		<key>VibeFinance.debug.dylib</key>
		<data>
		CXjdedoyQu+kmkdvbr6jAN6UR0E=
		</data>
		<key>__preview.dylib</key>
		<data>
		ODsDwkcTavWx8qoHHe47uSEg71U=
		</data>
		<key>fix_auth_issues.sql</key>
		<data>
		Fqbyxf9/+rbDAyMEHmAgbOhtcXc=
		</data>
		<key>mcp-with-keys.json</key>
		<data>
		p2r93qKyhwA/nhzMBZ734YUdXKA=
		</data>
		<key>mcp.json</key>
		<data>
		4fdRpEcPEdzNwemegeOYllkPAaU=
		</data>
		<key>package.json</key>
		<data>
		XUbqLbwc7E5pGvg7P8mGSzGSSnY=
		</data>
		<key>setup-elevenlabs-mcp.sh</key>
		<data>
		1InHDh4r1E9fvuPiLDCQlsHiOzo=
		</data>
		<key>setup-magic-mcp.sh</key>
		<data>
		dYSlNohsVs4AY5MngUjWuvsMgwg=
		</data>
		<key>setup.sql</key>
		<data>
		9PRkb5RB750iYAcx6kkzbeEkclE=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ADVANCED_ANALYTICS_IMPLEMENTATION.md</key>
		<dict>
			<key>hash2</key>
			<data>
			UjQhDt8fbrDOlRh8gV75Snvbk20rluiKxJiKg+VnqYU=
			</data>
		</dict>
		<key>API_INTEGRATIONS.md</key>
		<dict>
			<key>hash2</key>
			<data>
			n179CVd7Bs8UrVpvJoMNMIcd1HL9HgPV+NAQjQRoY4Y=
			</data>
		</dict>
		<key>APPLE_INTELLIGENCE_REVAMP.md</key>
		<dict>
			<key>hash2</key>
			<data>
			OHe08NLvssyI6x3o5wcWJiAgwsG6Nk992lCEN8sZCx0=
			</data>
		</dict>
		<key>BUILD_SUCCESS_FINAL.md</key>
		<dict>
			<key>hash2</key>
			<data>
			4rZdNdCM2OUdRaTpV8E9xiiF7VD7ncvVix0XvJfgKL8=
			</data>
		</dict>
		<key>DEVELOPMENT_SETUP.md</key>
		<dict>
			<key>hash2</key>
			<data>
			C+UYhj0DDnf01VvLo4YcjB2kQX8t8+0UiRLC12yF6O0=
			</data>
		</dict>
		<key>FEATURES_OVERVIEW.md</key>
		<dict>
			<key>hash2</key>
			<data>
			qUz3hytpKcJE8jKHv1Vj4jY7wVUQkixD5Za9T5+RHZg=
			</data>
		</dict>
		<key>FINAL_IMPLEMENTATION_SUMMARY.md</key>
		<dict>
			<key>hash2</key>
			<data>
			Rfd+VLnd+QJapZC2IsnPhh2huf2ex5+UMkX7L3pe/ds=
			</data>
		</dict>
		<key>IMPLEMENTATION_COMPLETE.md</key>
		<dict>
			<key>hash2</key>
			<data>
			ZM7g9dfsi05XEjXVEmzAc3OY003ZCrfvdCeJdgr6heg=
			</data>
		</dict>
		<key>MAGIC_MCP_SETUP.md</key>
		<dict>
			<key>hash2</key>
			<data>
			RtayE1+oxWGcJ0kCON+4oRtznYwtld5wuMG+Ni7RANo=
			</data>
		</dict>
		<key>MCP_QUICK_REFERENCE.md</key>
		<dict>
			<key>hash2</key>
			<data>
			Egj8jksH+8QOcYQ30OFa8hCTkdaY4jOeIOBw/KsNcHw=
			</data>
		</dict>
		<key>MCP_SETUP.md</key>
		<dict>
			<key>hash2</key>
			<data>
			D+0ixy299AqGCTLCn9gqSN+j5wlJtmC3YDboSX5piKU=
			</data>
		</dict>
		<key>MOCK_AUTH_GUIDE.md</key>
		<dict>
			<key>hash2</key>
			<data>
			8lGZL3C+FnnefCp3ghFozVVlHn4eBXRRafUZnhtD9cM=
			</data>
		</dict>
		<key>PAYMENT_PROCESSING_IMPLEMENTATION.md</key>
		<dict>
			<key>hash2</key>
			<data>
			ljE6fbWFYR9H7rXdHFHLHRxBgNNmX9LkwUfJ1xnqeG8=
			</data>
		</dict>
		<key>PERFORMANCE_OPTIMIZATION_IMPLEMENTATION.md</key>
		<dict>
			<key>hash2</key>
			<data>
			M6PjTaVsQpiTgHUuZwmI5wHjY0dP5EbcP9iBl9nta9g=
			</data>
		</dict>
		<key>PROJECT_STATUS.md</key>
		<dict>
			<key>hash2</key>
			<data>
			fHbbnYvxNDCBR4qed00cA3iICQ5QmgiuqiCGa/SJzro=
			</data>
		</dict>
		<key>README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			M0ypJrjP+UNM4DMjskOH1fLzfZCwDgTcQoYvip1kvJQ=
			</data>
		</dict>
		<key>REAL_TRADING_IMPLEMENTATION.md</key>
		<dict>
			<key>hash2</key>
			<data>
			hGayxn6mjQ5NJMW/ZqidtUP0l/x+vESwccTDPT8Akyo=
			</data>
		</dict>
		<key>SOLUTION_SUMMARY.md</key>
		<dict>
			<key>hash2</key>
			<data>
			N4qjBdlTpIfBp8VCrRA6Cd82wSjwWMFyPf8mbbjzVHA=
			</data>
		</dict>
		<key>TECHNICAL_IMPLEMENTATION.md</key>
		<dict>
			<key>hash2</key>
			<data>
			L+kn+RIV2NgrIJ0bnHk2x5k94E69T6U9UuDhD0fw4kw=
			</data>
		</dict>
		<key>USER_GUIDE.md</key>
		<dict>
			<key>hash2</key>
			<data>
			MoN2dRjNq8ZTwAVpgd1P5Q/6UxD5iM1lp+nBLO7iFgs=
			</data>
		</dict>
		<key>VibeFinance.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			vt+Yof+O85z3ufMbIT2MoS/rZG3QtF/3ipN7akZtbZM=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			87N+B9lf7c7ThYpnydV/QhAbitoZ6wNtzekARDngnEs=
			</data>
		</dict>
		<key>fix_auth_issues.sql</key>
		<dict>
			<key>hash2</key>
			<data>
			a5HMJI+OF4gMpPg5AmF6SKDRp6XpIptOEoIT1YGwIak=
			</data>
		</dict>
		<key>mcp-with-keys.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8ZPsG8k+q7f0+x/yd4SKq6YB0p8Q5S64DkW9rnV8Lgg=
			</data>
		</dict>
		<key>mcp.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uUJB7aoILZIcsfnw9u3tV6SvdKVVD9rSsyQvlepm8OA=
			</data>
		</dict>
		<key>package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PIwUh+G7Y/Ci2vBECHa7tYBNqsAzou4zXhh5DxOLzVk=
			</data>
		</dict>
		<key>setup-elevenlabs-mcp.sh</key>
		<dict>
			<key>hash2</key>
			<data>
			NR1nespwB/fYP3GZmH+xFIHr+rU9ubLyUQtfLLePe5k=
			</data>
		</dict>
		<key>setup-magic-mcp.sh</key>
		<dict>
			<key>hash2</key>
			<data>
			/gOYkBN31qy68ZYcqH7PGyboetDqKuyRoKrLW7mDA5M=
			</data>
		</dict>
		<key>setup.sql</key>
		<dict>
			<key>hash2</key>
			<data>
			BjDasIKO5bDGT0Op1Qd8gGLX1VD76B5eqGkmWLpkchk=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
